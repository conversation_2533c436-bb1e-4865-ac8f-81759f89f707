part of 'market_chart_cubit.dart';

class MarketChartState {
  MarketChartState({
    required this.indexCode,
    required this.refPrice,
    this.chartData,
  });

  final List<ChartData>? chartData;

  final IndexCode indexCode;

  final double refPrice;

  MarketChartState copyWith({
    List<ChartData>? chartData,
    IndexCode? indexCode,
    double? refPrice,
  }) {
    return MarketChartState(
      refPrice: refPrice ?? this.refPrice,
      chartData: chartData ?? this.chartData,
      indexCode: indexCode ?? this.indexCode,
    );
  }
}
