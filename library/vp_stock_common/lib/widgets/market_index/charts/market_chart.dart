import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/widgets/intraday_chart/widgets/intraday_chart_tooltip_view.dart';

import 'market_chart_cubit.dart';

class MarketChart extends StatelessWidget {
  const MarketChart._({
    required this.indexCode,
    required this.referencePrice,
    this.chartData,
    this.xAxisInterval,
    this.xAxisVisible = false,
    this.showVolume = false,
    this.primaryYAxisVisible = false,
    this.primaryXAxisFormatter,
    this.shouldRefreshData = false,
  });

  factory MarketChart.data({
    required List<ChartData> chartData,
    required IndexCode indexCode,
    required double referencePrice,
    bool showVolume = false,
    bool primaryYAxisVisible = false,
    bool xAxisVisible = false,
    double? xAxisInterval,
    String Function(num value)? primaryXAxisFormatter,
  }) => MarketChart._(
    chartData: chartData,
    indexCode: indexCode,
    referencePrice: referencePrice,
    showVolume: showVolume,
    xAxisVisible: xAxisVisible,
    xAxisInterval: xAxisInterval,
    primaryYAxisVisible: primaryYAxisVisible,
    primaryXAxisFormatter: primaryXAxisFormatter,
    shouldRefreshData: false,
  );

  factory MarketChart.temp({
    required IndexCode indexCode,
    required double referencePrice,
    List<ChartData>? tempData,
    bool showVolume = false,
    bool primaryYAxisVisible = false,
    bool xAxisVisible = false,
    double? xAxisInterval,
    String Function(num value)? primaryXAxisFormatter,
  }) => MarketChart._(
    chartData: tempData ?? const [],
    indexCode: indexCode,
    referencePrice: referencePrice,
    showVolume: showVolume,
    xAxisVisible: xAxisVisible,
    xAxisInterval: xAxisInterval,
    primaryYAxisVisible: primaryYAxisVisible,
    primaryXAxisFormatter: primaryXAxisFormatter,
    shouldRefreshData: true,
  );

  final List<ChartData>? chartData;

  final IndexCode indexCode;

  final double referencePrice;

  final bool showVolume;

  final bool primaryYAxisVisible;

  final bool xAxisVisible;

  final double? xAxisInterval;

  final String Function(num value)? primaryXAxisFormatter;

  final bool shouldRefreshData;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<MarketChartBloc>(
      create:
          (_) => MarketChartBloc(
            chartData: chartData,
            indexCode: indexCode,
            refPrice: referencePrice,
            shouldRefreshData: shouldRefreshData,
          )..loadData(),
      child: BlocBuilder<MarketChartBloc, MarketChartState>(
        builder: (context, state) {
          return IntradayChartView(
            minY: -20,
            maxY: 120,
            xAxisInterval: xAxisInterval,
            xAxisVisible: xAxisVisible,
            primaryXAxisFormatter: primaryXAxisFormatter,
            minutesPerPoint: 1,
            showVolume: showVolume,
            primaryYAxisVisible: primaryYAxisVisible,
            chartData: state.chartData ?? const [],
            refPrice: referencePrice,
            tooltipBuilder:
                (data) => IntradayChartMarketTooltipView(
                  data: data,
                  referencePrice: referencePrice,
                ),
            primaryYAxisFormatter: (value) => value.getIndexFormatted(),
          );
        },
      ),
    );
  }
}
