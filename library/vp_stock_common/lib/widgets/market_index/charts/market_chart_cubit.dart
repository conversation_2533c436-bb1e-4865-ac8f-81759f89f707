import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/widgets/market_index/charts/market_price_chart_extensions.dart';

part 'market_chart_state.dart';

class MarketChartBloc extends AppCubit<MarketChartState> {
  MarketChartBloc({
    required IndexCode indexCode,
    required double refPrice,
    List<ChartData>? chartData,
    this.shouldRefreshData = false,
  }) : super(
         MarketChartState(
           chartData: chartData,
           indexCode: indexCode,
           refPrice: refPrice,
         ),
       );

  final bool shouldRefreshData;

  final repository = GetIt.instance<StockCommonRepository>();

  MarketIntradayChartHelper? intradayChartHelper;

  Future _loadData() async {
    if (shouldRefreshData) {
      try {
        final data = await repository.getMarketInfo(state.indexCode);

        emit(state.copyWith(chartData: data?.index.toChartDataList()));
      } catch (e, stackTrace) {
        debugPrintStack(stackTrace: stackTrace);
      }
    }
  }

  void loadData() async {
    try {
      await _loadData();

      intradayChartHelper?.dispose();

      intradayChartHelper =
          MarketIntradayChartHelper(
              indexCode: state.indexCode,
              refPrice: state.refPrice,
              duration: const Duration(minutes: 1),
              onNewData: (data) {
                emit(state.copyWith(chartData: data));
              },
            )
            ..setData(state.chartData ?? const [])
            ..subscribe();
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  @override
  Future<void> close() {
    intradayChartHelper?.dispose();
    return super.close();
  }
}
