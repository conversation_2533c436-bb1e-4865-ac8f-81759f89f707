import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class MarketIndexNoChartShimmerItemView extends StatelessWidget {
  const MarketIndexNoChartShimmerItemView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: theme.colorScheme.background,
        borderRadius: BorderRadius.circular(8),
      ),
      child: _shimmerTextLine(height: 64),
    );
  }

  Widget _shimmerTextLine({required double height}) {
    return Shimmer.fromColors(
      baseColor: themeData.skeletonBase,
      highlightColor: themeData.skeletonHighLight,
      child: Container(
        height: height,
        width: 115,
        decoration: BoxDecoration(
          color: themeData.highlightBg,
          borderRadius: BorderRadius.circular(3),
        ),
      ),
    );
  }
}
