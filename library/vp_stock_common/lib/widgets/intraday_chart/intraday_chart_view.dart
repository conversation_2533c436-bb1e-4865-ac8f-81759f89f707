import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:vp_common/utils/format_utils.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_stock_common/widgets/intraday_chart/widgets/intraday_chart_tooltip_view.dart';

final upColor = ChartColorUtils.upColor;
final downColor = ChartColorUtils.downColor;
final refColor = ChartColorUtils.refColor;

final shadowUpColor = ChartColorUtils.shadowUpColor;
final shadowDownColor = ChartColorUtils.shadowDownColor;
final shadowRefColor = ChartColorUtils.shadowRefColor;

class _LineBuf {
  /// Danh sách các điểm (ChartData) sau khi đã chèn thêm điểm giao cắt
  final List<ChartData> points;

  /// Tọ<PERSON> độ <PERSON> chuẩn hóa (double) tương ứng với từng điểm
  final List<double> xValues;

  /// Màu của đoạn tại từng điểm (đỏ/xanh tùy trên hay dưới tham chiếu)
  final List<Color> segmentColors;

  _LineBuf(this.points, this.xValues, this.segmentColors);
}

/// Tạo buffer cho line chart với màu phân đoạn theo giá tham chiếu.
///
/// - Duyệt từng cặp điểm [current] → [next].
/// - Thêm điểm gốc vào danh sách (points, xValues, segmentColors).
/// - Nếu đoạn cắt qua referencePrice:
///   + Tính nội suy vị trí/time giao cắt.
///   + Thêm 2 điểm giao cắt (trước & sau, tách bằng eps) để đổi màu mượt.
/// - Cuối cùng thêm điểm cuối.
///
/// Trả về [_LineBuf]:
///   points: danh sách điểm (gồm cả giao cắt)
///   xValues: toạ độ X tương ứng
///   segmentColors: màu cho từng điểm
_LineBuf _buildLineBuffer(
  List<ChartData> src,
  double refPrice, {
  num? ceilingPrice,
  num? floorPrice,
  double eps = 1e-6,
}) {
  final points = <ChartData>[];
  final xValues = <double>[];
  final segmentColors = <Color>[];

  Color color(num v) {
    if (v == refPrice) return refColor;

    return v > refPrice ? upColor : downColor;
  }

  void getPoint({
    required ChartData current,
    required ChartData next,
    required int index,
    Color? currentPointColor,
  }) {
    final dy = (next.value - current.value).toDouble();
    if (dy != 0) {
      final t = (refPrice - current.value) / dy; // 0..1
      final xIntersect = index + t;

      final interpolatedMillis =
          current.time.millisecondsSinceEpoch +
          (t *
                  (next.time.millisecondsSinceEpoch -
                      current.time.millisecondsSinceEpoch))
              .round();
      final time = DateTime.fromMillisecondsSinceEpoch(interpolatedMillis);

      points.add(ChartData(time: time, value: refPrice, volume: 0));
      xValues.add(xIntersect - eps);
      segmentColors.add(currentPointColor ?? color(current.value));

      points.add(ChartData(time: time, value: refPrice, volume: 0));
      xValues.add(xIntersect + eps);
      segmentColors.add(color(next.value));
    }
  }

  if (src.isEmpty) return _LineBuf(points, xValues, segmentColors);

  for (var index = 0; index < src.length - 1; index++) {
    final current = src[index];
    final next = src[index + 1];

    points.add(current);
    xValues.add(index.toDouble());
    segmentColors.add(color(current.value));

    /// Xử lý trường hợp 2 điểm nằm 2 bên đường tham chiếu
    final isCurrentAboveRef = current.value > refPrice;
    final isNextAboveRef = next.value > refPrice;

    if (isCurrentAboveRef != isNextAboveRef) {
      getPoint(current: current, next: next, index: index);

      continue;
    }

    /// Điểm bắt đầu nằm trên tham chiếu, điểm kết thúc nằm ngoài tham chiếu
    final isCurrentAtReference = current.value == refPrice;
    final isNextNotAtReference = next.value != refPrice;

    if (isCurrentAtReference && isNextNotAtReference) {
      getPoint(
        current: current,
        next: next,
        index: index,
        currentPointColor: color(next.value),
      );

      continue;
    }

    /// Điểm bắt đầu nằm ngoài tham chiếu, điểm kết thúc nằm trên tham chiếu
    final isCurrentNotAtReference = current.value != refPrice;
    final isNextAtReference = next.value == refPrice;

    if (isCurrentNotAtReference && isNextAtReference) {
      getPoint(current: current, next: next, index: index);

      continue;
    }
  }

  final last = src.last;
  points.add(last);
  xValues.add((src.length - 1).toDouble());
  segmentColors.add(color(last.value));

  return _LineBuf(points, xValues, segmentColors);
}

class IntradayChartView extends StatefulWidget {
  const IntradayChartView({
    required this.chartData,
    required this.refPrice,
    this.floorPrice,
    this.ceilingPrice,
    this.minutesPerPoint = 5,
    this.showVolume = false,
    this.primaryYAxisVisible = true,
    this.refDashArray = const [4, 6],
    this.minY = -60,
    this.maxY = 130,
    this.xAxisInterval,
    this.xAxisVisible = false,
    this.tooltipBuilder,
    this.primaryXAxisFormatter,
    this.primaryYAxisFormatter,
    super.key,
  });

  final List<ChartData> chartData;

  final double refPrice;

  final double? floorPrice;

  final double? ceilingPrice;

  final bool showVolume;

  final bool primaryYAxisVisible;

  final int minutesPerPoint;

  final List<double> refDashArray;

  final double minY;

  final double maxY;

  final bool xAxisVisible;

  final double? xAxisInterval;

  final String Function(num value)? primaryXAxisFormatter;

  final String Function(double value)? primaryYAxisFormatter;

  final Widget Function(ChartData data)? tooltipBuilder;

  @override
  State<IntradayChartView> createState() => _IntradayChartViewState();
}

class _IntradayChartViewState extends State<IntradayChartView> {
  late IntradayChartAdapter adapter = IntradayChartAdapter(
    minY: widget.minY,
    maxY: widget.maxY,
    refPrice: widget.refPrice,
    chartData: widget.chartData,
    minutesPerPoint: widget.minutesPerPoint,
  );

  TrackballBehavior? trackballBehavior;

  TrackballMarkerSettings? getMarkerSettings() {
    final color =
        (adapter.chartData.lastOrNull?.value ?? 0) >= widget.refPrice
            ? upColor
            : downColor;

    return TrackballMarkerSettings(
      height: 10,
      width: 10,
      borderWidth: 12,
      color: color,
      shape: DataMarkerType.circle,
      borderColor: color.withValues(alpha: 0.24),
      markerVisibility: TrackballVisibilityMode.visible,
    );
  }

  void createTrackballBehavior() {
    trackballBehavior ??= TrackballBehavior(
      tooltipSettings: const InteractiveTooltip(
        enable: true,
        borderRadius: 4,
        arrowLength: 0,
        arrowWidth: 0,
      ),
      lineDashArray: const [5, 5],
      lineColor: vpColor.strokeNormal,
      enable: true,
      activationMode: ActivationMode.singleTap,
      tooltipDisplayMode: TrackballDisplayMode.floatAllPoints,
      markerSettings: getMarkerSettings(),
      builder: (BuildContext context, TrackballDetails args) {
        if (args.series.name == 'Price' &&
            args.pointIndex != null &&
            args.pointIndex! >= 0 &&
            args.pointIndex! < lineBuf.points.length) {
          final data = lineBuf.points[args.pointIndex!];

          if (data.time.minute % widget.minutesPerPoint != 0) {
            return const SizedBox.shrink();
          }

          return widget.tooltipBuilder?.call(data) ??
              IntradayChartTooltipView(
                data: data,
                floorPrice: widget.floorPrice,
                ceilingPrice: widget.ceilingPrice,
                referencePrice: widget.refPrice,
              );
        }
        return const SizedBox.shrink();
      },
    );
  }

  @override
  void initState() {
    createTrackballBehavior();
    super.initState();
  }

  @override
  void didUpdateWidget(covariant IntradayChartView oldWidget) {
    super.didUpdateWidget(oldWidget);
    adapter.updateChartData(widget.chartData);
  }

  late _LineBuf lineBuf;

  @override
  Widget build(BuildContext context) {
    final ref = widget.refPrice;

    lineBuf = _buildLineBuffer(
      adapter.chartData,
      ref,
      ceilingPrice: widget.ceilingPrice,
      floorPrice: widget.floorPrice,
    );

    return SfCartesianChart(
      trackballBehavior: trackballBehavior,
      axes: <ChartAxis>[
        if (widget.showVolume)
          NumericAxis(
            name: 'volumeAxis',
            minimum: 0,
            maximum: adapter.maxY + adapter.minY.abs(),
            isVisible: false,
          ),
        NumericAxis(
          name: 'referenceAxis',
          opposedPosition: false,
          isVisible: true,
          minimum: adapter.minY,
          maximum: adapter.maxY,
          borderWidth: 0,
          maximumLabelWidth: 0,
          labelStyle: const TextStyle(color: Colors.transparent),
          majorGridLines: const MajorGridLines(width: 0),
          majorTickLines: const MajorTickLines(width: 0),
          axisLine: const AxisLine(width: 0),
          plotBands: <PlotBand>[
            PlotBand(
              isVisible: true,
              start: adapter.getY(widget.refPrice),
              end: adapter.getY(widget.refPrice),
              borderColor: vpColor.strokeWarning.withValues(alpha: 0.5),
              borderWidth: 1,
              dashArray: widget.refDashArray,
            ),
          ],
        ),
      ],
      series: [
        RangeAreaSeries<ChartData, double>(
          dataSource: lineBuf.points,
          xValueMapper: (_, i) => lineBuf.xValues[i],
          highValueMapper: (d, i) {
            final y = adapter.getY(d.value);
            return (lineBuf.segmentColors[i] == refColor) ? y : null;
          },
          lowValueMapper: (d, i) {
            return (lineBuf.segmentColors[i] == refColor) ? adapter.minY : null;
          },
          color: shadowRefColor,
          borderWidth: 0,
          enableTooltip: false,
          isVisibleInLegend: false,
        ),

        RangeAreaSeries<ChartData, double>(
          dataSource: lineBuf.points,
          xValueMapper: (_, i) => lineBuf.xValues[i],
          highValueMapper: (d, i) {
            final y = adapter.getY(d.value);
            return (lineBuf.segmentColors[i] == upColor) ? y : null;
          },
          lowValueMapper: (d, i) {
            return (lineBuf.segmentColors[i] == upColor) ? adapter.minY : null;
          },
          color: shadowUpColor,
          borderWidth: 0,
          enableTooltip: false,
          isVisibleInLegend: false,
        ),

        RangeAreaSeries<ChartData, double>(
          dataSource: lineBuf.points,
          xValueMapper: (_, i) => lineBuf.xValues[i],
          highValueMapper: (d, i) {
            final y = adapter.getY(d.value);
            return (lineBuf.segmentColors[i] == downColor) ? y : null;
          },
          lowValueMapper: (_, i) {
            return (lineBuf.segmentColors[i] == downColor)
                ? adapter.minY
                : null;
          },
          color: shadowDownColor,
          borderWidth: 0,
          enableTooltip: false,
          isVisibleInLegend: false,
        ),

        SplineSeries<ChartData, double>(
          name: 'Price',
          width: 1,
          dataSource: lineBuf.points,
          splineType: SplineType.monotonic,
          xValueMapper: (_, i) => lineBuf.xValues[i],
          yValueMapper: (d, _) => adapter.getY(d.value),
          pointColorMapper: (_, i) => lineBuf.segmentColors[i],
        ),

        if (widget.showVolume)
          ColumnSeries<ChartData?, double>(
            dataSource: adapter.chartData,
            xValueMapper: (data, index) => index.toDouble(),
            yValueMapper: (data, index) => adapter.getYVolume(data?.volume),
            name: 'Volume',
            yAxisName: 'volumeAxis',
            pointColorMapper:
                (item, index) => ChartColorUtils.getVolumeItemColor(
                  reference: widget.refPrice,
                  data: adapter.chartData,
                  item: item,
                ).withValues(alpha: 0.4),
            enableTooltip: false,
            isVisibleInLegend: false,
            markerSettings: const MarkerSettings(isVisible: false),
          ),
      ],
      primaryXAxis: NumericAxis(
        isVisible: widget.xAxisVisible,
        interval: widget.xAxisInterval,
        maximum: adapter.maxX,
        minimum: adapter.minX,
        majorGridLines: const MajorGridLines(width: 0),
        majorTickLines: const MajorTickLines(width: 0),
        axisLabelFormatter:
            widget.primaryXAxisFormatter != null
                ? (details) {
                  return ChartAxisLabel(
                    widget.primaryXAxisFormatter!.call(details.value),
                    vpTextStyle.captionRegular.copyColor(vpColor.textPrimary),
                  );
                }
                : null,
      ),
      primaryYAxis: NumericAxis(
        isVisible: widget.primaryYAxisVisible,
        minimum: adapter.minY,
        maximum: adapter.maxY,
        opposedPosition: true,
        interval: 10,
        majorGridLines: const MajorGridLines(width: 0),
        majorTickLines: const MajorTickLines(width: 0),
        axisLine: AxisLine(width: 1, color: vpColor.strokeNormal),
        axisLabelFormatter: (AxisLabelRenderDetails details) {
          if (showTicksAt.contains(details.value)) {
            final price = adapter.getPriceFromY(details.value.toDouble());
            return ChartAxisLabel(
              widget.primaryYAxisFormatter?.call(price) ??
                  FormatUtils.formatClosePrice(price, trimZero: false) ??
                  '',
              vpTextStyle.captionRegular.copyColor(vpColor.textSecondary),
            );
          }
          return ChartAxisLabel(
            '',
            const TextStyle(color: Colors.transparent, fontSize: 0),
          );
        },
      ),
      margin: EdgeInsets.zero,
      plotAreaBorderWidth: 0,
    );
  }

  final List<double> showTicksAt = const [0, 50.0, 100];
}
