import 'dart:math';

import 'package:intl/intl.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_stock_common/vp_stock_common.dart';

extension NumExtensions on num {
  DateTime? timestampToDateTime({
    bool isMilliseconds = true,
    bool isUtc = false,
  }) {
    var newTimestamp = toInt();
    if (!isMilliseconds) newTimestamp *= 1000;

    return DateTime.fromMillisecondsSinceEpoch(newTimestamp, isUtc: isUtc);
  }

  String toFormatThousandSeparator({bool isReturnZero = false}) {
    if (this == 0 && !isReturnZero) return '';
    final format = NumberFormat('#,###.##', 'en_US');
    final result = format.format(this);
    return result.trim();
  }

  String toMoney({
    bool addCharacter = false,
    bool showSymbol = true,
    String? symbol,
  }) {
    final value = this == -0.0 ? 0 : this;

    final format = NumberFormat.currency(
      locale: 'vi',
      symbol: showSymbol ? (symbol ?? VPCommonLocalize.current.vnd) : '',
    );

    var result = format.format(value);
    result = result.trim().replaceAll('.', ',');

    if (addCharacter && value > 0 && !result.startsWith('+')) {
      result = '+$result';
    }

    return result;
  }

  String format() {
    final format = NumberFormat('#,###.##', 'en_US');
    final result = format.format(this);
    return result.trim();
  }

  String toFormat3() {
    final format = NumberFormat('###,###,###', 'en_US');
    final result = format.format(this);
    return result.trim();
  }

  String toFormat4() {
    final num val = double.parse(toStringAsFixed(1));
    if (val % 1 == 0) {
      final format = NumberFormat("###,###,##0.0", "en_US");
      final result = format.format(val);
      return result.trim();
    }
    final format = NumberFormat("###,###,###.#", "en_US");
    final result = format.format(val);
    return result.trim();
  }

  bool get isOddLot => this > 0.0 && this < 100;

  bool isZero() {
    return this == 0 || this == 0.0;
  }

  double toPrecision(int fractionDigits) {
    final mod = pow(10, fractionDigits.toDouble()).toDouble();
    return (this * mod).round().toDouble() / mod;
  }

  String getPriceFormatted({
    bool convertToThousand = false,
    String currency = '',
    bool trimZero = false,
    bool prefix = false,
  }) {
    if (this == 0) {
      return '0';
    }
    var value = this;
    var decimalDigits = 2;
    if (convertToThousand) {
      value = value / 1000;
    }

    if (trimZero) {
      if (value == value.round().toDouble()) {
        decimalDigits = 0;
      }
    }

    final format = NumberFormat.currency(
      locale: 'en-US',
      symbol: '',
      decimalDigits: decimalDigits,
    );

    final prefixText = prefix && value > 0.0 ? '+' : '';

    return '$prefixText${format.format(value)}$currency';
  }

  String getChangePercentDisplay(
      {bool addCharacter = false, int? fractionDigits}) {
    if (this == 0) {
      return '0%';
    }

    if (this == 100) {
      return '100%';
    }
    if (fractionDigits != null) {
      return '${(addCharacter && this > 0) ? '+' : ''}${toStringAsFixed(fractionDigits)}%';
    } else {
      if ((this * 100) % 10 == 0) {
        return '${(addCharacter && this > 0) ? '+' : ''}${toStringAsFixed(1)}%';
      }

      return '${(addCharacter && this > 0) ? '+' : ''}${toStringAsFixed(2)}%';
    }
  }

  String getMarketChangePercentDisplay({int fractionDigits = 2}) {
    if (this == 0) {
      return '0%';
    }

    if (this == 100) {
      return '100%';
    }

    if (this > 0) {
      return '${toStringAsFixed(fractionDigits)}%';
    }

    return '${toStringAsFixed(fractionDigits)}%';
  }

  String toPriceChart({NumberFormat? numberFormat}) {
    final currencyFormat = numberFormat ?? NumberFormat('#,###.#', 'en_ES');
    var volume = toDouble();
    if (volume >= 100000000) {
      volume = volume / 1000000000;
      return '${currencyFormat.format(volume)} tỷ';
    }
    if (volume >= 100000) {
      volume = volume / 1000000;
      return '${currencyFormat.format(volume)} tr';
    }
    if (volume >= 100) {
      volume = volume / 1000;
      return '${currencyFormat.format(volume)} nghìn';
    }
    return volume.toInt().toString();
  }

  String convertToABSValueAndFormat() {
    return abs().toDouble().toStringAsFixed(2);
  }

  String formatDoubleCurrencyValue(String? suffix) {
    return '${toFormatStock()}${suffix != null ? ' $suffix' : ''}';
  }

  String toFormatStock() {
    final NumberFormat format = NumberFormat("#,###.##", "en_US");
    String result = format.format(this);
    return result.trim();
  }

  String toMoneyTooltip() {
    const num offset = 1000000;
    return (abs() * offset).toMoney();
  }

  num toValidValueRequest() {
    return toPrecision(2);
  }

  int getValueToDivisible5() {
    final valueDividerBy10 = this ~/ 10;
    final surPlus = valueDividerBy10 % 10;
    switch (surPlus) {
      case 0:
      case 1:
      case 2:
      case 3:
        return (valueDividerBy10 - surPlus) * 10;
      case 4:
      case 5:
      case 6:
        return (valueDividerBy10 - surPlus + 5) * 10;
      case 7:
      case 8:
      case 9:
        return (valueDividerBy10 - surPlus + 10) * 10;
      default:
        return toInt();
    }
  }

  num getValidPriceForConditionalRequest(
      StockType? stockType, ExchangeCode? exchange) {
    const tenK = 10000;
    const fiftyK = 50000;
    if (stockType == StockType.CW ||
        (stockType == StockType.EF && !(exchange == ExchangeCode.UPCOM))) {
      return ((this / 10).roundToDouble() * 10) / 1000;
    }
    final value = this / 1000;
    if (exchange == ExchangeCode.HOSE) {
      if (this < tenK) {
        return value.toPrecision(2);
      } else if (tenK <= this && this < fiftyK) {
        return getValueToDivisible5() / 1000;
      } else {
        return value.toPrecision(1);
      }
    } else {
      return value.toPrecision(1);
    }
  }

  num formatConditionalPriceToRequest(
      StockType? stockType, ExchangeCode? exchange) {
    final value = getValidPriceForConditionalRequest(stockType, exchange);
    return (value * 1000).round().toInt();
  }

  num get priceInt {
    if (this % 1 == 0) {
      return toInt();
    }
    return this;
  }
}
