import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';

final _bgLoading = Colors.black.withOpacity(0.3);
const Color _bgPopupLight = Color(0xffffffff);
const Color _bgPopupDark = Color(0xff21212e);

extension DarkMode on BuildContext {
  /// is dark mode currently enabled?
  bool get isDarkMode {
    final brightness = MediaQuery.of(this).platformBrightness;
    return brightness == Brightness.dark;
  }

  Color get bgPopup {
    return isDarkMode ? _bgPopupDark : _bgPopupLight;
  }
}

/// Tuy tung man hinh su dung
class VPBankLoading extends StatelessWidget {
  const VPBankLoading({super.key, this.size = 50});

  final double? size;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: Lottie.asset(Assets.json.loadingLogo),
    );
  }
}

/// Full man hinh voi text loading
class VPBankLoadingWithText extends StatelessWidget {
  const VPBankLoadingWithText({ this.text, super.key});

  final Widget? text;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          Container(
            color: _bgLoading,
          ),
          Center(
              child: Container(
                  decoration: BoxDecoration(
                      color: context.bgPopup,
                      borderRadius: BorderRadius.circular(18)),
                  width: MediaQuery.of(context).size.width * 0.7,
                  child: IntrinsicHeight(
                    child: Column(
                      children: [
                        const SizedBox(height: 24),
                        const VPBankLoading(),
                        const SizedBox(height: 16),
                       if (text != null) text!,
                        const SizedBox(height: 24),
                      ],
                    ),
                  )))
        ],
      ),
    );
  }
}

/// Full man hinh khong text
class VPBankLoadingDefault extends StatelessWidget {
  const VPBankLoadingDefault({super.key, this.bgColor, this.boxColor});

  factory VPBankLoadingDefault.transparent() => const VPBankLoadingDefault(
        bgColor: Colors.transparent,
        boxColor: Colors.transparent,
      );

  final Color? bgColor;

  final Color? boxColor;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          color: bgColor ?? _bgLoading,
        ),
        Center(
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: boxColor ?? context.bgPopup,
              borderRadius: BorderRadius.circular(16),
            ),
            child: const VPBankLoading(),
          ),
        )
      ],
    );
  }
}

void showDialogLoading({bool isNoText = false, bool onWillPop = true}) {
  final context =
      GetIt.instance<NavigationService>().navigatorKey.currentContext;
  if (context == null) {
    return;
  }
  showDialog<void>(
      barrierColor: themeData.bgLoading,
      context: context,
      barrierDismissible: false,
      builder: (context) {
        if (isNoText) {
          return PopScope(
            canPop: onWillPop,
            child: SizedBox(
              width: double.maxFinite,
              height: double.maxFinite,
              child: Center(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: themeData.bgPopup,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const VPBankLoading(size: 44),
                ),
              ),
            ),
          );
        }
        return PopScope(
          canPop: onWillPop,
          child: AlertDialog(
            backgroundColor: themeData.bgPopup,
            contentPadding: const EdgeInsets.symmetric(vertical: 24),
            content: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(18),
                ),
                child: IntrinsicHeight(
                  child: Column(
                    children: [
                      const VPBankLoading(),
                      const SizedBox(height: 16),
                      Text(
                        VPCommonLocalize.current.loading,
                        style: vpTextStyle.body14.copyColor(themeData.gray500),
                      ),
                    ],
                  ),
                )),
          ),
        );
      });
}

void hideDialogLoading() {
  final context =
      GetIt.instance<NavigationService>().navigatorKey.currentContext;
  if (context == null) {
    return;
  }
  Navigator.pop(context);
}
