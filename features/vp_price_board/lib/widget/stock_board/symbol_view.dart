part of 'stock_board_item_view.dart';

class _SymbolView extends StatelessWidget {
  const _SymbolView({required this.item, this.onEventTap});

  final StockInfoModel item;

  final Function(StockInfoModel stock)? onEventTap;

  @override
  Widget build(BuildContext context) {
    final child = VPSocketInvestmentBuilder<VPStockInfoData>(
      symbol: item.symbol,
      emitImmediately: true,
      channel: VPSocketChannel.stockInfo.name,
      buildWhen:
          (preData, data) => StockBoardItemHelper.buildWhen(
            type: StockInfoFieldType.symbol,
            item: item,
            preData: preData,
            data: data,
          ),
      builder: (_, __, data, child) {
        return AutoSizeText(
          item.symbol,
          maxLines: 1,
          minFontSize: 10,
          softWrap: false,
          textAlign: TextAlign.start,
          overflow: TextOverflow.visible,
          style: vpTextStyle.subtitle14.copyColor(
            StockBoardUIHelper.getTextColor(item: item, data: data),
          ),
        );
      },
    );

    if (!item.haveEvent) return child;

    return GestureDetector(
      onTap: () => onEventTap?.call(item),
      child: Row(
        spacing: 2,
        children: [
          child,
          Icon(Icons.info, color: vpColor.iconAccentBlue, size: 10),
        ],
      ),
    );
  }
}
