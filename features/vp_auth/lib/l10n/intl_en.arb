{"@@locale": "en", "account_market": "Market", "account_smartOTP": "Smart OTP", "account_support": "Support", "account_account": "Account", "account_open_account": "Register", "account_sign_up_bank_note2": "This account also receives proceeds from the sale and dividends of fund certificates (if any).", "account_user_reference": "Presenter", "account_if_not_user_reference": "If you don't have a referral, you can leave it blank and move on", "account_not_found_user_reference": "No referrer found", "account_type_user_reference": "Enter referrer code (if any)", "account_register": "Register account", "account_declare_information": "Declare information", "account_back": "Back", "account_next": "Next", "account_enter_phone_number": "Enter your phone number", "account_login": "<PERSON><PERSON>", "account_password": "Password", "account_input_account": "Enter account number or phone number", "account_input_user": "Enter account number or phone numberC", "account_please_input_user": "Please input Account number 116C", "account_input_email_or_phone": "Email or phone number", "account_please_input_email_or_phone": "Please input email or phone number", "account_input_phone_or_email": "Phone or email", "account_hint_input_phone_or_email": "input phone or email", "account_guide_input_email_or_phone": "Please enter the OTP code that has been sent to your email or phone number", "account_date_of_birth": "Date of birth", "account_hint_input_date_of_birth": "Input date of birth", "account_cccd": "CCCD/HC/GPKD Number", "account_hint_input_cccd": "input CCCD", "account_forgot_password": "Forgot password", "account_create_new_password": "Create a new password", "account_please_input_info_for_reset_password": "Please enter the information below to reinstall the password", "account_id_number": "CCCD/HC/GPKD", "account_input_id_number": "Enter CCCD number", "account_create_password": "Create password", "account_input_phone_info": "Import your phone number", "account_input_email_info": "Import your email address", "account_phone": "Phone", "account_email": "Email", "account_hint_email": "Email Address", "account_phone_number": "Phone number", "account_email_register": "<EMAIL>", "account_system_send_otp": "The system has sent OTP to your phone number. Please enter the OTP received", "account_counting_otp": "OTP code has an expiry date in", "account_expried_otp": "The OTP has expired. Please try again.", "account_re_enter_otp": "Resend OTP", "account_invalid_otp": "OTP code is incorrect, please try again", "account_invalid_otp_account": "OTP code is incorrect, please try again", "account_enter_email": "Enter your email", "account_setup_pass": "Set a password for your account", "account_enter_pass": "Enter password", "account_re_enter_pass": "Re-enter password", "account_please_enter_your_password": "Please enter your password", "account_current_password": "Current password", "account_enter_the_new_password": "Enter the new password", "account_length_8_50_characters": "Length 8 - 50 characters", "account_minimum_1_capital_letter": "Minimum 1 capital letter", "account_minimum_1_character_number": "Minimum 1 character number", "account_does_not_contain_space": "Does not contain space", "account_valid_pass": "Password must contain at least 8 characters, at least 1 capital letter and 1 number", "account_not_match_pass": "The entered passwords do not match. Please try again.", "account_change_pass_success": "Change the password successfully", "account_change_pass_success_description": "The password has been successfully changed. Please login again to use the application", "account_fail_enter_pass": "Message 'Invalid password. A valid password must be at least 8 characters with at least 1 uppercase letter and 1 number'", "account_enter_bank_acc": "Enter your bank account", "account_your_bank_info": "Your bank account information", "account_select_bank": "Choose a bank", "account_account_number": "Account Number", "account_input_account_number": "Input account number", "account_set_bond_enjoyment": "Set bond enjoyment", "account_set_fund_enjoyment": "Set fund enjoyment", "account_vp_bank_account": "Don't have VPBank account?", "account_register_now": "Register now", "account_select_acc_number": "Select securities account number", "account_select_acc_number_guide": "Please select your stock trading account number", "account_suggest_acc_number": "Suggestions for you", "account_question_support": "Don't have a caregiver yet?", "account_dont_have_support": "I want to find a caregiver", "account_have_support": "I have a caregiver", "account_no_need_support": "I have no need", "account_incorrect_information": "Incorrect information", "account_incorrect_information_message": "The information you have just entered is incorrect, please try again.", "account_close": "Close", "account_error": "An error occurred, please try again later!", "account_no_touch_id": "Touch ID not installed", "account_no_face_id": "Face ID not installed", "account_login_failde": "<PERSON>gin failde", "account_disable_fingerprint": "Fingerprint login has been disabled due to incorrect input multiple times. Please use password to login the app", "account_go_to_settings_button": "Setting", "account_go_to_settings_description": "Biometric authentication is not set up on your device. Please either enable Touch ID or Face ID on your phone.", "account_fingerprint": "Scan your fingerprint (or face or whatever) to authenticate", "account_active_fingerprint": "Please sign in to the app to activate with your fingerprint/faceid", "account_message_no_touch_id": "You have not set up your account's Touch ID on this device. Please login with password and set up Touch ID to use this function", "account_enter_password": "Please enter a password", "account_enter_your_password": "Please enter your password", "account_enter_your_pin": "Please enter your PIN", "account_enter_username": "Please enter account number or phone number", "account_search_bank": "Search by bank name", "account_font_identi_card": "Front of Identity card", "account_back_identi_card": "Back of Identity card", "account_signature": "Signature", "account_signature_title": "Signature", "account_note_upload": "Note:\\n- Please take photos of the same identity card\\n- Please take photos clearly, without losing angles and overexposing important information on the card", "account_cancel": "Cancel", "account_tutorial_ocr": "Make sure the card is captured clearly, the information is not blurred or glare", "account_tutorial_ocr2": "Ensure captured information is clear, without blur or glare", "account_loading_wait": "The system is processing, please wait...", "account_unrecognizable": "Unrecognizable", "account_ocr_fail_description": "The photo you just took cannot be used. Please check if the information is blurry, in a well-lit environment and try again", "account_retry": "Retry", "account_select_account": "Select account", "account_invalid_username": "Invalid username", "account_invalid_phone_or_email": "Invalid phone or email", "account_info_check_guide": "Personal information", "account_guide_edit_info": "If your above information is not correct with the information on CCCD, you can correct your information or go to VPBank Securities counter for assistance", "account_edit_info": "Edit information", "account_edit_info_short": "Edit info", "account_edit_info_guide": "You can manually edit the information. However, your account may be temporarily restricted until verification (3-5 days).", "account_edit": "Edit", "account_full_name": "Full name", "account_birthday": "Birthday", "account_identify_card": "ID Card", "account_take_a_photo_verification": "Take a photo verification", "account_update_cccd": "Update citizen identification information", "account_issued_date_card": "Issued Date", "account_place_of_issued_card": "Place Issued", "account_permanent_address": "Permanent Address", "account_upload_profile": "Upload Profile", "account_e_kyc_title": "Face Recognition", "account_e_kyc_guide": "VPBank Securities needs data about your face to ensure security and information verification.", "account_start_ekyc": "Start face recognition", "account_match_face": "Please fit your face into the frame", "account_verify_face_fail": "Face detection failed, please try again", "account_verify_face_success": "Face authentication successful", "account_contract_and_term": "Contract & Terms", "account_change_password": "Change password", "account_new_password": "New password", "account_confirm_new_password": "Confirm new password", "account_success_change_password": "Password changed successfully", "account_change_pin": "Change the PIN", "account_change_the_pin": "Change PIN", "account_change_pin_for_continue": "Please enter the PIN to continue", "account_pin_is_sent": "PIN is sent", "account_new_pin": "New PIN code", "account_enter_the_new_pin": "Enter the new PIN", "account_length_6_30_characters": "Length 6 - 30 characters", "account_do_not_contain_special_characters": "Do not contain special characters", "account_forgot_the_pin": "Forgot the PIN", "account_pin_not_match": "PIN does not match each other. Please try again", "account_change_pin_successfully": "Change the battery successfully", "account_change_pin_successfully_des": "Your battery has been successfully changed", "account_wrong_otp": "OTP code is incorrect, please try again", "account_re_send_otp": "Resend OTP", "account_gender": "Gender", "account_gender_female": "Female", "account_gender_male": "Male", "account_no_support": "Touch ID not supported", "account_biometric_invalid": "Biometric invalid", "account_goto_main_page": "Go to home screen", "account_contract": "Contract", "account_current_address": "Current residential address", "account_us_citizen": "US Citizen", "account_yes": "Yes", "account_no": "No", "account_error_invalid_phone": "Invalid account number", "account_error_invalid_email": "<PERSON><PERSON><PERSON>", "account_bank_info": "Bank information", "account_bank": "Bank", "account_bank_branch": "Branch", "account_account_holder_name": "Account Holder Name", "account_edit_bank_acc": "Edit bank accounts", "account_agree": "Agree", "account_success": "Success", "account_success_change_pass_pin": "PIN and password created successfully", "account_find_branch_bank": "Branch Search", "account_support_call_hotline": "Call Hotline", "account_support_website": "Website", "account_support_zalo": "Cha<PERSON> with support", "account_support_zalo2": "VPBank Securities", "account_money_laundering_prevention": "Money laundering prevention", "account_intended_use": "Purpose of using the account", "account_job": "Job", "account_position": "Position", "account_notification": "Notification", "account_continue_text": "Continue", "account_customer_confirmation": "Customer confirmation:", "account_customer_confirmation1": "Not a US citizen, do not hold a US Green Card, and not a resident in the US.", "account_customer_confirmation2": "No other beneficial ownership.", "account_customer_confirmation3": "No trustee involved.", "account_phone_number_used": "Your phone number is already linked to an account at VPBank. Please click 'Understood' to proceed with opening a securities account.", "account_exist_account_bank": "You already have registration information at VPBank. Please click 'Understood' to continue opening a securities account.", "account_open_an_account": "Open a VPBank bank account", "account_document_invalid": "Please register with your CCCD number to open both VPBank securities and banking accounts. Or click 'Continue' to open a securities account with your ID card.", "account_over_gen_otp": "Exceeded daily OTP limit", "account_create_over_otp": "You have reached the daily OTP creation limit and exceeded the maximum attempts. Please try again tomorrow.", "account_splash1_1": "3 minutes eKYC\nAccount is available", "account_splash1_2": "\nInstant transaction", "account_splash2": "Only with\nPhone with camera\nand valid ID/CCCD", "account_guide_input_phone": "The phone number is used to log in to the account and receive OTP in the system of VPBank Securities.", "account_success_phone": "Phone numbers that can be used", "account_wrong_phone": "Invalid phone number. Phone number must start with 0 up to 10 numbers", "account_check_email_guide_title": "Email is used to:", "account_check_email_guide_content1": "Receive transaction and asset alerts", "account_check_email_guide_content2": "Get market information, investment recommendations", "account_check_email_guide_content3": "Update new products and services, offers", "account_check_email_guide_content4": "Enhance account security", "account_input_otp": "Input OTP", "account_verify": "Verify", "account_info_email": "Email information", "account_not_found_support": "No caregiver found", "account_presenter": "Presenter", "account_ekyc_title_1": "Have your CCCD/ID card ready. Make sure:", "account_ekyc_description_1": "Use the original and valid CCCD/ID card.", "account_ekyc_description_2": "Place the document flat within the frame.", "account_ekyc_description_3": "Make sure all information on the document is clear and\nreadable. Avoid dark, blurry, or glared photos.", "account_ekyc_description_4": "VPBank Securities will also require facial data to ensure identity and security.", "account_signature_description_1": "A photo of your signature will be used to\nverify and enhance account security.", "account_signature_description_2": "Please prepare your signature on a piece of\npaper so we can take a photo.", "account_your_signature": "Your signature photo", "account_front": "Front", "account_behind": "Behind", "account_start_taking_photos": "Start taking photos", "account_tutorial_ocr_1": "Make sure the card is clearly captured and the information is not blurry or glared.", "account_tutorial_ocr_2": "Ensure captured information is clear, without blur or glare.", "account_requestReLogin": "<PERSON>ui lòng đăng nhập lại để tiếp tục sử dụng dịch vụ", "account_sessionExpire": "<PERSON><PERSON><PERSON> đ<PERSON> nh<PERSON>p hết hạn", "account_sessionRegisterExprie": "<PERSON><PERSON><PERSON> đ<PERSON>ng ký hết hạn", "account_unregister_smart_otp_title": "Smart OTP not registered", "account_unregister_smart_otp_content": "You have not registered Smart OTP on this device. Please log in and go to Security / Smart OTP to register.", "account_valid_name_empty": "Full name must not be blank", "account_valid_name_wrong": "Invalid full name. Full name must not include numbers or special characters", "account_valid_id_card_empty": "ID Card number cannot be left blank", "account_valid_id_card_special": "ID Card number does not include letters, special characters and spaces", "account_valid_id_wrong": "Invalid ID number", "account_valid_id_place": "Place of issue cannot be left blank", "account_valid_per_address": "Permanent address cannot be left blank", "account_valid_current_address": "Current residential address must be at least 15 characters", "account_note_open_an_account_1": "Currently, VPBank cannot provide services for you. Please contact the care hotline customer for support.", "account_note_open_an_account_2": "Click 'Understood' to continue opening a stock account.", "account_wrong_front_id_card": "Front side image is invalid. Please try again.", "account_wrong_back_id_card": "Back side image is invalid. Please try again.", "account_note": "Note", "account_sign_up_bank_note": "The bank account holder information must match the information on your CCCD/ID card", "account_no_result_search": "No results were found", "account_signature_description1": "A photo of your signature will be used to verify and enhance account security.", "account_signature_description2": "Please prepare your signature on a piece of paper so we can take a photo.", "account_cancel_register_title": "Cancel account registration?", "account_cancel_register_content1": "You did", "account_cancel_register_content2": "Are you sure you want to cancel the subscription?", "account_cancel_register": "Cancel subscription", "account_auto_find_acc": "Find account number automatically", "account_contract_note1": "By pressing", "account_contract_note2": "Confirm", "account_contract_note3": ", you confirm that you have read, understood and signed the Account Opening Agreement at VPBank Securities. Simultaneously agree for VPBankS to share customer's data and information and customer's securities trading account with VPBank Partners.", "account_confirm_supporter": "Confirm Caregiver information", "account_depository_acc": "Depository account number", "account_not_found_supporter_title": "Broker not found", "account_not_found_supporter_content": "The care worker account number you just entered does not exist on the system. Please try again.", "account_caring_staff": "Caring staff", "account_capture_profile": "Take a profile photo", "account_take_photos_successfully": "Take photos successfully", "account_confirm_info": "Confirm information", "account_not_rotate_90": "Do not rotate more than 90 degrees", "account_guide_ekyc1": "Rotate your face from the center of the camera to the sides to start recognizing faces", "account_guide_ekyc2": "You note your eyes can always look at the camera. Do not rotate more than 90 degrees when you cannot look at the camera", "account_ekyc_start": "The system prepares to identify later", "account_face_to_frame": "Put faces in frame", "account_register_success": "CONGRATULATIONS ON <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> OPENING A VPBANK SECURITIES ACCOUNT!", "account_register_success_combo": "CONGRATULATIONS ON S<PERSON>CE<PERSON><PERSON><PERSON><PERSON><PERSON> REGISTERING AND OPENING A COMBO ACCOUNT!", "account_content_success": "Please login with your account number and password sent via sms.\nThank you for trusting and accompanying VPBank Securities!", "account_content_success_combo1": "Information about securities accounts and bank account opening status will be sent to you via registered sms & email.", "account_content_success_combo2": "Thank you for trusting and investing with VPBank Securities!", "account_start_invest_vpbank": "Start investing with VPBankS", "account_sdk_vp_title": "Dont have a VPBank bank account yet?", "account_sdk_vp_r0": "Open a VPBank checking account now to enjoy the benefits", "account_sdk_vp_r1": "Free to open beautiful Digital Account", "account_sdk_vp_r2": "Quick deposit/withdrawal of securities", "account_sdk_vp_r3": "Free Investment Advisory Service", "account_sdk_vp_r4": "Unlimited Cashback with Credit Card", "account_sdk_action": "Open an account now", "account_take_photo_id_card": "Take CCCD", "account_type_address": "Enter address", "account_back_acc_name": "Bank Account name (Unsigned)", "account_msg_one": "Hiện tại VPBank chưa thể cung cấp dịch vụ cho bạn. <PERSON>ui lòng liên hệ tổng đài chăm sóc khách hàng ********** để được hỗ trợ. Ấn “Đã hiểu” để trở về.", "account_contract_success": "Contract download successful.", "account_contract_failed": "Load of contract failed. Please try again.", "account_takePhotosSuccessfully": "<PERSON><PERSON><PERSON>nh thành công ", "account_cancelChangePass": "<PERSON><PERSON><PERSON> thay đổi mật khẩu đăng nhập", "account_cancelChangePassConfirm": "Bạn có chắc chắn muốn hủy các bước thay đổi mật khẩu?"}