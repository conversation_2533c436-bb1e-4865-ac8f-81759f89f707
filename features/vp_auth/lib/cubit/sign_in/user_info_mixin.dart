import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:vp_auth/core/constant/setting_define_param_api.dart';
import 'package:vp_auth/core/repository/account_repository.dart';
import 'package:vp_auth/utils/list_account.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';

mixin UserInfoMixin {
  final repository = GetIt.instance<AccountRepository>();

  Future getUserInfo(
      {Function(UserInfoModel userInfo, bool updatePassword, bool updatePin)?
          onUpdatePinForFirst,
      Function(UserInfoModel userInfo)? onSuccess,
      Function(VerificationInfoModel verificationInfoDto)? onUpdateEkyc,
      Function(Object e)? onFail}) async {
    try {
      final futures = [
        _getVerificationInfo(),
        _getSubAccounts(),
        _getCustomerFlexInfo(),
      ];

      final values = await Future.wait(futures, eagerError: true);

      final userInfoStatus = values.first as VerificationInfoModel;
      final needChangeIdType = userInfoStatus.needChangeIdType;
      final userInfo = values.last as UserInfoModel;

      final updatePassword = userInfoStatus.firstUpdatePassword ?? false;

      final updatePin = userInfoStatus.firstUpdateTradingPassword ?? false;

      _saveInfoUser(userInfo);
      await _getUserSetting(userInfo.userinfo?.username ?? '');
      if (updatePassword || updatePin) {
        onUpdatePinForFirst?.call(userInfo, updatePassword, updatePin);
      } else {
        GetIt.instance<AuthCubit>().verificationInfoModel = userInfoStatus;
        onSuccess?.call(userInfo);
      }
      if ((needChangeIdType ?? false)) {
        onUpdateEkyc?.call(userInfoStatus);
      }

      _setCuIdToAppsFlyer();

      //  NotificationTopic().subCribeLogged();
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      // AppData().clear();
      onFail?.call(e);
    }
  }

  Future<VerificationInfoModel?> _getVerificationInfo() async {
    try {
      final _info = await repository.getVerificationInfo();
      return _info.data;
    } catch (e) {
      return null;
    }
  }

  Future<UserInfoModel?> _getCustomerFlexInfo() async {
    try {
      final value = await repository.getUserInfo();
      return value.data;
    } catch (e) {
      return null;
    }
  }

  /* ---- Kiểm tra tài khoản đã lưu tại local hay chưa ---- */
  void _saveInfoUser(UserInfoModel userInfo) async {
    try {
      setListAccount(userInfo);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  Future _getSubAccounts() async {
    try {
      final accountList = await repository.getAccountList();

      GetIt.instance<SubAccountCubit>().setSubAccounts(accountList);
    } catch (e) {
      GetIt.instance<SubAccountCubit>().setSubAccounts(null);
    }
  }

  /// Set CUID to Appsflyer
  void _setCuIdToAppsFlyer() {
    // try {
    //   final accessToken = Session().accessToken;

    //   if (accessToken == null) return;

    //   final mapToken = JwtDecoder.decode(accessToken);

    //   final userId = mapToken['user_id'];

    //   Appsflyer().setUserId(userId);
    // } catch (e) {
    //   dlog(e);
    // }
  }

  Future _getUserSetting(String userId) async {
    final param = SettingUserParam(
      userId: userId,
      targetApp: AppConstants.appCode,
      settingGroupCode: SettingApiGroupCode.all,
    );
    final values = await repository.getUserSetting(param);
    if (values.data != null) {
      final accountSettings = values.data!.firstWhereOrNull(
        (element) => element.settingGroupCode == SettingApiGroupCode.acc,
      );
      if (accountSettings != null) {
        final defaultSubAccount = accountSettings.listCode?.firstWhereOrNull(
          (element) => element.settingCode == SettingApiCodeACC.acc01,
        );
        if (defaultSubAccount != null) {
          final subAccountType = defaultSubAccount.settingValue ==
                  SubAccountType.normal.defaultSetting
              ? SubAccountType.normal
              : SubAccountType.margin;
          GetIt.instance<SubAccountCubit>()
              .setDefaultSubAccount(subAccountType);
        }
        final defaultConfirmCondition =
            accountSettings.listCode?.firstWhereOrNull(
          (element) => element.settingCode == SettingApiCodeACC.acc02,
        );
        if (defaultConfirmCondition != null) {
          final status = defaultConfirmCondition.settingCode == '1';
          await SharedPref.setBool(KeyShared.showConfirmOrderMessage, status);
        }
      }
      final colorAndLanguage = values.data!.firstWhereOrNull(
        (element) => element.settingGroupCode == SettingApiGroupCode.cal,
      );
      if (colorAndLanguage != null) {
        final color = colorAndLanguage.listCode?.firstWhereOrNull(
          (element) => element.settingCode == SettingApiCodeCAL.cal01,
        );
        if (color != null) {
          await SharedPref.setString(
              KeyShared.appColor, color.settingValue ?? AppTheme.light.value);
        }
        final language = colorAndLanguage.listCode?.firstWhereOrNull(
          (element) => element.settingCode == SettingApiCodeCAL.cal02,
        );
        if (language != null) {
          await SharedPref.setString(KeyShared.appLanguage,
              language.settingValue ?? AppLanguage.vi.value);
        }
        GetIt.instance<ThemeCubit>().init();
      }
    }
  }
}
