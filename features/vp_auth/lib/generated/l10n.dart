// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Market`
  String get account_market {
    return Intl.message(
      'Market',
      name: 'account_market',
      desc: '',
      args: [],
    );
  }

  /// `Smart OTP`
  String get account_smartOTP {
    return Intl.message(
      'Smart OTP',
      name: 'account_smartOTP',
      desc: '',
      args: [],
    );
  }

  /// `Support`
  String get account_support {
    return Intl.message(
      'Support',
      name: 'account_support',
      desc: '',
      args: [],
    );
  }

  /// `Account`
  String get account_account {
    return Intl.message(
      'Account',
      name: 'account_account',
      desc: '',
      args: [],
    );
  }

  /// `Register`
  String get account_open_account {
    return Intl.message(
      'Register',
      name: 'account_open_account',
      desc: '',
      args: [],
    );
  }

  /// `This account also receives proceeds from the sale and dividends of fund certificates (if any).`
  String get account_sign_up_bank_note2 {
    return Intl.message(
      'This account also receives proceeds from the sale and dividends of fund certificates (if any).',
      name: 'account_sign_up_bank_note2',
      desc: '',
      args: [],
    );
  }

  /// `Presenter`
  String get account_user_reference {
    return Intl.message(
      'Presenter',
      name: 'account_user_reference',
      desc: '',
      args: [],
    );
  }

  /// `If you don't have a referral, you can leave it blank and move on`
  String get account_if_not_user_reference {
    return Intl.message(
      'If you don\'t have a referral, you can leave it blank and move on',
      name: 'account_if_not_user_reference',
      desc: '',
      args: [],
    );
  }

  /// `No referrer found`
  String get account_not_found_user_reference {
    return Intl.message(
      'No referrer found',
      name: 'account_not_found_user_reference',
      desc: '',
      args: [],
    );
  }

  /// `Enter referrer code (if any)`
  String get account_type_user_reference {
    return Intl.message(
      'Enter referrer code (if any)',
      name: 'account_type_user_reference',
      desc: '',
      args: [],
    );
  }

  /// `Register account`
  String get account_register {
    return Intl.message(
      'Register account',
      name: 'account_register',
      desc: '',
      args: [],
    );
  }

  /// `Declare information`
  String get account_declare_information {
    return Intl.message(
      'Declare information',
      name: 'account_declare_information',
      desc: '',
      args: [],
    );
  }

  /// `Back`
  String get account_back {
    return Intl.message(
      'Back',
      name: 'account_back',
      desc: '',
      args: [],
    );
  }

  /// `Next`
  String get account_next {
    return Intl.message(
      'Next',
      name: 'account_next',
      desc: '',
      args: [],
    );
  }

  /// `Enter your phone number`
  String get account_enter_phone_number {
    return Intl.message(
      'Enter your phone number',
      name: 'account_enter_phone_number',
      desc: '',
      args: [],
    );
  }

  /// `Login`
  String get account_login {
    return Intl.message(
      'Login',
      name: 'account_login',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get account_password {
    return Intl.message(
      'Password',
      name: 'account_password',
      desc: '',
      args: [],
    );
  }

  /// `Enter account number or phone number`
  String get account_input_account {
    return Intl.message(
      'Enter account number or phone number',
      name: 'account_input_account',
      desc: '',
      args: [],
    );
  }

  /// `Enter account number or phone numberC`
  String get account_input_user {
    return Intl.message(
      'Enter account number or phone numberC',
      name: 'account_input_user',
      desc: '',
      args: [],
    );
  }

  /// `Please input Account number 116C`
  String get account_please_input_user {
    return Intl.message(
      'Please input Account number 116C',
      name: 'account_please_input_user',
      desc: '',
      args: [],
    );
  }

  /// `Email or phone number`
  String get account_input_email_or_phone {
    return Intl.message(
      'Email or phone number',
      name: 'account_input_email_or_phone',
      desc: '',
      args: [],
    );
  }

  /// `Please input email or phone number`
  String get account_please_input_email_or_phone {
    return Intl.message(
      'Please input email or phone number',
      name: 'account_please_input_email_or_phone',
      desc: '',
      args: [],
    );
  }

  /// `Phone or email`
  String get account_input_phone_or_email {
    return Intl.message(
      'Phone or email',
      name: 'account_input_phone_or_email',
      desc: '',
      args: [],
    );
  }

  /// `input phone or email`
  String get account_hint_input_phone_or_email {
    return Intl.message(
      'input phone or email',
      name: 'account_hint_input_phone_or_email',
      desc: '',
      args: [],
    );
  }

  /// `Please enter the OTP code that has been sent to your email or phone number`
  String get account_guide_input_email_or_phone {
    return Intl.message(
      'Please enter the OTP code that has been sent to your email or phone number',
      name: 'account_guide_input_email_or_phone',
      desc: '',
      args: [],
    );
  }

  /// `Date of birth`
  String get account_date_of_birth {
    return Intl.message(
      'Date of birth',
      name: 'account_date_of_birth',
      desc: '',
      args: [],
    );
  }

  /// `Input date of birth`
  String get account_hint_input_date_of_birth {
    return Intl.message(
      'Input date of birth',
      name: 'account_hint_input_date_of_birth',
      desc: '',
      args: [],
    );
  }

  /// `CCCD/HC/GPKD Number`
  String get account_cccd {
    return Intl.message(
      'CCCD/HC/GPKD Number',
      name: 'account_cccd',
      desc: '',
      args: [],
    );
  }

  /// `input CCCD`
  String get account_hint_input_cccd {
    return Intl.message(
      'input CCCD',
      name: 'account_hint_input_cccd',
      desc: '',
      args: [],
    );
  }

  /// `Forgot password`
  String get account_forgot_password {
    return Intl.message(
      'Forgot password',
      name: 'account_forgot_password',
      desc: '',
      args: [],
    );
  }

  /// `Create a new password`
  String get account_create_new_password {
    return Intl.message(
      'Create a new password',
      name: 'account_create_new_password',
      desc: '',
      args: [],
    );
  }

  /// `Please enter the information below to reinstall the password`
  String get account_please_input_info_for_reset_password {
    return Intl.message(
      'Please enter the information below to reinstall the password',
      name: 'account_please_input_info_for_reset_password',
      desc: '',
      args: [],
    );
  }

  /// `CCCD/HC/GPKD`
  String get account_id_number {
    return Intl.message(
      'CCCD/HC/GPKD',
      name: 'account_id_number',
      desc: '',
      args: [],
    );
  }

  /// `Enter CCCD number`
  String get account_input_id_number {
    return Intl.message(
      'Enter CCCD number',
      name: 'account_input_id_number',
      desc: '',
      args: [],
    );
  }

  /// `Create password`
  String get account_create_password {
    return Intl.message(
      'Create password',
      name: 'account_create_password',
      desc: '',
      args: [],
    );
  }

  /// `Import your phone number`
  String get account_input_phone_info {
    return Intl.message(
      'Import your phone number',
      name: 'account_input_phone_info',
      desc: '',
      args: [],
    );
  }

  /// `Import your email address`
  String get account_input_email_info {
    return Intl.message(
      'Import your email address',
      name: 'account_input_email_info',
      desc: '',
      args: [],
    );
  }

  /// `Phone`
  String get account_phone {
    return Intl.message(
      'Phone',
      name: 'account_phone',
      desc: '',
      args: [],
    );
  }

  /// `Email`
  String get account_email {
    return Intl.message(
      'Email',
      name: 'account_email',
      desc: '',
      args: [],
    );
  }

  /// `Email Address`
  String get account_hint_email {
    return Intl.message(
      'Email Address',
      name: 'account_hint_email',
      desc: '',
      args: [],
    );
  }

  /// `Phone number`
  String get account_phone_number {
    return Intl.message(
      'Phone number',
      name: 'account_phone_number',
      desc: '',
      args: [],
    );
  }

  /// `<EMAIL>`
  String get account_email_register {
    return Intl.message(
      '<EMAIL>',
      name: 'account_email_register',
      desc: '',
      args: [],
    );
  }

  /// `The system has sent OTP to your phone number. Please enter the OTP received`
  String get account_system_send_otp {
    return Intl.message(
      'The system has sent OTP to your phone number. Please enter the OTP received',
      name: 'account_system_send_otp',
      desc: '',
      args: [],
    );
  }

  /// `OTP code has an expiry date in`
  String get account_counting_otp {
    return Intl.message(
      'OTP code has an expiry date in',
      name: 'account_counting_otp',
      desc: '',
      args: [],
    );
  }

  /// `The OTP has expired. Please try again.`
  String get account_expried_otp {
    return Intl.message(
      'The OTP has expired. Please try again.',
      name: 'account_expried_otp',
      desc: '',
      args: [],
    );
  }

  /// `Resend OTP`
  String get account_re_enter_otp {
    return Intl.message(
      'Resend OTP',
      name: 'account_re_enter_otp',
      desc: '',
      args: [],
    );
  }

  /// `OTP code is incorrect, please try again`
  String get account_invalid_otp {
    return Intl.message(
      'OTP code is incorrect, please try again',
      name: 'account_invalid_otp',
      desc: '',
      args: [],
    );
  }

  /// `OTP code is incorrect, please try again`
  String get account_invalid_otp_account {
    return Intl.message(
      'OTP code is incorrect, please try again',
      name: 'account_invalid_otp_account',
      desc: '',
      args: [],
    );
  }

  /// `Enter your email`
  String get account_enter_email {
    return Intl.message(
      'Enter your email',
      name: 'account_enter_email',
      desc: '',
      args: [],
    );
  }

  /// `Set a password for your account`
  String get account_setup_pass {
    return Intl.message(
      'Set a password for your account',
      name: 'account_setup_pass',
      desc: '',
      args: [],
    );
  }

  /// `Enter password`
  String get account_enter_pass {
    return Intl.message(
      'Enter password',
      name: 'account_enter_pass',
      desc: '',
      args: [],
    );
  }

  /// `Re-enter password`
  String get account_re_enter_pass {
    return Intl.message(
      'Re-enter password',
      name: 'account_re_enter_pass',
      desc: '',
      args: [],
    );
  }

  /// `Please enter your password`
  String get account_please_enter_your_password {
    return Intl.message(
      'Please enter your password',
      name: 'account_please_enter_your_password',
      desc: '',
      args: [],
    );
  }

  /// `Current password`
  String get account_current_password {
    return Intl.message(
      'Current password',
      name: 'account_current_password',
      desc: '',
      args: [],
    );
  }

  /// `Enter the new password`
  String get account_enter_the_new_password {
    return Intl.message(
      'Enter the new password',
      name: 'account_enter_the_new_password',
      desc: '',
      args: [],
    );
  }

  /// `Length 8 - 50 characters`
  String get account_length_8_50_characters {
    return Intl.message(
      'Length 8 - 50 characters',
      name: 'account_length_8_50_characters',
      desc: '',
      args: [],
    );
  }

  /// `Minimum 1 capital letter`
  String get account_minimum_1_capital_letter {
    return Intl.message(
      'Minimum 1 capital letter',
      name: 'account_minimum_1_capital_letter',
      desc: '',
      args: [],
    );
  }

  /// `Minimum 1 character number`
  String get account_minimum_1_character_number {
    return Intl.message(
      'Minimum 1 character number',
      name: 'account_minimum_1_character_number',
      desc: '',
      args: [],
    );
  }

  /// `Does not contain space`
  String get account_does_not_contain_space {
    return Intl.message(
      'Does not contain space',
      name: 'account_does_not_contain_space',
      desc: '',
      args: [],
    );
  }

  /// `Password must contain at least 8 characters, at least 1 capital letter and 1 number`
  String get account_valid_pass {
    return Intl.message(
      'Password must contain at least 8 characters, at least 1 capital letter and 1 number',
      name: 'account_valid_pass',
      desc: '',
      args: [],
    );
  }

  /// `The entered passwords do not match. Please try again.`
  String get account_not_match_pass {
    return Intl.message(
      'The entered passwords do not match. Please try again.',
      name: 'account_not_match_pass',
      desc: '',
      args: [],
    );
  }

  /// `Change the password successfully`
  String get account_change_pass_success {
    return Intl.message(
      'Change the password successfully',
      name: 'account_change_pass_success',
      desc: '',
      args: [],
    );
  }

  /// `The password has been successfully changed. Please login again to use the application`
  String get account_change_pass_success_description {
    return Intl.message(
      'The password has been successfully changed. Please login again to use the application',
      name: 'account_change_pass_success_description',
      desc: '',
      args: [],
    );
  }

  /// `Message 'Invalid password. A valid password must be at least 8 characters with at least 1 uppercase letter and 1 number'`
  String get account_fail_enter_pass {
    return Intl.message(
      'Message \'Invalid password. A valid password must be at least 8 characters with at least 1 uppercase letter and 1 number\'',
      name: 'account_fail_enter_pass',
      desc: '',
      args: [],
    );
  }

  /// `Enter your bank account`
  String get account_enter_bank_acc {
    return Intl.message(
      'Enter your bank account',
      name: 'account_enter_bank_acc',
      desc: '',
      args: [],
    );
  }

  /// `Your bank account information`
  String get account_your_bank_info {
    return Intl.message(
      'Your bank account information',
      name: 'account_your_bank_info',
      desc: '',
      args: [],
    );
  }

  /// `Choose a bank`
  String get account_select_bank {
    return Intl.message(
      'Choose a bank',
      name: 'account_select_bank',
      desc: '',
      args: [],
    );
  }

  /// `Account Number`
  String get account_account_number {
    return Intl.message(
      'Account Number',
      name: 'account_account_number',
      desc: '',
      args: [],
    );
  }

  /// `Input account number`
  String get account_input_account_number {
    return Intl.message(
      'Input account number',
      name: 'account_input_account_number',
      desc: '',
      args: [],
    );
  }

  /// `Set bond enjoyment`
  String get account_set_bond_enjoyment {
    return Intl.message(
      'Set bond enjoyment',
      name: 'account_set_bond_enjoyment',
      desc: '',
      args: [],
    );
  }

  /// `Set fund enjoyment`
  String get account_set_fund_enjoyment {
    return Intl.message(
      'Set fund enjoyment',
      name: 'account_set_fund_enjoyment',
      desc: '',
      args: [],
    );
  }

  /// `Don't have VPBank account?`
  String get account_vp_bank_account {
    return Intl.message(
      'Don\'t have VPBank account?',
      name: 'account_vp_bank_account',
      desc: '',
      args: [],
    );
  }

  /// `Register now`
  String get account_register_now {
    return Intl.message(
      'Register now',
      name: 'account_register_now',
      desc: '',
      args: [],
    );
  }

  /// `Select securities account number`
  String get account_select_acc_number {
    return Intl.message(
      'Select securities account number',
      name: 'account_select_acc_number',
      desc: '',
      args: [],
    );
  }

  /// `Please select your stock trading account number`
  String get account_select_acc_number_guide {
    return Intl.message(
      'Please select your stock trading account number',
      name: 'account_select_acc_number_guide',
      desc: '',
      args: [],
    );
  }

  /// `Suggestions for you`
  String get account_suggest_acc_number {
    return Intl.message(
      'Suggestions for you',
      name: 'account_suggest_acc_number',
      desc: '',
      args: [],
    );
  }

  /// `Don't have a caregiver yet?`
  String get account_question_support {
    return Intl.message(
      'Don\'t have a caregiver yet?',
      name: 'account_question_support',
      desc: '',
      args: [],
    );
  }

  /// `I want to find a caregiver`
  String get account_dont_have_support {
    return Intl.message(
      'I want to find a caregiver',
      name: 'account_dont_have_support',
      desc: '',
      args: [],
    );
  }

  /// `I have a caregiver`
  String get account_have_support {
    return Intl.message(
      'I have a caregiver',
      name: 'account_have_support',
      desc: '',
      args: [],
    );
  }

  /// `I have no need`
  String get account_no_need_support {
    return Intl.message(
      'I have no need',
      name: 'account_no_need_support',
      desc: '',
      args: [],
    );
  }

  /// `Incorrect information`
  String get account_incorrect_information {
    return Intl.message(
      'Incorrect information',
      name: 'account_incorrect_information',
      desc: '',
      args: [],
    );
  }

  /// `The information you have just entered is incorrect, please try again.`
  String get account_incorrect_information_message {
    return Intl.message(
      'The information you have just entered is incorrect, please try again.',
      name: 'account_incorrect_information_message',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get account_close {
    return Intl.message(
      'Close',
      name: 'account_close',
      desc: '',
      args: [],
    );
  }

  /// `An error occurred, please try again later!`
  String get account_error {
    return Intl.message(
      'An error occurred, please try again later!',
      name: 'account_error',
      desc: '',
      args: [],
    );
  }

  /// `Touch ID not installed`
  String get account_no_touch_id {
    return Intl.message(
      'Touch ID not installed',
      name: 'account_no_touch_id',
      desc: '',
      args: [],
    );
  }

  /// `Face ID not installed`
  String get account_no_face_id {
    return Intl.message(
      'Face ID not installed',
      name: 'account_no_face_id',
      desc: '',
      args: [],
    );
  }

  /// `Login failde`
  String get account_login_failde {
    return Intl.message(
      'Login failde',
      name: 'account_login_failde',
      desc: '',
      args: [],
    );
  }

  /// `Fingerprint login has been disabled due to incorrect input multiple times. Please use password to login the app`
  String get account_disable_fingerprint {
    return Intl.message(
      'Fingerprint login has been disabled due to incorrect input multiple times. Please use password to login the app',
      name: 'account_disable_fingerprint',
      desc: '',
      args: [],
    );
  }

  /// `Setting`
  String get account_go_to_settings_button {
    return Intl.message(
      'Setting',
      name: 'account_go_to_settings_button',
      desc: '',
      args: [],
    );
  }

  /// `Biometric authentication is not set up on your device. Please either enable Touch ID or Face ID on your phone.`
  String get account_go_to_settings_description {
    return Intl.message(
      'Biometric authentication is not set up on your device. Please either enable Touch ID or Face ID on your phone.',
      name: 'account_go_to_settings_description',
      desc: '',
      args: [],
    );
  }

  /// `Scan your fingerprint (or face or whatever) to authenticate`
  String get account_fingerprint {
    return Intl.message(
      'Scan your fingerprint (or face or whatever) to authenticate',
      name: 'account_fingerprint',
      desc: '',
      args: [],
    );
  }

  /// `Please sign in to the app to activate with your fingerprint/faceid`
  String get account_active_fingerprint {
    return Intl.message(
      'Please sign in to the app to activate with your fingerprint/faceid',
      name: 'account_active_fingerprint',
      desc: '',
      args: [],
    );
  }

  /// `You have not set up your account's Touch ID on this device. Please login with password and set up Touch ID to use this function`
  String get account_message_no_touch_id {
    return Intl.message(
      'You have not set up your account\'s Touch ID on this device. Please login with password and set up Touch ID to use this function',
      name: 'account_message_no_touch_id',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a password`
  String get account_enter_password {
    return Intl.message(
      'Please enter a password',
      name: 'account_enter_password',
      desc: '',
      args: [],
    );
  }

  /// `Please enter your password`
  String get account_enter_your_password {
    return Intl.message(
      'Please enter your password',
      name: 'account_enter_your_password',
      desc: '',
      args: [],
    );
  }

  /// `Please enter your PIN`
  String get account_enter_your_pin {
    return Intl.message(
      'Please enter your PIN',
      name: 'account_enter_your_pin',
      desc: '',
      args: [],
    );
  }

  /// `Please enter account number or phone number`
  String get account_enter_username {
    return Intl.message(
      'Please enter account number or phone number',
      name: 'account_enter_username',
      desc: '',
      args: [],
    );
  }

  /// `Search by bank name`
  String get account_search_bank {
    return Intl.message(
      'Search by bank name',
      name: 'account_search_bank',
      desc: '',
      args: [],
    );
  }

  /// `Front of Identity card`
  String get account_font_identi_card {
    return Intl.message(
      'Front of Identity card',
      name: 'account_font_identi_card',
      desc: '',
      args: [],
    );
  }

  /// `Back of Identity card`
  String get account_back_identi_card {
    return Intl.message(
      'Back of Identity card',
      name: 'account_back_identi_card',
      desc: '',
      args: [],
    );
  }

  /// `Signature`
  String get account_signature {
    return Intl.message(
      'Signature',
      name: 'account_signature',
      desc: '',
      args: [],
    );
  }

  /// `Signature`
  String get account_signature_title {
    return Intl.message(
      'Signature',
      name: 'account_signature_title',
      desc: '',
      args: [],
    );
  }

  /// `Note:\n- Please take photos of the same identity card\n- Please take photos clearly, without losing angles and overexposing important information on the card`
  String get account_note_upload {
    return Intl.message(
      'Note:\\n- Please take photos of the same identity card\\n- Please take photos clearly, without losing angles and overexposing important information on the card',
      name: 'account_note_upload',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get account_cancel {
    return Intl.message(
      'Cancel',
      name: 'account_cancel',
      desc: '',
      args: [],
    );
  }

  /// `Make sure the card is captured clearly, the information is not blurred or glare`
  String get account_tutorial_ocr {
    return Intl.message(
      'Make sure the card is captured clearly, the information is not blurred or glare',
      name: 'account_tutorial_ocr',
      desc: '',
      args: [],
    );
  }

  /// `Ensure captured information is clear, without blur or glare`
  String get account_tutorial_ocr2 {
    return Intl.message(
      'Ensure captured information is clear, without blur or glare',
      name: 'account_tutorial_ocr2',
      desc: '',
      args: [],
    );
  }

  /// `The system is processing, please wait...`
  String get account_loading_wait {
    return Intl.message(
      'The system is processing, please wait...',
      name: 'account_loading_wait',
      desc: '',
      args: [],
    );
  }

  /// `Unrecognizable`
  String get account_unrecognizable {
    return Intl.message(
      'Unrecognizable',
      name: 'account_unrecognizable',
      desc: '',
      args: [],
    );
  }

  /// `The photo you just took cannot be used. Please check if the information is blurry, in a well-lit environment and try again`
  String get account_ocr_fail_description {
    return Intl.message(
      'The photo you just took cannot be used. Please check if the information is blurry, in a well-lit environment and try again',
      name: 'account_ocr_fail_description',
      desc: '',
      args: [],
    );
  }

  /// `Retry`
  String get account_retry {
    return Intl.message(
      'Retry',
      name: 'account_retry',
      desc: '',
      args: [],
    );
  }

  /// `Select account`
  String get account_select_account {
    return Intl.message(
      'Select account',
      name: 'account_select_account',
      desc: '',
      args: [],
    );
  }

  /// `Invalid username`
  String get account_invalid_username {
    return Intl.message(
      'Invalid username',
      name: 'account_invalid_username',
      desc: '',
      args: [],
    );
  }

  /// `Invalid phone or email`
  String get account_invalid_phone_or_email {
    return Intl.message(
      'Invalid phone or email',
      name: 'account_invalid_phone_or_email',
      desc: '',
      args: [],
    );
  }

  /// `Personal information`
  String get account_info_check_guide {
    return Intl.message(
      'Personal information',
      name: 'account_info_check_guide',
      desc: '',
      args: [],
    );
  }

  /// `If your above information is not correct with the information on CCCD, you can correct your information or go to VPBank Securities counter for assistance`
  String get account_guide_edit_info {
    return Intl.message(
      'If your above information is not correct with the information on CCCD, you can correct your information or go to VPBank Securities counter for assistance',
      name: 'account_guide_edit_info',
      desc: '',
      args: [],
    );
  }

  /// `Edit information`
  String get account_edit_info {
    return Intl.message(
      'Edit information',
      name: 'account_edit_info',
      desc: '',
      args: [],
    );
  }

  /// `Edit info`
  String get account_edit_info_short {
    return Intl.message(
      'Edit info',
      name: 'account_edit_info_short',
      desc: '',
      args: [],
    );
  }

  /// `You can manually edit the information. However, your account may be temporarily restricted until verification (3-5 days).`
  String get account_edit_info_guide {
    return Intl.message(
      'You can manually edit the information. However, your account may be temporarily restricted until verification (3-5 days).',
      name: 'account_edit_info_guide',
      desc: '',
      args: [],
    );
  }

  /// `Edit`
  String get account_edit {
    return Intl.message(
      'Edit',
      name: 'account_edit',
      desc: '',
      args: [],
    );
  }

  /// `Full name`
  String get account_full_name {
    return Intl.message(
      'Full name',
      name: 'account_full_name',
      desc: '',
      args: [],
    );
  }

  /// `Birthday`
  String get account_birthday {
    return Intl.message(
      'Birthday',
      name: 'account_birthday',
      desc: '',
      args: [],
    );
  }

  /// `ID Card`
  String get account_identify_card {
    return Intl.message(
      'ID Card',
      name: 'account_identify_card',
      desc: '',
      args: [],
    );
  }

  /// `Take a photo verification`
  String get account_take_a_photo_verification {
    return Intl.message(
      'Take a photo verification',
      name: 'account_take_a_photo_verification',
      desc: '',
      args: [],
    );
  }

  /// `Update citizen identification information`
  String get account_update_cccd {
    return Intl.message(
      'Update citizen identification information',
      name: 'account_update_cccd',
      desc: '',
      args: [],
    );
  }

  /// `Issued Date`
  String get account_issued_date_card {
    return Intl.message(
      'Issued Date',
      name: 'account_issued_date_card',
      desc: '',
      args: [],
    );
  }

  /// `Place Issued`
  String get account_place_of_issued_card {
    return Intl.message(
      'Place Issued',
      name: 'account_place_of_issued_card',
      desc: '',
      args: [],
    );
  }

  /// `Permanent Address`
  String get account_permanent_address {
    return Intl.message(
      'Permanent Address',
      name: 'account_permanent_address',
      desc: '',
      args: [],
    );
  }

  /// `Upload Profile`
  String get account_upload_profile {
    return Intl.message(
      'Upload Profile',
      name: 'account_upload_profile',
      desc: '',
      args: [],
    );
  }

  /// `Face Recognition`
  String get account_e_kyc_title {
    return Intl.message(
      'Face Recognition',
      name: 'account_e_kyc_title',
      desc: '',
      args: [],
    );
  }

  /// `VPBank Securities needs data about your face to ensure security and information verification.`
  String get account_e_kyc_guide {
    return Intl.message(
      'VPBank Securities needs data about your face to ensure security and information verification.',
      name: 'account_e_kyc_guide',
      desc: '',
      args: [],
    );
  }

  /// `Start face recognition`
  String get account_start_ekyc {
    return Intl.message(
      'Start face recognition',
      name: 'account_start_ekyc',
      desc: '',
      args: [],
    );
  }

  /// `Please fit your face into the frame`
  String get account_match_face {
    return Intl.message(
      'Please fit your face into the frame',
      name: 'account_match_face',
      desc: '',
      args: [],
    );
  }

  /// `Face detection failed, please try again`
  String get account_verify_face_fail {
    return Intl.message(
      'Face detection failed, please try again',
      name: 'account_verify_face_fail',
      desc: '',
      args: [],
    );
  }

  /// `Face authentication successful`
  String get account_verify_face_success {
    return Intl.message(
      'Face authentication successful',
      name: 'account_verify_face_success',
      desc: '',
      args: [],
    );
  }

  /// `Contract & Terms`
  String get account_contract_and_term {
    return Intl.message(
      'Contract & Terms',
      name: 'account_contract_and_term',
      desc: '',
      args: [],
    );
  }

  /// `Change password`
  String get account_change_password {
    return Intl.message(
      'Change password',
      name: 'account_change_password',
      desc: '',
      args: [],
    );
  }

  /// `New password`
  String get account_new_password {
    return Intl.message(
      'New password',
      name: 'account_new_password',
      desc: '',
      args: [],
    );
  }

  /// `Confirm new password`
  String get account_confirm_new_password {
    return Intl.message(
      'Confirm new password',
      name: 'account_confirm_new_password',
      desc: '',
      args: [],
    );
  }

  /// `Password changed successfully`
  String get account_success_change_password {
    return Intl.message(
      'Password changed successfully',
      name: 'account_success_change_password',
      desc: '',
      args: [],
    );
  }

  /// `Change the PIN`
  String get account_change_pin {
    return Intl.message(
      'Change the PIN',
      name: 'account_change_pin',
      desc: '',
      args: [],
    );
  }

  /// `Change PIN`
  String get account_change_the_pin {
    return Intl.message(
      'Change PIN',
      name: 'account_change_the_pin',
      desc: '',
      args: [],
    );
  }

  /// `Please enter the PIN to continue`
  String get account_change_pin_for_continue {
    return Intl.message(
      'Please enter the PIN to continue',
      name: 'account_change_pin_for_continue',
      desc: '',
      args: [],
    );
  }

  /// `PIN is sent`
  String get account_pin_is_sent {
    return Intl.message(
      'PIN is sent',
      name: 'account_pin_is_sent',
      desc: '',
      args: [],
    );
  }

  /// `New PIN code`
  String get account_new_pin {
    return Intl.message(
      'New PIN code',
      name: 'account_new_pin',
      desc: '',
      args: [],
    );
  }

  /// `Enter the new PIN`
  String get account_enter_the_new_pin {
    return Intl.message(
      'Enter the new PIN',
      name: 'account_enter_the_new_pin',
      desc: '',
      args: [],
    );
  }

  /// `Length 6 - 30 characters`
  String get account_length_6_30_characters {
    return Intl.message(
      'Length 6 - 30 characters',
      name: 'account_length_6_30_characters',
      desc: '',
      args: [],
    );
  }

  /// `Do not contain special characters`
  String get account_do_not_contain_special_characters {
    return Intl.message(
      'Do not contain special characters',
      name: 'account_do_not_contain_special_characters',
      desc: '',
      args: [],
    );
  }

  /// `Forgot the PIN`
  String get account_forgot_the_pin {
    return Intl.message(
      'Forgot the PIN',
      name: 'account_forgot_the_pin',
      desc: '',
      args: [],
    );
  }

  /// `PIN does not match each other. Please try again`
  String get account_pin_not_match {
    return Intl.message(
      'PIN does not match each other. Please try again',
      name: 'account_pin_not_match',
      desc: '',
      args: [],
    );
  }

  /// `Change the battery successfully`
  String get account_change_pin_successfully {
    return Intl.message(
      'Change the battery successfully',
      name: 'account_change_pin_successfully',
      desc: '',
      args: [],
    );
  }

  /// `Your battery has been successfully changed`
  String get account_change_pin_successfully_des {
    return Intl.message(
      'Your battery has been successfully changed',
      name: 'account_change_pin_successfully_des',
      desc: '',
      args: [],
    );
  }

  /// `OTP code is incorrect, please try again`
  String get account_wrong_otp {
    return Intl.message(
      'OTP code is incorrect, please try again',
      name: 'account_wrong_otp',
      desc: '',
      args: [],
    );
  }

  /// `Resend OTP`
  String get account_re_send_otp {
    return Intl.message(
      'Resend OTP',
      name: 'account_re_send_otp',
      desc: '',
      args: [],
    );
  }

  /// `Gender`
  String get account_gender {
    return Intl.message(
      'Gender',
      name: 'account_gender',
      desc: '',
      args: [],
    );
  }

  /// `Female`
  String get account_gender_female {
    return Intl.message(
      'Female',
      name: 'account_gender_female',
      desc: '',
      args: [],
    );
  }

  /// `Male`
  String get account_gender_male {
    return Intl.message(
      'Male',
      name: 'account_gender_male',
      desc: '',
      args: [],
    );
  }

  /// `Touch ID not supported`
  String get account_no_support {
    return Intl.message(
      'Touch ID not supported',
      name: 'account_no_support',
      desc: '',
      args: [],
    );
  }

  /// `Biometric invalid`
  String get account_biometric_invalid {
    return Intl.message(
      'Biometric invalid',
      name: 'account_biometric_invalid',
      desc: '',
      args: [],
    );
  }

  /// `Go to home screen`
  String get account_goto_main_page {
    return Intl.message(
      'Go to home screen',
      name: 'account_goto_main_page',
      desc: '',
      args: [],
    );
  }

  /// `Contract`
  String get account_contract {
    return Intl.message(
      'Contract',
      name: 'account_contract',
      desc: '',
      args: [],
    );
  }

  /// `Current residential address`
  String get account_current_address {
    return Intl.message(
      'Current residential address',
      name: 'account_current_address',
      desc: '',
      args: [],
    );
  }

  /// `US Citizen`
  String get account_us_citizen {
    return Intl.message(
      'US Citizen',
      name: 'account_us_citizen',
      desc: '',
      args: [],
    );
  }

  /// `Yes`
  String get account_yes {
    return Intl.message(
      'Yes',
      name: 'account_yes',
      desc: '',
      args: [],
    );
  }

  /// `No`
  String get account_no {
    return Intl.message(
      'No',
      name: 'account_no',
      desc: '',
      args: [],
    );
  }

  /// `Invalid account number`
  String get account_error_invalid_phone {
    return Intl.message(
      'Invalid account number',
      name: 'account_error_invalid_phone',
      desc: '',
      args: [],
    );
  }

  /// `Invalid Email`
  String get account_error_invalid_email {
    return Intl.message(
      'Invalid Email',
      name: 'account_error_invalid_email',
      desc: '',
      args: [],
    );
  }

  /// `Bank information`
  String get account_bank_info {
    return Intl.message(
      'Bank information',
      name: 'account_bank_info',
      desc: '',
      args: [],
    );
  }

  /// `Bank`
  String get account_bank {
    return Intl.message(
      'Bank',
      name: 'account_bank',
      desc: '',
      args: [],
    );
  }

  /// `Branch`
  String get account_bank_branch {
    return Intl.message(
      'Branch',
      name: 'account_bank_branch',
      desc: '',
      args: [],
    );
  }

  /// `Account Holder Name`
  String get account_account_holder_name {
    return Intl.message(
      'Account Holder Name',
      name: 'account_account_holder_name',
      desc: '',
      args: [],
    );
  }

  /// `Edit bank accounts`
  String get account_edit_bank_acc {
    return Intl.message(
      'Edit bank accounts',
      name: 'account_edit_bank_acc',
      desc: '',
      args: [],
    );
  }

  /// `Agree`
  String get account_agree {
    return Intl.message(
      'Agree',
      name: 'account_agree',
      desc: '',
      args: [],
    );
  }

  /// `Success`
  String get account_success {
    return Intl.message(
      'Success',
      name: 'account_success',
      desc: '',
      args: [],
    );
  }

  /// `PIN and password created successfully`
  String get account_success_change_pass_pin {
    return Intl.message(
      'PIN and password created successfully',
      name: 'account_success_change_pass_pin',
      desc: '',
      args: [],
    );
  }

  /// `Branch Search`
  String get account_find_branch_bank {
    return Intl.message(
      'Branch Search',
      name: 'account_find_branch_bank',
      desc: '',
      args: [],
    );
  }

  /// `Call Hotline`
  String get account_support_call_hotline {
    return Intl.message(
      'Call Hotline',
      name: 'account_support_call_hotline',
      desc: '',
      args: [],
    );
  }

  /// `Website`
  String get account_support_website {
    return Intl.message(
      'Website',
      name: 'account_support_website',
      desc: '',
      args: [],
    );
  }

  /// `Chat with support`
  String get account_support_zalo {
    return Intl.message(
      'Chat with support',
      name: 'account_support_zalo',
      desc: '',
      args: [],
    );
  }

  /// `VPBank Securities`
  String get account_support_zalo2 {
    return Intl.message(
      'VPBank Securities',
      name: 'account_support_zalo2',
      desc: '',
      args: [],
    );
  }

  /// `Money laundering prevention`
  String get account_money_laundering_prevention {
    return Intl.message(
      'Money laundering prevention',
      name: 'account_money_laundering_prevention',
      desc: '',
      args: [],
    );
  }

  /// `Purpose of using the account`
  String get account_intended_use {
    return Intl.message(
      'Purpose of using the account',
      name: 'account_intended_use',
      desc: '',
      args: [],
    );
  }

  /// `Job`
  String get account_job {
    return Intl.message(
      'Job',
      name: 'account_job',
      desc: '',
      args: [],
    );
  }

  /// `Position`
  String get account_position {
    return Intl.message(
      'Position',
      name: 'account_position',
      desc: '',
      args: [],
    );
  }

  /// `Notification`
  String get account_notification {
    return Intl.message(
      'Notification',
      name: 'account_notification',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get account_continue_text {
    return Intl.message(
      'Continue',
      name: 'account_continue_text',
      desc: '',
      args: [],
    );
  }

  /// `Customer confirmation:`
  String get account_customer_confirmation {
    return Intl.message(
      'Customer confirmation:',
      name: 'account_customer_confirmation',
      desc: '',
      args: [],
    );
  }

  /// `Not a US citizen, do not hold a US Green Card, and not a resident in the US.`
  String get account_customer_confirmation1 {
    return Intl.message(
      'Not a US citizen, do not hold a US Green Card, and not a resident in the US.',
      name: 'account_customer_confirmation1',
      desc: '',
      args: [],
    );
  }

  /// `No other beneficial ownership.`
  String get account_customer_confirmation2 {
    return Intl.message(
      'No other beneficial ownership.',
      name: 'account_customer_confirmation2',
      desc: '',
      args: [],
    );
  }

  /// `No trustee involved.`
  String get account_customer_confirmation3 {
    return Intl.message(
      'No trustee involved.',
      name: 'account_customer_confirmation3',
      desc: '',
      args: [],
    );
  }

  /// `Your phone number is already linked to an account at VPBank. Please click 'Understood' to proceed with opening a securities account.`
  String get account_phone_number_used {
    return Intl.message(
      'Your phone number is already linked to an account at VPBank. Please click \'Understood\' to proceed with opening a securities account.',
      name: 'account_phone_number_used',
      desc: '',
      args: [],
    );
  }

  /// `You already have registration information at VPBank. Please click 'Understood' to continue opening a securities account.`
  String get account_exist_account_bank {
    return Intl.message(
      'You already have registration information at VPBank. Please click \'Understood\' to continue opening a securities account.',
      name: 'account_exist_account_bank',
      desc: '',
      args: [],
    );
  }

  /// `Open a VPBank bank account`
  String get account_open_an_account {
    return Intl.message(
      'Open a VPBank bank account',
      name: 'account_open_an_account',
      desc: '',
      args: [],
    );
  }

  /// `Please register with your CCCD number to open both VPBank securities and banking accounts. Or click 'Continue' to open a securities account with your ID card.`
  String get account_document_invalid {
    return Intl.message(
      'Please register with your CCCD number to open both VPBank securities and banking accounts. Or click \'Continue\' to open a securities account with your ID card.',
      name: 'account_document_invalid',
      desc: '',
      args: [],
    );
  }

  /// `Exceeded daily OTP limit`
  String get account_over_gen_otp {
    return Intl.message(
      'Exceeded daily OTP limit',
      name: 'account_over_gen_otp',
      desc: '',
      args: [],
    );
  }

  /// `You have reached the daily OTP creation limit and exceeded the maximum attempts. Please try again tomorrow.`
  String get account_create_over_otp {
    return Intl.message(
      'You have reached the daily OTP creation limit and exceeded the maximum attempts. Please try again tomorrow.',
      name: 'account_create_over_otp',
      desc: '',
      args: [],
    );
  }

  /// `3 minutes eKYC\nAccount is available`
  String get account_splash1_1 {
    return Intl.message(
      '3 minutes eKYC\nAccount is available',
      name: 'account_splash1_1',
      desc: '',
      args: [],
    );
  }

  /// `\nInstant transaction`
  String get account_splash1_2 {
    return Intl.message(
      '\nInstant transaction',
      name: 'account_splash1_2',
      desc: '',
      args: [],
    );
  }

  /// `Only with\nPhone with camera\nand valid ID/CCCD`
  String get account_splash2 {
    return Intl.message(
      'Only with\nPhone with camera\nand valid ID/CCCD',
      name: 'account_splash2',
      desc: '',
      args: [],
    );
  }

  /// `The phone number is used to log in to the account and receive OTP in the system of VPBank Securities.`
  String get account_guide_input_phone {
    return Intl.message(
      'The phone number is used to log in to the account and receive OTP in the system of VPBank Securities.',
      name: 'account_guide_input_phone',
      desc: '',
      args: [],
    );
  }

  /// `Phone numbers that can be used`
  String get account_success_phone {
    return Intl.message(
      'Phone numbers that can be used',
      name: 'account_success_phone',
      desc: '',
      args: [],
    );
  }

  /// `Invalid phone number. Phone number must start with 0 up to 10 numbers`
  String get account_wrong_phone {
    return Intl.message(
      'Invalid phone number. Phone number must start with 0 up to 10 numbers',
      name: 'account_wrong_phone',
      desc: '',
      args: [],
    );
  }

  /// `Email is used to:`
  String get account_check_email_guide_title {
    return Intl.message(
      'Email is used to:',
      name: 'account_check_email_guide_title',
      desc: '',
      args: [],
    );
  }

  /// `Receive transaction and asset alerts`
  String get account_check_email_guide_content1 {
    return Intl.message(
      'Receive transaction and asset alerts',
      name: 'account_check_email_guide_content1',
      desc: '',
      args: [],
    );
  }

  /// `Get market information, investment recommendations`
  String get account_check_email_guide_content2 {
    return Intl.message(
      'Get market information, investment recommendations',
      name: 'account_check_email_guide_content2',
      desc: '',
      args: [],
    );
  }

  /// `Update new products and services, offers`
  String get account_check_email_guide_content3 {
    return Intl.message(
      'Update new products and services, offers',
      name: 'account_check_email_guide_content3',
      desc: '',
      args: [],
    );
  }

  /// `Enhance account security`
  String get account_check_email_guide_content4 {
    return Intl.message(
      'Enhance account security',
      name: 'account_check_email_guide_content4',
      desc: '',
      args: [],
    );
  }

  /// `Input OTP`
  String get account_input_otp {
    return Intl.message(
      'Input OTP',
      name: 'account_input_otp',
      desc: '',
      args: [],
    );
  }

  /// `Verify`
  String get account_verify {
    return Intl.message(
      'Verify',
      name: 'account_verify',
      desc: '',
      args: [],
    );
  }

  /// `Email information`
  String get account_info_email {
    return Intl.message(
      'Email information',
      name: 'account_info_email',
      desc: '',
      args: [],
    );
  }

  /// `No caregiver found`
  String get account_not_found_support {
    return Intl.message(
      'No caregiver found',
      name: 'account_not_found_support',
      desc: '',
      args: [],
    );
  }

  /// `Presenter`
  String get account_presenter {
    return Intl.message(
      'Presenter',
      name: 'account_presenter',
      desc: '',
      args: [],
    );
  }

  /// `Have your CCCD/ID card ready. Make sure:`
  String get account_ekyc_title_1 {
    return Intl.message(
      'Have your CCCD/ID card ready. Make sure:',
      name: 'account_ekyc_title_1',
      desc: '',
      args: [],
    );
  }

  /// `Use the original and valid CCCD/ID card.`
  String get account_ekyc_description_1 {
    return Intl.message(
      'Use the original and valid CCCD/ID card.',
      name: 'account_ekyc_description_1',
      desc: '',
      args: [],
    );
  }

  /// `Place the document flat within the frame.`
  String get account_ekyc_description_2 {
    return Intl.message(
      'Place the document flat within the frame.',
      name: 'account_ekyc_description_2',
      desc: '',
      args: [],
    );
  }

  /// `Make sure all information on the document is clear and\nreadable. Avoid dark, blurry, or glared photos.`
  String get account_ekyc_description_3 {
    return Intl.message(
      'Make sure all information on the document is clear and\nreadable. Avoid dark, blurry, or glared photos.',
      name: 'account_ekyc_description_3',
      desc: '',
      args: [],
    );
  }

  /// `VPBank Securities will also require facial data to ensure identity and security.`
  String get account_ekyc_description_4 {
    return Intl.message(
      'VPBank Securities will also require facial data to ensure identity and security.',
      name: 'account_ekyc_description_4',
      desc: '',
      args: [],
    );
  }

  /// `A photo of your signature will be used to\nverify and enhance account security.`
  String get account_signature_description_1 {
    return Intl.message(
      'A photo of your signature will be used to\nverify and enhance account security.',
      name: 'account_signature_description_1',
      desc: '',
      args: [],
    );
  }

  /// `Please prepare your signature on a piece of\npaper so we can take a photo.`
  String get account_signature_description_2 {
    return Intl.message(
      'Please prepare your signature on a piece of\npaper so we can take a photo.',
      name: 'account_signature_description_2',
      desc: '',
      args: [],
    );
  }

  /// `Your signature photo`
  String get account_your_signature {
    return Intl.message(
      'Your signature photo',
      name: 'account_your_signature',
      desc: '',
      args: [],
    );
  }

  /// `Front`
  String get account_front {
    return Intl.message(
      'Front',
      name: 'account_front',
      desc: '',
      args: [],
    );
  }

  /// `Behind`
  String get account_behind {
    return Intl.message(
      'Behind',
      name: 'account_behind',
      desc: '',
      args: [],
    );
  }

  /// `Start taking photos`
  String get account_start_taking_photos {
    return Intl.message(
      'Start taking photos',
      name: 'account_start_taking_photos',
      desc: '',
      args: [],
    );
  }

  /// `Make sure the card is clearly captured and the information is not blurry or glared.`
  String get account_tutorial_ocr_1 {
    return Intl.message(
      'Make sure the card is clearly captured and the information is not blurry or glared.',
      name: 'account_tutorial_ocr_1',
      desc: '',
      args: [],
    );
  }

  /// `Ensure captured information is clear, without blur or glare.`
  String get account_tutorial_ocr_2 {
    return Intl.message(
      'Ensure captured information is clear, without blur or glare.',
      name: 'account_tutorial_ocr_2',
      desc: '',
      args: [],
    );
  }

  /// `Vui lòng đăng nhập lại để tiếp tục sử dụng dịch vụ`
  String get account_requestReLogin {
    return Intl.message(
      'Vui lòng đăng nhập lại để tiếp tục sử dụng dịch vụ',
      name: 'account_requestReLogin',
      desc: '',
      args: [],
    );
  }

  /// `Phiên đăng nhập hết hạn`
  String get account_sessionExpire {
    return Intl.message(
      'Phiên đăng nhập hết hạn',
      name: 'account_sessionExpire',
      desc: '',
      args: [],
    );
  }

  /// `Phiên đăng ký hết hạn`
  String get account_sessionRegisterExprie {
    return Intl.message(
      'Phiên đăng ký hết hạn',
      name: 'account_sessionRegisterExprie',
      desc: '',
      args: [],
    );
  }

  /// `Smart OTP not registered`
  String get account_unregister_smart_otp_title {
    return Intl.message(
      'Smart OTP not registered',
      name: 'account_unregister_smart_otp_title',
      desc: '',
      args: [],
    );
  }

  /// `You have not registered Smart OTP on this device. Please log in and go to Security / Smart OTP to register.`
  String get account_unregister_smart_otp_content {
    return Intl.message(
      'You have not registered Smart OTP on this device. Please log in and go to Security / Smart OTP to register.',
      name: 'account_unregister_smart_otp_content',
      desc: '',
      args: [],
    );
  }

  /// `Full name must not be blank`
  String get account_valid_name_empty {
    return Intl.message(
      'Full name must not be blank',
      name: 'account_valid_name_empty',
      desc: '',
      args: [],
    );
  }

  /// `Invalid full name. Full name must not include numbers or special characters`
  String get account_valid_name_wrong {
    return Intl.message(
      'Invalid full name. Full name must not include numbers or special characters',
      name: 'account_valid_name_wrong',
      desc: '',
      args: [],
    );
  }

  /// `ID Card number cannot be left blank`
  String get account_valid_id_card_empty {
    return Intl.message(
      'ID Card number cannot be left blank',
      name: 'account_valid_id_card_empty',
      desc: '',
      args: [],
    );
  }

  /// `ID Card number does not include letters, special characters and spaces`
  String get account_valid_id_card_special {
    return Intl.message(
      'ID Card number does not include letters, special characters and spaces',
      name: 'account_valid_id_card_special',
      desc: '',
      args: [],
    );
  }

  /// `Invalid ID number`
  String get account_valid_id_wrong {
    return Intl.message(
      'Invalid ID number',
      name: 'account_valid_id_wrong',
      desc: '',
      args: [],
    );
  }

  /// `Place of issue cannot be left blank`
  String get account_valid_id_place {
    return Intl.message(
      'Place of issue cannot be left blank',
      name: 'account_valid_id_place',
      desc: '',
      args: [],
    );
  }

  /// `Permanent address cannot be left blank`
  String get account_valid_per_address {
    return Intl.message(
      'Permanent address cannot be left blank',
      name: 'account_valid_per_address',
      desc: '',
      args: [],
    );
  }

  /// `Current residential address must be at least 15 characters`
  String get account_valid_current_address {
    return Intl.message(
      'Current residential address must be at least 15 characters',
      name: 'account_valid_current_address',
      desc: '',
      args: [],
    );
  }

  /// `Currently, VPBank cannot provide services for you. Please contact the care hotline customer for support.`
  String get account_note_open_an_account_1 {
    return Intl.message(
      'Currently, VPBank cannot provide services for you. Please contact the care hotline customer for support.',
      name: 'account_note_open_an_account_1',
      desc: '',
      args: [],
    );
  }

  /// `Click 'Understood' to continue opening a stock account.`
  String get account_note_open_an_account_2 {
    return Intl.message(
      'Click \'Understood\' to continue opening a stock account.',
      name: 'account_note_open_an_account_2',
      desc: '',
      args: [],
    );
  }

  /// `Front side image is invalid. Please try again.`
  String get account_wrong_front_id_card {
    return Intl.message(
      'Front side image is invalid. Please try again.',
      name: 'account_wrong_front_id_card',
      desc: '',
      args: [],
    );
  }

  /// `Back side image is invalid. Please try again.`
  String get account_wrong_back_id_card {
    return Intl.message(
      'Back side image is invalid. Please try again.',
      name: 'account_wrong_back_id_card',
      desc: '',
      args: [],
    );
  }

  /// `Note`
  String get account_note {
    return Intl.message(
      'Note',
      name: 'account_note',
      desc: '',
      args: [],
    );
  }

  /// `The bank account holder information must match the information on your CCCD/ID card`
  String get account_sign_up_bank_note {
    return Intl.message(
      'The bank account holder information must match the information on your CCCD/ID card',
      name: 'account_sign_up_bank_note',
      desc: '',
      args: [],
    );
  }

  /// `No results were found`
  String get account_no_result_search {
    return Intl.message(
      'No results were found',
      name: 'account_no_result_search',
      desc: '',
      args: [],
    );
  }

  /// `A photo of your signature will be used to verify and enhance account security.`
  String get account_signature_description1 {
    return Intl.message(
      'A photo of your signature will be used to verify and enhance account security.',
      name: 'account_signature_description1',
      desc: '',
      args: [],
    );
  }

  /// `Please prepare your signature on a piece of paper so we can take a photo.`
  String get account_signature_description2 {
    return Intl.message(
      'Please prepare your signature on a piece of paper so we can take a photo.',
      name: 'account_signature_description2',
      desc: '',
      args: [],
    );
  }

  /// `Cancel account registration?`
  String get account_cancel_register_title {
    return Intl.message(
      'Cancel account registration?',
      name: 'account_cancel_register_title',
      desc: '',
      args: [],
    );
  }

  /// `You did`
  String get account_cancel_register_content1 {
    return Intl.message(
      'You did',
      name: 'account_cancel_register_content1',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to cancel the subscription?`
  String get account_cancel_register_content2 {
    return Intl.message(
      'Are you sure you want to cancel the subscription?',
      name: 'account_cancel_register_content2',
      desc: '',
      args: [],
    );
  }

  /// `Cancel subscription`
  String get account_cancel_register {
    return Intl.message(
      'Cancel subscription',
      name: 'account_cancel_register',
      desc: '',
      args: [],
    );
  }

  /// `Find account number automatically`
  String get account_auto_find_acc {
    return Intl.message(
      'Find account number automatically',
      name: 'account_auto_find_acc',
      desc: '',
      args: [],
    );
  }

  /// `By pressing`
  String get account_contract_note1 {
    return Intl.message(
      'By pressing',
      name: 'account_contract_note1',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get account_contract_note2 {
    return Intl.message(
      'Confirm',
      name: 'account_contract_note2',
      desc: '',
      args: [],
    );
  }

  /// `, you confirm that you have read, understood and signed the Account Opening Agreement at VPBank Securities. Simultaneously agree for VPBankS to share customer's data and information and customer's securities trading account with VPBank Partners.`
  String get account_contract_note3 {
    return Intl.message(
      ', you confirm that you have read, understood and signed the Account Opening Agreement at VPBank Securities. Simultaneously agree for VPBankS to share customer\'s data and information and customer\'s securities trading account with VPBank Partners.',
      name: 'account_contract_note3',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Caregiver information`
  String get account_confirm_supporter {
    return Intl.message(
      'Confirm Caregiver information',
      name: 'account_confirm_supporter',
      desc: '',
      args: [],
    );
  }

  /// `Depository account number`
  String get account_depository_acc {
    return Intl.message(
      'Depository account number',
      name: 'account_depository_acc',
      desc: '',
      args: [],
    );
  }

  /// `Broker not found`
  String get account_not_found_supporter_title {
    return Intl.message(
      'Broker not found',
      name: 'account_not_found_supporter_title',
      desc: '',
      args: [],
    );
  }

  /// `The care worker account number you just entered does not exist on the system. Please try again.`
  String get account_not_found_supporter_content {
    return Intl.message(
      'The care worker account number you just entered does not exist on the system. Please try again.',
      name: 'account_not_found_supporter_content',
      desc: '',
      args: [],
    );
  }

  /// `Caring staff`
  String get account_caring_staff {
    return Intl.message(
      'Caring staff',
      name: 'account_caring_staff',
      desc: '',
      args: [],
    );
  }

  /// `Take a profile photo`
  String get account_capture_profile {
    return Intl.message(
      'Take a profile photo',
      name: 'account_capture_profile',
      desc: '',
      args: [],
    );
  }

  /// `Take photos successfully`
  String get account_take_photos_successfully {
    return Intl.message(
      'Take photos successfully',
      name: 'account_take_photos_successfully',
      desc: '',
      args: [],
    );
  }

  /// `Confirm information`
  String get account_confirm_info {
    return Intl.message(
      'Confirm information',
      name: 'account_confirm_info',
      desc: '',
      args: [],
    );
  }

  /// `Do not rotate more than 90 degrees`
  String get account_not_rotate_90 {
    return Intl.message(
      'Do not rotate more than 90 degrees',
      name: 'account_not_rotate_90',
      desc: '',
      args: [],
    );
  }

  /// `Rotate your face from the center of the camera to the sides to start recognizing faces`
  String get account_guide_ekyc1 {
    return Intl.message(
      'Rotate your face from the center of the camera to the sides to start recognizing faces',
      name: 'account_guide_ekyc1',
      desc: '',
      args: [],
    );
  }

  /// `You note your eyes can always look at the camera. Do not rotate more than 90 degrees when you cannot look at the camera`
  String get account_guide_ekyc2 {
    return Intl.message(
      'You note your eyes can always look at the camera. Do not rotate more than 90 degrees when you cannot look at the camera',
      name: 'account_guide_ekyc2',
      desc: '',
      args: [],
    );
  }

  /// `The system prepares to identify later`
  String get account_ekyc_start {
    return Intl.message(
      'The system prepares to identify later',
      name: 'account_ekyc_start',
      desc: '',
      args: [],
    );
  }

  /// `Put faces in frame`
  String get account_face_to_frame {
    return Intl.message(
      'Put faces in frame',
      name: 'account_face_to_frame',
      desc: '',
      args: [],
    );
  }

  /// `CONGRATULATIONS ON SUCCESSFULLY OPENING A VPBANK SECURITIES ACCOUNT!`
  String get account_register_success {
    return Intl.message(
      'CONGRATULATIONS ON SUCCESSFULLY OPENING A VPBANK SECURITIES ACCOUNT!',
      name: 'account_register_success',
      desc: '',
      args: [],
    );
  }

  /// `CONGRATULATIONS ON SUCCESSFULLY REGISTERING AND OPENING A COMBO ACCOUNT!`
  String get account_register_success_combo {
    return Intl.message(
      'CONGRATULATIONS ON SUCCESSFULLY REGISTERING AND OPENING A COMBO ACCOUNT!',
      name: 'account_register_success_combo',
      desc: '',
      args: [],
    );
  }

  /// `Please login with your account number and password sent via sms.\nThank you for trusting and accompanying VPBank Securities!`
  String get account_content_success {
    return Intl.message(
      'Please login with your account number and password sent via sms.\nThank you for trusting and accompanying VPBank Securities!',
      name: 'account_content_success',
      desc: '',
      args: [],
    );
  }

  /// `Information about securities accounts and bank account opening status will be sent to you via registered sms & email.`
  String get account_content_success_combo1 {
    return Intl.message(
      'Information about securities accounts and bank account opening status will be sent to you via registered sms & email.',
      name: 'account_content_success_combo1',
      desc: '',
      args: [],
    );
  }

  /// `Thank you for trusting and investing with VPBank Securities!`
  String get account_content_success_combo2 {
    return Intl.message(
      'Thank you for trusting and investing with VPBank Securities!',
      name: 'account_content_success_combo2',
      desc: '',
      args: [],
    );
  }

  /// `Start investing with VPBankS`
  String get account_start_invest_vpbank {
    return Intl.message(
      'Start investing with VPBankS',
      name: 'account_start_invest_vpbank',
      desc: '',
      args: [],
    );
  }

  /// `Dont have a VPBank bank account yet?`
  String get account_sdk_vp_title {
    return Intl.message(
      'Dont have a VPBank bank account yet?',
      name: 'account_sdk_vp_title',
      desc: '',
      args: [],
    );
  }

  /// `Open a VPBank checking account now to enjoy the benefits`
  String get account_sdk_vp_r0 {
    return Intl.message(
      'Open a VPBank checking account now to enjoy the benefits',
      name: 'account_sdk_vp_r0',
      desc: '',
      args: [],
    );
  }

  /// `Free to open beautiful Digital Account`
  String get account_sdk_vp_r1 {
    return Intl.message(
      'Free to open beautiful Digital Account',
      name: 'account_sdk_vp_r1',
      desc: '',
      args: [],
    );
  }

  /// `Quick deposit/withdrawal of securities`
  String get account_sdk_vp_r2 {
    return Intl.message(
      'Quick deposit/withdrawal of securities',
      name: 'account_sdk_vp_r2',
      desc: '',
      args: [],
    );
  }

  /// `Free Investment Advisory Service`
  String get account_sdk_vp_r3 {
    return Intl.message(
      'Free Investment Advisory Service',
      name: 'account_sdk_vp_r3',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited Cashback with Credit Card`
  String get account_sdk_vp_r4 {
    return Intl.message(
      'Unlimited Cashback with Credit Card',
      name: 'account_sdk_vp_r4',
      desc: '',
      args: [],
    );
  }

  /// `Open an account now`
  String get account_sdk_action {
    return Intl.message(
      'Open an account now',
      name: 'account_sdk_action',
      desc: '',
      args: [],
    );
  }

  /// `Take CCCD`
  String get account_take_photo_id_card {
    return Intl.message(
      'Take CCCD',
      name: 'account_take_photo_id_card',
      desc: '',
      args: [],
    );
  }

  /// `Enter address`
  String get account_type_address {
    return Intl.message(
      'Enter address',
      name: 'account_type_address',
      desc: '',
      args: [],
    );
  }

  /// `Bank Account name (Unsigned)`
  String get account_back_acc_name {
    return Intl.message(
      'Bank Account name (Unsigned)',
      name: 'account_back_acc_name',
      desc: '',
      args: [],
    );
  }

  /// `Hiện tại VPBank chưa thể cung cấp dịch vụ cho bạn. Vui lòng liên hệ tổng đài chăm sóc khách hàng ********** để được hỗ trợ. Ấn “Đã hiểu” để trở về.`
  String get account_msg_one {
    return Intl.message(
      'Hiện tại VPBank chưa thể cung cấp dịch vụ cho bạn. Vui lòng liên hệ tổng đài chăm sóc khách hàng ********** để được hỗ trợ. Ấn “Đã hiểu” để trở về.',
      name: 'account_msg_one',
      desc: '',
      args: [],
    );
  }

  /// `Contract download successful.`
  String get account_contract_success {
    return Intl.message(
      'Contract download successful.',
      name: 'account_contract_success',
      desc: '',
      args: [],
    );
  }

  /// `Load of contract failed. Please try again.`
  String get account_contract_failed {
    return Intl.message(
      'Load of contract failed. Please try again.',
      name: 'account_contract_failed',
      desc: '',
      args: [],
    );
  }

  /// `Chụp ảnh thành công `
  String get account_takePhotosSuccessfully {
    return Intl.message(
      'Chụp ảnh thành công ',
      name: 'account_takePhotosSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Hủy thay đổi mật khẩu đăng nhập`
  String get account_cancelChangePass {
    return Intl.message(
      'Hủy thay đổi mật khẩu đăng nhập',
      name: 'account_cancelChangePass',
      desc: '',
      args: [],
    );
  }

  /// `Bạn có chắc chắn muốn hủy các bước thay đổi mật khẩu?`
  String get account_cancelChangePassConfirm {
    return Intl.message(
      'Bạn có chắc chắn muốn hủy các bước thay đổi mật khẩu?',
      name: 'account_cancelChangePassConfirm',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'vi'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
