import 'package:flutter/material.dart';
import 'package:vp_centralize/core/service/smart_otp_service.dart';
import 'package:vp_centralize/model/smart_otp/smart_otp_active_obj.dart';
import 'package:vp_centralize/model/smart_otp/smart_otp_gen_secretkey_obj.dart';
import 'package:vp_centralize/model/smart_otp/smart_otp_register_obj.dart';
import 'package:vp_centralize/model/smart_otp/smart_otp_registered_obj.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';

abstract class SmartOtpRepository {
  Future<BaseResponse?> registerSmartOTP(SmartOtpRegisterRequestObj param);

  Future<BaseResponse?> activeSmartOtp(SmartOtpActiveRequestObj param);

  Future<BaseResponse?> cancelSmartOtp(SmartOtpActiveRequestObj param);

  Future<BaseResponse<SmartOTPRegisteredResponseObj?>> registedSmartOtp(
    String deviceId,
  );

  Future<String> registerSmartOTP2(SmartOtpRegisterRequestObj param);

  Future<Response> generationSecretKey2(SmartOtpGenSecretKeyRequestObj param);
}

class SmartOtpRepositoryImpl extends SmartOtpRepository {
  final SmartOtpService smartOtpService;

  SmartOtpRepositoryImpl({required this.smartOtpService});

  /// API dang ky sesion_id smart otp
  @override
  Future<String> registerSmartOTP2(SmartOtpRegisterRequestObj param) async {
    try {
      final response = await smartOtpService.registerSmartOTP2(param);
      if (response.isSuccess) {
        return response.data['session_id'];
      }

      throw response.message ?? '';
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  /// API dang ky sesion_id smart otp
  @override
  Future<BaseResponse?> registerSmartOTP(SmartOtpRegisterRequestObj param) {
    try {
      return smartOtpService.registerSmartOTP(param);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<Response> generationSecretKey2(SmartOtpGenSecretKeyRequestObj param) {
    try {
      return smartOtpService.generationSecretKey2(param);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  // Active SmartOTP
  @override
  Future<BaseResponse?> activeSmartOtp(SmartOtpActiveRequestObj param) async {
    try {
      return await smartOtpService.activeSmartOtp(param);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  // Cancel SmartOTP
  @override
  Future<BaseResponse?> cancelSmartOtp(SmartOtpActiveRequestObj param) {
    try {
      return smartOtpService.cancelSmartOtp(param);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse<SmartOTPRegisteredResponseObj?>> registedSmartOtp(
    String deviceId,
  ) {
    try {
      return smartOtpService.registedSmartOtp({'device_id': deviceId});
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      throw HandleError.from(e);
    }
  }
}
