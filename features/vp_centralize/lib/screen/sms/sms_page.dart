import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:vp_centralize/generated/assets.gen.dart';
import 'package:vp_centralize/localization/localize_key.dart';
import 'package:vp_centralize/model/params/get_sms_otp_params.dart';
import 'package:vp_centralize/screen/centralize_orientation/centralize_orientation_page.dart';
import 'package:vp_centralize/screen/pin/pin_page.dart';
import 'package:vp_centralize/screen/smart_otp/custom_scaffold.dart';
import 'package:vp_centralize/screen/smart_otp/rx_view.dart';
import 'package:vp_centralize/screen/sms/cubit/sms_cubit.dart';
import 'package:vp_centralize/screen/sms/verify_email/verify_email_bottom_sheet.dart';
import 'package:vp_centralize/screen/sms/verify_email/verify_email_cubit.dart';
import 'package:vp_centralize/screen/widgets/app_pincode_widget.dart';
import 'package:vp_centralize/screen/widgets/keep_session_view.dart';
import 'package:vp_centralize/screen/widgets/progress_bar.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class SmsPage extends StatefulWidget {
  const SmsPage({super.key, required this.params});

  final GetSmsOTPParams? params;

  bool get isCentralize => params?.isCentralize ?? false;

  OTPVerifyBy get verifyBy => params?.verifyBy ?? OTPVerifyBy.sms;

  OTPResend? get onResendOtp => params?.onResendSmsOTP;

  OTPVerify? get onSubmitPinCode => params?.onSubmitPinCode;

  int get expireSeconds => params?.expireSeconds ?? smsOtpTimeOut;

  bool get allowKeepSession => params?.allowKeepSession ?? false;

  String? get appbarTitle => params?.appbarTitle;

  String? get appbarSubTitle => params?.appbarSubTitle;

  bool Function(Response? responseApi)? get popWhenSubmitFail =>
      params?.popWhenSubmitFail;

  @override
  State<SmsPage> createState() => _SmsPageState();
}

class _SmsPageState extends State<SmsPage> {
  late SmsCubit cubit = SmsCubit(
    widget.onSubmitPinCode,
    widget.onResendOtp,
    widget.isCentralize,
    widget.expireSeconds,
    widget.popWhenSubmitFail,
    widget.allowKeepSession,
  );

  final int timeConfigEnableResend = 20;

  bool get verifyByEmail => widget.verifyBy == OTPVerifyBy.email;

  bool get verifyBySms => widget.verifyBy == OTPVerifyBy.sms;

  bool get verifyByEmailAndSms => widget.verifyBy == OTPVerifyBy.otp;

  final otpController = TextEditingController();

  @override
  void dispose() {
    cubit.close();
    otpController.dispose();
    super.dispose();
  }

  void showLoading() => cubit.showLoading();

  void hideLoading() => cubit.hideLoading();

  @override
  Widget build(BuildContext context) {
    return BlocProvider<SmsCubit>.value(
      value: cubit,
      child: CentralizeOrientationPage(
        child: CustomScaffold(
          loadingStream: cubit.loadingStream,
          child: SafeArea(
            child: Column(
              children: [
                HeaderWidget(
                  back: false,
                  subTitle:
                      widget.appbarSubTitle ?? LocalizeKey.smsOTPAppBarTitle,
                  title: widget.appbarTitle ?? LocalizeKey.smsOTPAppBarSubTitle,
                  icon: SvgPicture.asset(
                    CentralizeKeyAssets.icons.icClose.path,
                    package: CentralizeKeyAssets.package,
                    colorFilter: ColorFilter.mode(
                      vpColor.iconPrimary,
                      BlendMode.srcIn,
                    ),
                  ),
                  actionRight: () => Navigator.pop(context),
                ),

                const SizedBox(height: 16),

                /// input otp
                buildInputOtpArea(),

                buildCountTimeAndResend(),

                const Spacer(),

                /// button verify email
                buildVerifyEmailView(),

                RxView<bool>(
                  rxSubject: cubit.allowKeepSession,
                  builder: (context, allowKeepSession) {
                    return KeepSessionView(
                      allowKeepSession: allowKeepSession,
                      onChanged: (value) => cubit.onKeepSessionChanged(value),
                    );
                  },
                ),

                /// bottom view
                buildBottomView(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget buildBottomView() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: RxView<String>(
        rxSubject: cubit.otp,
        builder: (_, __) {
          return BottomView(
            onBack: () => Navigator.pop(context),
            onNext: cubit.enableButtonNext ? () => cubit.confirm() : null,
          );
        },
      ),
    );
  }

  Widget buildInputOtpArea() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          buildHintView(),
          const SizedBox(height: 16),
          AppPinCodeWidget(
            parentContext: context,
            count: characterOTP,
            controller: otpController,
            onCompleted: (otp) {},
            autoDisposeControllers: false,
            onChanged: (otp, __) => cubit.onOTPChanged(otp),
            beforeTextPaste:
                (text) => text != null && int.tryParse(text) != null,
          ),
        ],
      ),
    );
  }

  Widget buildCountTimeAndResend() {
    return Column(
      children: [
        /// error message
        RxView<String>(
          rxSubject: cubit.errorMessage,
          builder: (_, message) {
            if (message.isNullOrEmpty) {
              return const SizedBox(height: 0);
            }

            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                message,
                style: vpTextStyle.subtitle14.copyColor(themeData.red),
                textAlign: TextAlign.center,
              ),
            );
          },
        ),

        /// count time
        RxView<int>(
          rxSubject: cubit.remainTime,
          builder: (_, remainTime) {
            if (remainTime <= 0) return const SizedBox(height: 0);

            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Mã OTP có hạn sử dụng trong',
                  style: vpTextStyle.body14.copyColor(themeData.gray500),
                ),
                Text(
                  cubit.onFormatTimeToDisplay(remainTime),
                  style: vpTextStyle.body14.copyColor(themeData.primary),
                ),
              ],
            );
          },
        ),

        if (widget.onResendOtp != null)
          RxView<int>(
            rxSubject: cubit.remainTime,
            builder: (_, remainTime) {
              bool isEnable =
                  remainTime <= widget.expireSeconds - timeConfigEnableResend;
              return TextButton(
                onPressed: () => isEnable ? onResendOTP() : null,
                child: Text(
                  'Gửi lại OTP',
                  style: vpTextStyle.subtitle14.copyColor(
                    isEnable ? themeData.primary : vpColor.textDisabled,
                  ),
                ),
              );
            },
          ),
      ],
    );
  }

  void onResendOTP() {
    otpController.clear();

    cubit
      ..onClearInput()
      ..reSendOTP();
  }

  Widget buildHintView() {
    return RxView<bool>(
      rxSubject: cubit.emailVerifySuccess,
      builder: (_, justVerified) {
        final phone = cubit.phone;

        final email = cubit.email;

        final emailValid = email.hasData && justVerified;

        if (phone.isNullOrEmpty) {
          return Text(
            'Hệ thống đã gửi OTP vào số điện thoại của bạn. Vui lòng nhập mã OTP đã nhận',
            style: vpTextStyle.body14,
            textAlign: TextAlign.center,
          );
        }

        final children = <InlineSpan>[];

        final phoneSpans = <TextSpan>[
          const TextSpan(text: LocalizeKey.smsOTPPleaseInputOTP),
          TextSpan(
            text: phone!.obscure(3, 7),
            style: vpTextStyle.subtitle14.copyColor(vpColor.textSecondary),
          ),
        ];

        final emailSpans =
            <TextSpan>[]
              ..addIf(
                const TextSpan(text: LocalizeKey.smsOTPPleaseInputEmail),
                addIf: () => emailValid,
              )
              ..addIf(
                TextSpan(
                  text: email.obscureEmail(3),
                  style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
                ),
                addIf: () => emailValid,
              );

        if (verifyBySms) {
          children.addAll(phoneSpans);
        }

        if (verifyByEmail) {
          children.addAll(
            []
              ..addIf(
                const TextSpan(text: LocalizeKey.smsOTPPleaseInputEmailOTP),
                addIf: () => email.hasData,
              )
              ..addIf(
                TextSpan(
                  text: email.obscureEmail(3),
                  style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
                ),
                addIf: () => email.hasData,
              ),
          );
        }

        if (verifyByEmailAndSms) {
          children
            ..addAll(phoneSpans)
            ..addAll(emailSpans);
        }

        return Text.rich(
          TextSpan(
            style: vpTextStyle.body14.copyColor(vpColor.textSecondary),
            children: children,
          ),
        );
      },
    );
  }

  Widget buildVerifyEmailView() {
    if (verifyByEmailAndSms) {
      return RxView<bool>(
        rxSubject: cubit.emailVerified,
        builder: (_, isVerified) {
          final email = cubit.email;

          if (email.isNullOrEmpty || isVerified) {
            return const SizedBox(height: 0);
          }

          final style = vpTextStyle.body14;

          return Padding(
            padding: const EdgeInsets.all(16),
            child: Text.rich(
              TextSpan(
                style: style,
                children: [
                  TextSpan(
                    text: LocalizeKey.smsOTPVerifyEmailTitle,
                    style: vpTextStyle.body14.copyColor(vpColor.textSecondary),
                  ),
                  TextSpan(
                    text: LocalizeKey.smsOTPVerifyEmailButton,
                    style: style?.copyWith(
                      color: themeData.primary,
                      fontWeight: FontWeight.w600,
                    ),
                    recognizer:
                        TapGestureRecognizer()
                          ..onTap = () => openVerifyEmailBottomSheet(),
                  ),
                ],
              ),
            ),
          );
        },
      );
    }

    return const SizedBox(height: 0);
  }

  void openVerifyEmailBottomSheet() async {
    context.hideKeyboard();

    VerifyEmailCubit verifyEmailCubit = VerifyEmailCubit(cubit.email!);

    verifyEmailCubit.onSendOTP(
      onSuccess: () async {
        final verifySuccess = await showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          builder:
              (_) => Padding(
                padding: MediaQuery.of(context).viewInsets,
                child: VerifyEmailBottomSheet(
                  email: cubit.email!,
                  cubit: verifyEmailCubit,
                ),
              ),
        );

        if (verifySuccess == true) cubit.onRefreshInfo();
      },
      onError: (errorMessage) {
        verifyEmailCubit.close();

        if (errorMessage != null) {
          showSnackBar(context, errorMessage, isSuccess: false);
        }
      },
      onVerifiedInOtherDevice: (error) {
        showError(error);

        cubit.onRefreshInfo();
      },
      onStart: () => showLoading(),
      onFinally: () => hideLoading(),
    );
  }
}
