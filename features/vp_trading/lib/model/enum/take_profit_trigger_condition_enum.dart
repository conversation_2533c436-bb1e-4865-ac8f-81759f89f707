import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/generated/l10n.dart';

enum TakeProfitTriggerConditionEnum { rateProfit, slippageProfit }

extension TakeProfitTriggerConditionEnumExtension
    on TakeProfitTriggerConditionEnum {
  String _takeProfitToString() {
    switch (this) {
      case TakeProfitTriggerConditionEnum.rateProfit:
        return VPTradingLocalize
            .current
            .trading_condition_take_profit_rate_percent;
      case TakeProfitTriggerConditionEnum.slippageProfit:
        return VPTradingLocalize.current.trading_condition_profit_margin;
    }
  }

  String _stopLossToString() {
    switch (this) {
      case TakeProfitTriggerConditionEnum.rateProfit:
        return VPTradingLocalize
            .current
            .trading_condition_stop_loss_rate_percent;
      case TakeProfitTriggerConditionEnum.slippageProfit:
        return VPTradingLocalize.current.trading_condition_stop_loss_margin;
    }
  }

  String toStringShowDialog({OrderType? orderType = OrderType.takeProfit}) {
    if (orderType == OrderType.takeProfit) {
      return _takeProfitToString();
    } else if (orderType == OrderType.stopLoss) {
      return _stopLossToString();
    } else {
      return '';
    }
  }

  static TakeProfitTriggerConditionEnum fromString(String? text) {
    if (text == "rate") {
      return TakeProfitTriggerConditionEnum.rateProfit;
    } else {
      return TakeProfitTriggerConditionEnum.slippageProfit;
    }
  }
}
