import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/enum/condition_order_type_fu_enum.dart';

class OrderConditionTrailingStopItem extends StatelessWidget {
  const OrderConditionTrailingStopItem({
    super.key,
    required this.item,
    required this.onSubOrderTap,
  });
  final ConditionOrderBookModel item;
  final VoidCallback onSubOrderTap;

  @override
  Widget build(BuildContext context) {
    return _buildOrderDetails();
  }

  Widget _buildOrderDetails() {
    return Column(
      spacing: 8,
      children: [
        _buildDetailRow('Loại lệnh', item.conditionOrderTypeFuEnum.title),

        _buildDetailRow('Thời gian đặt', item.createTime ?? '-'),

        _buildDetailRow('Thời gian kích hoạt', item.activeTime ?? '-'),

        _buildDetailRow('Số hiệu lệnh gốc', item.orderId ?? '-'),

        _buildDetailRow(
          'Giá KH ban đầu',
          item.fx1Price?.toString().priceDerivative.toString() ?? '-',
        ),
        _buildDetailRow(
          'Biên độ',
          item.deltaValue?.toString().priceDerivative.toString() ?? '-',
        ),
        _buildDetailRow(
          'Bước giá',
          item.priceStep?.toString().priceDerivative.toString() ?? '-',
        ),
        _buildDetailRow('Kênh đặt', item.viaName),

        _buildDetailRow(
          'Lệnh con',
          '-',
          valueWidget: GestureDetector(
            onTap: onSubOrderTap,
            child: Text(
              "Xem chi tiết",
              style: vpTextStyle.body14?.copyWith(color: vpColor.textBrand),
            ),
          ),
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value, {Widget? valueWidget}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: vpTextStyle.body14?.copyWith(color: vpColor.textSecondary),
        ),
        valueWidget ??
            Text(
              value,
              style: vpTextStyle.body14?.copyWith(
                color: vpColor.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
      ],
    );
  }
}
