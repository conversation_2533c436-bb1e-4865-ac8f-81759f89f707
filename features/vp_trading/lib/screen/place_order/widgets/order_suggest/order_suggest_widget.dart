// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/cubit/place_order/order_suggest/order_suggest_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';

class OrderSuggest extends StatelessWidget {
  const OrderSuggest({super.key});

  @override
  Widget build(BuildContext context) {
    //final bloc = context.read<PlaceOrderBloc>();
    return BlocBuilder<ValidateOrderCubit, ValidateOrderState>(
      buildWhen:
          (previous, current) => previous.sessionType != current.sessionType,
      builder: (context, stateValidate) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: BlocBuilder<OrderSuggestCubit, List<SessionType>>(
            buildWhen: (previous, current) {
              // if (previous.isEmpty && current.isNotEmpty) {
              //   bloc.add(ScrollPaddingChangeEvent());
              // }
              return previous != current;
            },
            builder:
                (_, state) => _SlideSwitcher(
                  show: true,
                  begin: Offset.zero,
                  //     begin: newOrder ? const Offset(0, -1) : Offset.zero,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 4),
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: [
                            BlocBuilder<StockInfoCubit, StockInfoState>(
                              buildWhen:
                                  (previous, current) =>
                                      previous.isRealtime != current.isRealtime,
                              builder:
                                  (context, state) => Padding(
                                    padding: const EdgeInsets.only(right: 8),
                                    child: VPChipView.dynamic(
                                      text: "Khớp",
                                      size: ChipSize.small,
                                      style:
                                          state.isRealtime
                                              ? ChipStyle.selected
                                              : ChipStyle.chipDefault,
                                      onTap: () {
                                        context
                                            .read<StockInfoCubit>()
                                            .onRealtimeChanged(
                                              !state.isRealtime,
                                            );
                                      },
                                    ),
                                  ),
                            ),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children:
                                  state
                                      .map(
                                        (e) => Padding(
                                          padding: const EdgeInsets.only(
                                            right: 8,
                                          ),
                                          child: VPChipView.dynamic(
                                            text: e.name.toUpperCase(),
                                            size: ChipSize.small,
                                            style:
                                                stateValidate.sessionType == e
                                                    ? ChipStyle.selected
                                                    : ChipStyle.chipDefault,
                                            onTap: () {
                                              context
                                                  .read<ValidateOrderCubit>()
                                                  .setSessionType(e);
                                              context
                                                  .read<ValidateOrderCubit>()
                                                  .onChangePrice(e.name);

                                              context
                                                  .read<StockInfoCubit>()
                                                  .onRealtimeChanged(false);
                                            },
                                          ),
                                        ),
                                      )
                                      .toList(),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
          ),
        );
      },
    );
  }
}

class _SlideSwitcher extends StatelessWidget {
  final Widget child;
  final Offset begin;
  final bool show;

  const _SlideSwitcher({
    required this.child,
    this.begin = const Offset(0, -1),
    required this.show,
  });

  @override
  Widget build(BuildContext context) => ClipRect(
    child: AnimatedSwitcher(
      switchInCurve: Curves.ease,
      switchOutCurve: Curves.ease,
      duration: const Duration(milliseconds: 100),
      child: show ? child : const SizedBox.shrink(),
      transitionBuilder:
          (child, animation) => SlideTransition(
            position: Tween<Offset>(
              begin: begin,
              end: Offset.zero,
            ).animate(animation),
            child: child,
          ),
    ),
  );
}
