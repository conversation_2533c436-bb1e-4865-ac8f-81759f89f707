// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a vi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'vi';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "accountIsNotLinkedToTheDelegation": MessageLookupByLibrary.simpleMessage(
      "Tài khoản không được liên kết ủy quyền, liên hệ ********** để được hỗ trợ.",
    ),
    "activationPrice": MessageLookupByLibrary.simpleMessage("Giá kích hoạt"),
    "active": MessageLookupByLibrary.simpleMessage("Đang hoạt động"),
    "add": MessageLookupByLibrary.simpleMessage("Thêm"),
    "addCode": MessageLookupByLibrary.simpleMessage("Thêm mã"),
    "addNewCategory": MessageLookupByLibrary.simpleMessage(
      "Thêm danh sách mới",
    ),
    "addStock": MessageLookupByLibrary.simpleMessage("Thêm cổ phiếu"),
    "addToCategory": MessageLookupByLibrary.simpleMessage("Thêm vào danh sách"),
    "addWatchList": MessageLookupByLibrary.simpleMessage("Thêm vào danh sách"),
    "added": MessageLookupByLibrary.simpleMessage("Đã thêm"),
    "addedSymbolFollowList": MessageLookupByLibrary.simpleMessage(
      "Đã thêm vào danh sách Mã CK đang chọn",
    ),
    "addedToList": MessageLookupByLibrary.simpleMessage(
      "Đã thêm vào danh sách",
    ),
    "allSymbol": MessageLookupByLibrary.simpleMessage("Tất cả mã"),
    "allocationPlan": MessageLookupByLibrary.simpleMessage("Kế hoạch phân bổ"),
    "allocationRules": MessageLookupByLibrary.simpleMessage(
      "Quy tắc phân bổ 50/30/20",
    ),
    "allocationRulesContent": MessageLookupByLibrary.simpleMessage(
      "Quy tắc 50/30/20 là quy tắc phổ biến được khuyến nghị để quản lý tài chính cá nhân hiệu quả.",
    ),
    "allowMultiSelect": MessageLookupByLibrary.simpleMessage(
      "Cho phép chọn nhiều mã",
    ),
    "amountInitialStart": MessageLookupByLibrary.simpleMessage(
      "Số tiền đầu tư ban đầu",
    ),
    "anErrorOccurred": MessageLookupByLibrary.simpleMessage("Có lỗi xảy ra"),
    "apply": MessageLookupByLibrary.simpleMessage("Áp dụng"),
    "areYouCancelCreatePlan": MessageLookupByLibrary.simpleMessage(
      "Bạn có chắc chắn muốn hủy tạo kế hoạch\ntích sản?",
    ),
    "asset": MessageLookupByLibrary.simpleMessage("Tài sản"),
    "atm": MessageLookupByLibrary.simpleMessage("Hòa vốn"),
    "autoAdjustTriggerPriceAndSetPrice": MessageLookupByLibrary.simpleMessage(
      "Tự động điều chỉnh giá kích hoạt & giá đặt",
    ),
    "availableBalances": MessageLookupByLibrary.simpleMessage("Số dư"),
    "availableVolume": MessageLookupByLibrary.simpleMessage("KL khả dụng"),
    "averageCostPrice": MessageLookupByLibrary.simpleMessage(
      "Giá vốn trung bình",
    ),
    "averageCostPriceConditional": MessageLookupByLibrary.simpleMessage(
      "Giá vốn bình quân",
    ),
    "averageMatchingPrice": MessageLookupByLibrary.simpleMessage(
      "Giá khớp trung bình",
    ),
    "averagePrice": MessageLookupByLibrary.simpleMessage("Giá trung bình"),
    "back": MessageLookupByLibrary.simpleMessage("Quay lại"),
    "bankLinking": MessageLookupByLibrary.simpleMessage(
      "Ngân hàng hỗ trợ liên kết",
    ),
    "bidPriceTitle": MessageLookupByLibrary.simpleMessage("Giá đặt/\nGiá khớp"),
    "bigger": MessageLookupByLibrary.simpleMessage("Lớn hơn"),
    "bil": MessageLookupByLibrary.simpleMessage("tỷ"),
    "bondRL": MessageLookupByLibrary.simpleMessage("Trái phiếu RL"),
    "bondsfunds": MessageLookupByLibrary.simpleMessage(
      "Trái phiếu/Quỹ trái phiếu:",
    ),
    "buttonAccept": MessageLookupByLibrary.simpleMessage("Xác nhận"),
    "buttonBack": MessageLookupByLibrary.simpleMessage("Quay lại"),
    "buttonClose": MessageLookupByLibrary.simpleMessage("Đóng"),
    "buttonContinue": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
    "buttonSaveChange": MessageLookupByLibrary.simpleMessage("Lưu thay đổi"),
    "buttonSeeDetails": MessageLookupByLibrary.simpleMessage(
      "Xem danh mục đầu tư",
    ),
    "canNotEditCommand": MessageLookupByLibrary.simpleMessage(
      "Không thể sửa lệnh",
    ),
    "canNotEditCommandWarning": MessageLookupByLibrary.simpleMessage(
      "Bạn không thể sửa lệnh do không còn nắm giữ\nmã chứng khoán",
    ),
    "cancelAll": MessageLookupByLibrary.simpleMessage("Hủy tất cả lệnh"),
    "cancelCreatePlan": MessageLookupByLibrary.simpleMessage(
      "Có, hủy kế hoạch",
    ),
    "cancelLinkingAccountFail": MessageLookupByLibrary.simpleMessage(
      "Hủy liên kết tài khoản không thành công",
    ),
    "cancelLinkingAccountSuccess": MessageLookupByLibrary.simpleMessage(
      "Hủy liên kết tài khoản thành công",
    ),
    "cancelOrder": MessageLookupByLibrary.simpleMessage("Hủy lệnh"),
    "cancelPlan": MessageLookupByLibrary.simpleMessage(
      "Hủy tạo kế hoạch tích sản",
    ),
    "cancelPlanPage": MessageLookupByLibrary.simpleMessage(
      "Bạn có muốn hủy tạo kế hoạch",
    ),
    "cancelSurvey": MessageLookupByLibrary.simpleMessage("Có, huỷ khảo sát"),
    "cancelSurveyNew": MessageLookupByLibrary.simpleMessage(
      "Bạn muốn huỷ khảo sát?",
    ),
    "cannotOrderOddLotWithMarketCommand": MessageLookupByLibrary.simpleMessage(
      "Không thể đặt lô lẻ với lệnh thị trường",
    ),
    "capitalCost": MessageLookupByLibrary.simpleMessage("Giá vốn"),
    "capitalValue": MessageLookupByLibrary.simpleMessage("Giá trị vốn"),
    "cashInTitle": MessageLookupByLibrary.simpleMessage("Số tiền nạp"),
    "cashMoney": MessageLookupByLibrary.simpleMessage(
      "Tiền mặt/Quỹ thị trường tiền tệ:",
    ),
    "category": MessageLookupByLibrary.simpleMessage("Danh mục"),
    "chooseInvestmentFrequency": MessageLookupByLibrary.simpleMessage(
      "Chọn tần suất đầu tư",
    ),
    "chooseTarget": MessageLookupByLibrary.simpleMessage("Chọn mục tiêu"),
    "codeTitle": MessageLookupByLibrary.simpleMessage("Mã CK"),
    "comingSoon": MessageLookupByLibrary.simpleMessage("Sắp ra mắt"),
    "commandClose": MessageLookupByLibrary.simpleMessage("Đóng"),
    "commandDialogCancel": MessageLookupByLibrary.simpleMessage(
      "Xác nhận dừng đặt lệnh",
    ),
    "commandType": MessageLookupByLibrary.simpleMessage("Loại lệnh"),
    "completed": MessageLookupByLibrary.simpleMessage("Hoàn thành"),
    "conditionalOrderWaitingConfirmGuide": MessageLookupByLibrary.simpleMessage(
      "Bằng việc %&Xác nhận lệnh%&, bạn hiểu và chấp thuận với các lệnh giao dịch  được phát sinh khi thỏa mãn các điều kiện kích hoạt.",
    ),
    "conditionalOrderWaitingConfirmNote": MessageLookupByLibrary.simpleMessage(
      "Lệnh chỉ được kích hoạt 01 lần duy nhất trong thời gian hiệu lực.",
    ),
    "confirm": MessageLookupByLibrary.simpleMessage("Xác nhận"),
    "confirmDeleteAll": MessageLookupByLibrary.simpleMessage(
      "Bạn có muốn xóa tất cả cổ phiếu trong danh sách đã chọn không?",
    ),
    "confirmPlan": MessageLookupByLibrary.simpleMessage(
      "Xác nhận thông tin kế hoạch",
    ),
    "contactCustomerService": MessageLookupByLibrary.simpleMessage(
      "Liên hệ DVKH",
    ),
    "continueInvesting": MessageLookupByLibrary.simpleMessage(
      "Bạn có muốn tiếp tục đầu tư?",
    ),
    "coutionInfoPlan": MessageLookupByLibrary.simpleMessage(
      "Lưu ý: số tiền đầu tư nhỏ hơn thị giá của mã cổ phiếu sẽ ảnh hưởng tới việc đặt lệnh của kế hoạch",
    ),
    "createInvestmentTaste": MessageLookupByLibrary.simpleMessage(
      "Tạo ra khẩu vị đầu tư của riêng bạn",
    ),
    "date": MessageLookupByLibrary.simpleMessage("Ngày"),
    "deceptionLinkAccount": MessageLookupByLibrary.simpleMessage(
      "Bạn sẽ được chuyển đến trang web của ngân hàng đối tác. Bạn có muốn tiếp tục không?",
    ),
    "deceptionUnlink": MessageLookupByLibrary.simpleMessage(
      "Bạn có xác nhận muốn hủy đăng ký liên kết",
    ),
    "deleteAll": MessageLookupByLibrary.simpleMessage("Xoá tất cả"),
    "deleteAllSelected": MessageLookupByLibrary.simpleMessage(
      "Xoá tất cả cổ phiếu đã chọn",
    ),
    "deposit": MessageLookupByLibrary.simpleMessage("TK ký quỹ"),
    "depositFull": MessageLookupByLibrary.simpleMessage("Tiểu khoản ký quỹ"),
    "descAddNewCategory": MessageLookupByLibrary.simpleMessage(
      "Đặt tên cho danh sách mới của bạn",
    ),
    "descriptionInvestmentPhilosophy": MessageLookupByLibrary.simpleMessage(
      "Để xây dựng chiến lược đầu tư tích sản phù hợp, cùng VPBankS thực hiện bộ khảo sát để tìm ra phong cách đầu tư và danh mục tích sản bền vững.",
    ),
    "descriptionLevelHight": MessageLookupByLibrary.simpleMessage(
      "Chiến lược đầu tư tập trung vào sự tăng trưởng vốn trong trung và dài hạn, là lựa chọn phù hợp cho Nhà đầu tư có khả năng chấp nhận rủi ro cao và sẵn sàng chấp nhận sự biến động đáng kể với giá trị khoản đầu tư của mình",
    ),
    "descriptionLevelLow": MessageLookupByLibrary.simpleMessage(
      "Chiến lược đầu tư với mục tiêu bảo toàn vốn và hạn chế khả năng mất vốn, là lựa chọn phù hợp cho Nhà đầu tư có khẩu vị rủi ro thấp và sẵn sàng chấp nhận mức lợi nhuận thấp để đổi lấy sự an tâm nhất định",
    ),
    "descriptionLevelMedium": MessageLookupByLibrary.simpleMessage(
      "Chiến lược đầu tư với mục tiêu gia tăng tài sản một cách vừa phải, là lựa chọn phù hợp cho Nhà đầu tư có khẩu vị rủi ro trung bình và sẵn sàng chấp nhận một mức độ rủi ro nhất định",
    ),
    "detailCategory": MessageLookupByLibrary.simpleMessage("Chi tiết danh mục"),
    "dilutionChoiceTitle": MessageLookupByLibrary.simpleMessage(
      "Khi có sự kiện quyền pha loãng giá cổ phiếu",
    ),
    "doYouWantToStopInvesting": MessageLookupByLibrary.simpleMessage(
      "Bạn có muốn dừng đầu tư?",
    ),
    "downloadFail": MessageLookupByLibrary.simpleMessage("Tải xuống thất bại"),
    "downloadSuccess": MessageLookupByLibrary.simpleMessage(
      "Tải xuống thành công",
    ),
    "downloading": MessageLookupByLibrary.simpleMessage("Đang tải xuống"),
    "editCategory": MessageLookupByLibrary.simpleMessage("Đã lưu danh sách"),
    "editPlanName": MessageLookupByLibrary.simpleMessage("Sửa tên kế hoạch"),
    "effectiveDate": MessageLookupByLibrary.simpleMessage("Ngày hiệu lực"),
    "effectiveTime": MessageLookupByLibrary.simpleMessage("Thời gian hiệu lực"),
    "emptyListFilterPlanWhenNoResultMatching":
        MessageLookupByLibrary.simpleMessage("Không có kết quả nào phù hợp"),
    "emptyListSearchWhenNoResultMatching": MessageLookupByLibrary.simpleMessage(
      "Không có kết quả nào phù hợp.\nVui lòng tham khảo danh sách cổ phiếu\nchọn lọc bởi VPBankS.",
    ),
    "emptyListSearchWhenSymbolExist": MessageLookupByLibrary.simpleMessage(
      "Mã cổ phiếu đã tồn tại trong danh mục của bạn.\nVui lòng tham khảo thêm các cổ phiếu\nchọn lọc bởi VPBankS.",
    ),
    "emptyStockCategory": MessageLookupByLibrary.simpleMessage(
      "Bạn chưa có cổ phiếu nào trong danh mục",
    ),
    "etf": MessageLookupByLibrary.simpleMessage("ETF"),
    "evenLot": MessageLookupByLibrary.simpleMessage("Lô Chẵn"),
    "expectValue": MessageLookupByLibrary.simpleMessage("Giá trị dự kiến"),
    "expected": MessageLookupByLibrary.simpleMessage("dự kiến"),
    "expectedGrowth": MessageLookupByLibrary.simpleMessage(
      "Tăng trưởng kỳ vọng",
    ),
    "expectedMoney": MessageLookupByLibrary.simpleMessage("Số tiền kỳ vọng"),
    "expectedMoneyTarget": MessageLookupByLibrary.simpleMessage(
      "Số tiền kỳ vọng đạt được",
    ),
    "expectedProfit": MessageLookupByLibrary.simpleMessage("Lãi dự kiến"),
    "expectedProfitLoss": MessageLookupByLibrary.simpleMessage(
      "Lãi/lỗ (dự kiến)",
    ),
    "expectedSavings": MessageLookupByLibrary.simpleMessage(
      "Tiền tích luỹ kỳ vọng",
    ),
    "exrightDate": MessageLookupByLibrary.simpleMessage(
      "Giao dịch không hưởng quyền",
    ),
    "feeTransfer": MessageLookupByLibrary.simpleMessage("Phí chuyển tiền"),
    "filterDataEmpty": MessageLookupByLibrary.simpleMessage(
      "Không có kết quả nào phù hợp với bộ lọc. Vui lòng thay đổi điều kiện lọc và thử lại",
    ),
    "followList": MessageLookupByLibrary.simpleMessage("Danh sách theo dõi"),
    "free": MessageLookupByLibrary.simpleMessage("Miễn phí"),
    "fundGoalsContent": MessageLookupByLibrary.simpleMessage(
      "Phù hợp với nhà đầu tư mới tham gia thị trường, không có thời gian theo dõi thị trường thường xuyên, mong muốn đầu tư vào danh mục được đa dạng hóa và quản lý bởi đội ngũ chuyên gia giàu kinh nghiệm.",
    ),
    "heldAsset": MessageLookupByLibrary.simpleMessage("Tài sản nắm giữ"),
    "hintTextEnterAmount": MessageLookupByLibrary.simpleMessage("Nhập số tiền"),
    "hintTextEnterPlanName": MessageLookupByLibrary.simpleMessage(
      "Nhập tên kế hoạch",
    ),
    "hintTextEnterYearNumber": MessageLookupByLibrary.simpleMessage(
      "Nhập số năm",
    ),
    "hintTextSearchPlan": MessageLookupByLibrary.simpleMessage(
      "Tìm kiếm kế hoạch",
    ),
    "hintTextSearchStockSymbol": MessageLookupByLibrary.simpleMessage(
      "Tìm kiếm mã cổ phiếu",
    ),
    "historyStockCode": MessageLookupByLibrary.simpleMessage("Mã cổ phiếu"),
    "holdVolume": MessageLookupByLibrary.simpleMessage("Khối lượng nắm giữ"),
    "howDoYouAlignWithAnyInvestmentPhilosophy":
        MessageLookupByLibrary.simpleMessage(
          "Bạn thuộc trường phái đầu tư nào?",
        ),
    "inDebit": MessageLookupByLibrary.simpleMessage("Nợ"),
    "individual": MessageLookupByLibrary.simpleMessage("Cá nhân"),
    "inforPlan": MessageLookupByLibrary.simpleMessage("Thông tin kế hoạch"),
    "initialInvestmentAmount": MessageLookupByLibrary.simpleMessage(
      "Bắt đầu đầu tư với",
    ),
    "initialInvestmentAmountAnd": MessageLookupByLibrary.simpleMessage(
      "và đầu tư định kỳ thêm",
    ),
    "inputAnswer": MessageLookupByLibrary.simpleMessage(
      "Nhập câu trả lời của bạn",
    ),
    "invalidActivationPrice": MessageLookupByLibrary.simpleMessage(
      "Giá kích hoạt không hợp lệ",
    ),
    "invalidConditionalPrice": MessageLookupByLibrary.simpleMessage(
      "Giá đặt không hợp lệ",
    ),
    "invalidInput": MessageLookupByLibrary.simpleMessage("Không hợp lệ"),
    "invalidMaxBuy": MessageLookupByLibrary.simpleMessage(
      "Vượt quá sức mua của tiểu khoản",
    ),
    "invalidMaxSell": MessageLookupByLibrary.simpleMessage(
      "Bạn không nắm giữ cổ phiếu này",
    ),
    "invalidMaxVolume": MessageLookupByLibrary.simpleMessage(
      "Vượt khối lượng tối đa là",
    ),
    "invalidMinMass": MessageLookupByLibrary.simpleMessage(
      "Khối lượng tối thiểu 100 và phải là bội số của 100",
    ),
    "invalidOddLot": MessageLookupByLibrary.simpleMessage(
      "Giao dịch lô lẻ chỉ sử dụng Lệnh LO",
    ),
    "invalidOutOfRange": MessageLookupByLibrary.simpleMessage(
      "Giá nhập phải nằm trong khoảng giá trần và giá sàn",
    ),
    "invalidRateProfit": MessageLookupByLibrary.simpleMessage("Không hợp lệ"),
    "invalidSlipPagePrice": MessageLookupByLibrary.simpleMessage(
      "Biên trượt không hợp lệ",
    ),
    "invalidStep10": MessageLookupByLibrary.simpleMessage(
      "Bước giá không hợp lệ phải chia hết cho 10 đ",
    ),
    "invalidStep100": MessageLookupByLibrary.simpleMessage(
      "Bước giá không hợp lệ phải chia hết cho 100 đ",
    ),
    "invalidStep50": MessageLookupByLibrary.simpleMessage(
      "Bước giá không hợp lệ phải chia hết cho 50 đ",
    ),
    "investmentAllocation": MessageLookupByLibrary.simpleMessage(
      "Phân bổ đầu tư",
    ),
    "investmentCategory": MessageLookupByLibrary.simpleMessage(
      "Danh mục đầu tư",
    ),
    "investmentFrequency": MessageLookupByLibrary.simpleMessage(
      "Tần suất đầu tư",
    ),
    "investmentGoals": MessageLookupByLibrary.simpleMessage(
      "Lựa chọn mục tiêu đầu tư",
    ),
    "investmentMoney": MessageLookupByLibrary.simpleMessage("Số vốn đầu tư"),
    "investmentPhilosophy": MessageLookupByLibrary.simpleMessage(
      "Trường phái đầu tư",
    ),
    "investmentTime": MessageLookupByLibrary.simpleMessage("Thời gian đầu tư"),
    "investmentTimeIn": MessageLookupByLibrary.simpleMessage("Đầu tư trong"),
    "issueDate": MessageLookupByLibrary.simpleMessage("Ngày thực hiện"),
    "itm": MessageLookupByLibrary.simpleMessage("Đang lời"),
    "jointVolume": MessageLookupByLibrary.simpleMessage("Khối lượng khớp"),
    "k": MessageLookupByLibrary.simpleMessage("nghìn"),
    "less": MessageLookupByLibrary.simpleMessage("Nhỏ hơn"),
    "linkAccount": MessageLookupByLibrary.simpleMessage("Liên kết tài khoản"),
    "linkedAccount": MessageLookupByLibrary.simpleMessage(
      "Tài khoản đã liên kết",
    ),
    "marginRate": MessageLookupByLibrary.simpleMessage("Tỷ lệ vay"),
    "market": MessageLookupByLibrary.simpleMessage("Thị trường"),
    "marketPrice": MessageLookupByLibrary.simpleMessage("Giá trị thị trường"),
    "marketPriceAcr": MessageLookupByLibrary.simpleMessage("Giá TT"),
    "maxBuyVolume": MessageLookupByLibrary.simpleMessage("KL tối đa"),
    "maxMoneyTransfer": MessageLookupByLibrary.simpleMessage(
      "Số tiền chuyển tối đa",
    ),
    "maxVolumeIs": MessageLookupByLibrary.simpleMessage("Khối lượng tối đa là"),
    "messageErrorWhenEnterPlanName": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập tên cho kế hoạch",
    ),
    "mil": MessageLookupByLibrary.simpleMessage("triệu"),
    "minValueTransfer": MessageLookupByLibrary.simpleMessage(
      "Số tiền chuyển tối thiểu là 1 đ. Vui lòng thử lại.",
    ),
    "money": MessageLookupByLibrary.simpleMessage("Tiền"),
    "moneyCategory": MessageLookupByLibrary.simpleMessage("Số tiền tích luỹ"),
    "moneySource": MessageLookupByLibrary.simpleMessage("Nguồn tiền"),
    "month": MessageLookupByLibrary.simpleMessage("tháng"),
    "monthlyInvestmentDay": MessageLookupByLibrary.simpleMessage(
      "Ngày đầu tư định kỳ hàng tháng là ngày",
    ),
    "mpOtp": MessageLookupByLibrary.simpleMessage(
      "Mã OTP được gửi từ ngân hàng MBBank đến số điện thoại",
    ),
    "myCategory": MessageLookupByLibrary.simpleMessage("Danh mục của tôi"),
    "nameAlreadyExists": MessageLookupByLibrary.simpleMessage("Tên đã tồn tại"),
    "nameCategory": MessageLookupByLibrary.simpleMessage("Tên danh sách"),
    "newCategory": MessageLookupByLibrary.simpleMessage("Danh sách mới"),
    "newOrderInterfaceIntro1": MessageLookupByLibrary.simpleMessage(
      "Giao diện đặt lệnh nâng cao mới",
    ),
    "newOrderInterfaceIntro2": MessageLookupByLibrary.simpleMessage(
      "Để thay đổi giao diện đặt lệnh truy cập",
    ),
    "newOrderInterfaceIntro3": MessageLookupByLibrary.simpleMessage(
      "Cài đặt -> Cấu hình giao dịch",
    ),
    "nextInvestmentPeriod": MessageLookupByLibrary.simpleMessage(
      "Kỳ đầu tư tiếp theo",
    ),
    "nextQuestion": MessageLookupByLibrary.simpleMessage("Câu hỏi tiếp theo"),
    "no": MessageLookupByLibrary.simpleMessage("Không"),
    "noData": MessageLookupByLibrary.simpleMessage("Hiện tại không có dữ liệu"),
    "noDataFound": MessageLookupByLibrary.simpleMessage(
      "Không tìm thấy dữ liệu",
    ),
    "noFilter": MessageLookupByLibrary.simpleMessage(
      "Không có kết quả nào phù hợp với bộ lọc. Vui lòng thay đổi điều kiện lọc và thử lại",
    ),
    "noHoldingPortfolio": MessageLookupByLibrary.simpleMessage(
      "Bạn chưa nắm giữ cổ phiếu nào",
    ),
    "noHoldingPortfolioForSell": MessageLookupByLibrary.simpleMessage(
      "Bạn chưa sở hữu mã cổ phiếu nào có thể bán",
    ),
    "noOrder": MessageLookupByLibrary.simpleMessage("Không có lệnh"),
    "noStock": MessageLookupByLibrary.simpleMessage("Danh sách chưa có mã nào"),
    "notAddSymbolFollowList": MessageLookupByLibrary.simpleMessage(
      "Thêm vào danh sách Mã CK đang chọn thất bại",
    ),
    "notConfirmCBTTClose": MessageLookupByLibrary.simpleMessage("Thông báo"),
    "noteAboutInvestmentTime": MessageLookupByLibrary.simpleMessage(
      "Thời gian đầu tư tối thiểu từ 1 năm, tối đa là 50 năm.",
    ),
    "noteAboutPeriodicInvestmentAmount": MessageLookupByLibrary.simpleMessage(
      "Nên phân bổ đầu tư tối thiểu bao nhiêu % tổng thu nhập?",
    ),
    "noteCanNotEditCommand": MessageLookupByLibrary.simpleMessage(
      "Bạn không thể sửa lệnh do không còn nắm giữ mã chứng khoán",
    ),
    "notificationCashIn": MessageLookupByLibrary.simpleMessage("Thông báo"),
    "numberBlockedShares": MessageLookupByLibrary.simpleMessage("CP phong tỏa"),
    "numberSharesTraded": MessageLookupByLibrary.simpleMessage("CP giao dịch"),
    "oddLot": MessageLookupByLibrary.simpleMessage("Lô lẻ"),
    "offerForSaleList": MessageLookupByLibrary.simpleMessage(
      "Danh sách chào bán",
    ),
    "offerList": MessageLookupByLibrary.simpleMessage("Danh sách cổ phiếu"),
    "operational": MessageLookupByLibrary.simpleMessage("Hiệu quả tích sản"),
    "orderConfirmLate": MessageLookupByLibrary.simpleMessage("Để sau"),
    "orderDescriptionAtc": MessageLookupByLibrary.simpleMessage(
      "Lệnh mua hoặc lệnh bán được thực hiện tại thời điểm giá đang đóng cửa",
    ),
    "orderDescriptionAto": MessageLookupByLibrary.simpleMessage(
      "Lệnh tranh mua bán tại mức giá mở cửa",
    ),
    "orderDescriptionBuyin": MessageLookupByLibrary.simpleMessage(
      "Lệnh bán áp dụng với các mã chứng khoán trong phiên Buy-in từ 8h45 - 9h00",
    ),
    "orderDescriptionGtc": MessageLookupByLibrary.simpleMessage(
      "Lệnh được kích hoạt khi giá đặt lệnh nằm trong khoảng trần - sàn của ngày giao dịch có hiệu lực trong khoảng thời gian thiết lập",
    ),
    "orderDescriptionLo": MessageLookupByLibrary.simpleMessage(
      "Lệnh đặt mua - bán chứng khoán theo giá mong muốn",
    ),
    "orderDescriptionMak": MessageLookupByLibrary.simpleMessage(
      "Lệnh thị trường khớp và huỷ tức có thể thực hiện toàn bộ hoặc một phần phần còn lại sẽ bị huỷ ngay sau khi khớp lệnh",
    ),
    "orderDescriptionMok": MessageLookupByLibrary.simpleMessage(
      "Lệnh thị trường khớp toàn bộ hoặc huỷ nếu không thực hiện được toàn bộ thì bị huỷ ngay sau khi nhập",
    ),
    "orderDescriptionMp": MessageLookupByLibrary.simpleMessage(
      "Lệnh đặt mua - bán chứng khoán với bất kỳ mức giá nào trong thời gian giao dịch",
    ),
    "orderDescriptionMtl": MessageLookupByLibrary.simpleMessage(
      "Lệnh thị trường giới hạn nếu không thực hiện được toàn bộ thì phần còn lại chuyển thành lệnh LO và áp dụng các quy định về sửa huỷ đối với lệnh LO",
    ),
    "orderDescriptionPlo": MessageLookupByLibrary.simpleMessage(
      "Lệnh mua hoặc bán chứng khoán tại mức giá đóng cửa sau khi kết thúc phiên ATC",
    ),
    "orderFundCertificates": MessageLookupByLibrary.simpleMessage(
      "Đặt mua chứng chỉ quỹ",
    ),
    "orderPrice": MessageLookupByLibrary.simpleMessage("Giá đặt lệnh"),
    "orderTime": MessageLookupByLibrary.simpleMessage("Thời gian đặt lệnh"),
    "orderTypeAtc": MessageLookupByLibrary.simpleMessage("Lệnh ATC"),
    "orderTypeAto": MessageLookupByLibrary.simpleMessage("Lệnh ATO"),
    "orderTypeBuyin": MessageLookupByLibrary.simpleMessage("Lệnh Buy-in"),
    "orderTypeCondition": MessageLookupByLibrary.simpleMessage(
      "Lệnh điều kiện",
    ),
    "orderTypeGtc": MessageLookupByLibrary.simpleMessage("Lệnh GTC"),
    "orderTypeLo": MessageLookupByLibrary.simpleMessage("Lệnh thường"),
    "orderTypeMak": MessageLookupByLibrary.simpleMessage("Lệnh MAK"),
    "orderTypeMok": MessageLookupByLibrary.simpleMessage("Lệnh MOK"),
    "orderTypeMp": MessageLookupByLibrary.simpleMessage("Lệnh MP"),
    "orderTypeMtl": MessageLookupByLibrary.simpleMessage("Lệnh MTL"),
    "orderTypePlo": MessageLookupByLibrary.simpleMessage("Lệnh PLO"),
    "orderValue": MessageLookupByLibrary.simpleMessage("Giá trị lệnh"),
    "orderValue2": MessageLookupByLibrary.simpleMessage("Giá trị lệnh đặt"),
    "ordinary": MessageLookupByLibrary.simpleMessage("TK thường"),
    "ordinaryFull": MessageLookupByLibrary.simpleMessage("Tiểu khoản thường"),
    "organization": MessageLookupByLibrary.simpleMessage("Tổ chức"),
    "otherVolume": MessageLookupByLibrary.simpleMessage("KL khác"),
    "otm": MessageLookupByLibrary.simpleMessage("Đang lỗ"),
    "overTransferMoney": MessageLookupByLibrary.simpleMessage(
      "Vượt quá số dư tài khoản",
    ),
    "overbought": MessageLookupByLibrary.simpleMessage("Dư mua"),
    "oversold": MessageLookupByLibrary.simpleMessage("Dư bán"),
    "pendingBuyCommand": MessageLookupByLibrary.simpleMessage("Lệnh chờ mua"),
    "pendingOrderCommandDescription": MessageLookupByLibrary.simpleMessage(
      "Lệnh được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo điều kiện NĐT cài đặt",
    ),
    "pendingSellCommand": MessageLookupByLibrary.simpleMessage("Lệnh chờ bán"),
    "percenCompleted": MessageLookupByLibrary.simpleMessage(
      "Tỷ lệ hoàn thành mục tiêu",
    ),
    "periodicInvestmentAmount": MessageLookupByLibrary.simpleMessage(
      "Số tiền đầu tư định kỳ",
    ),
    "periodicInvestmentDate": MessageLookupByLibrary.simpleMessage(
      "Ngày đầu tư định kỳ",
    ),
    "periodicInvestmentStartDate": MessageLookupByLibrary.simpleMessage(
      "Ngày bắt đầu đầu tư",
    ),
    "placeVolume": MessageLookupByLibrary.simpleMessage("KL đặt"),
    "plan": MessageLookupByLibrary.simpleMessage("Kế hoạch"),
    "pleaseEnterPrice": MessageLookupByLibrary.simpleMessage(
      "Vui lòng điền giá",
    ),
    "pleaseInputVolume": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập khối lượng",
    ),
    "pleaseSelectMax": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chọn tối đa {} mã",
    ),
    "price": MessageLookupByLibrary.simpleMessage("Giá"),
    "priceCeiling": MessageLookupByLibrary.simpleMessage("Trần"),
    "priceFloor": MessageLookupByLibrary.simpleMessage("Sàn"),
    "priceHigh": MessageLookupByLibrary.simpleMessage("Cao"),
    "priceList": MessageLookupByLibrary.simpleMessage("Bảng giá"),
    "priceLow": MessageLookupByLibrary.simpleMessage("Thấp"),
    "priceOrder": MessageLookupByLibrary.simpleMessage("Giá đặt"),
    "pricePrice": MessageLookupByLibrary.simpleMessage("Giá khớp"),
    "priceSlippageInvalid": MessageLookupByLibrary.simpleMessage(
      "Biên trượt giá không hợp lệ",
    ),
    "priceThresholdDialogContent": MessageLookupByLibrary.simpleMessage(
      "Tiềm năng tăng giá danh mục hiện tại nhỏ hơn 10% so với giá kỳ vọng. Quý khách cân nhắc trước khi đầu tư theo danh mục!",
    ),
    "priceThresholdDialogTitle": MessageLookupByLibrary.simpleMessage(
      "Cân nhắc phương án đầu tư",
    ),
    "processingOrder": MessageLookupByLibrary.simpleMessage("lệnh đang xử lý"),
    "profit": MessageLookupByLibrary.simpleMessage("Lợi nhuận"),
    "profitLoss": MessageLookupByLibrary.simpleMessage("Lãi/lỗ"),
    "profitLossExpected": MessageLookupByLibrary.simpleMessage(
      "Lãi/lỗ dự kiến",
    ),
    "profitLossToday": MessageLookupByLibrary.simpleMessage("Lãi/lỗ hôm nay"),
    "profitMargin": MessageLookupByLibrary.simpleMessage("Biên giá chốt lời"),
    "publicDate": MessageLookupByLibrary.simpleMessage("Ngày công bố"),
    "purchasingAbility": MessageLookupByLibrary.simpleMessage("Sức mua"),
    "rate": MessageLookupByLibrary.simpleMessage("Tỷ lệ"),
    "rateInvestmentAmount": MessageLookupByLibrary.simpleMessage(
      "Tỷ lệ phân bổ",
    ),
    "redo": MessageLookupByLibrary.simpleMessage("Làm lại"),
    "removeCategory": MessageLookupByLibrary.simpleMessage("Đã xóa danh sách"),
    "reset": MessageLookupByLibrary.simpleMessage("Đặt lại"),
    "retry": MessageLookupByLibrary.simpleMessage("Thử lại"),
    "revenue": MessageLookupByLibrary.simpleMessage("Doanh thu"),
    "scheduleInvestment": MessageLookupByLibrary.simpleMessage(
      "Ngày đầu tư định kỳ",
    ),
    "search": MessageLookupByLibrary.simpleMessage("Tìm kiếm"),
    "searchStocks": MessageLookupByLibrary.simpleMessage(
      "Tìm kiếm mã cổ phiếu",
    ),
    "searchStocks2": MessageLookupByLibrary.simpleMessage("Tìm mã chứng khoán"),
    "searchSymbol": MessageLookupByLibrary.simpleMessage("Tìm mã"),
    "seeNow": MessageLookupByLibrary.simpleMessage("Xem ngay"),
    "seeSample": MessageLookupByLibrary.simpleMessage("Xem minh hoạ"),
    "selectLinkingAccount": MessageLookupByLibrary.simpleMessage(
      "Chọn tài khoản đã liên kết",
    ),
    "selectedSymbolDeleted": MessageLookupByLibrary.simpleMessage(
      "Đã xoá tất cả mã CK đang chọn",
    ),
    "selectedSymbols": MessageLookupByLibrary.simpleMessage(
      "Mã chứng khoán đang chọn",
    ),
    "selfCreatedDirectory": MessageLookupByLibrary.simpleMessage(
      "Danh mục tự tạo",
    ),
    "setCommand": MessageLookupByLibrary.simpleMessage("Đặt lệnh"),
    "share": MessageLookupByLibrary.simpleMessage("Cổ phiếu"),
    "shortExrightDate": MessageLookupByLibrary.simpleMessage("Ngày GDKHQ"),
    "showMessageNextTime": MessageLookupByLibrary.simpleMessage(
      "Hiển thị thông báo này trong lần sau",
    ),
    "skip": MessageLookupByLibrary.simpleMessage("Bỏ qua"),
    "slippageMargin": MessageLookupByLibrary.simpleMessage("Biên trượt giá"),
    "startSurvey": MessageLookupByLibrary.simpleMessage("Bắt đầu khảo sát"),
    "status": MessageLookupByLibrary.simpleMessage("Trạng thái"),
    "statusTitle": MessageLookupByLibrary.simpleMessage("Trạng thái"),
    "stock": MessageLookupByLibrary.simpleMessage("Cổ phiếu"),
    "stockFilter": MessageLookupByLibrary.simpleMessage("Bộ lọc cổ phiếu"),
    "stockType": MessageLookupByLibrary.simpleMessage("Lệnh"),
    "stocksStockFunds": MessageLookupByLibrary.simpleMessage(
      "Cổ phiếu/Quỹ cổ phiếu:",
    ),
    "stopLoss": MessageLookupByLibrary.simpleMessage("Cắt lỗ"),
    "stopLossCommand": MessageLookupByLibrary.simpleMessage("Lệnh cắt lỗ"),
    "stopLossCommandDescription": MessageLookupByLibrary.simpleMessage(
      "Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động đến điểm cắt lỗ của NĐT",
    ),
    "stopLossMargin": MessageLookupByLibrary.simpleMessage("Biên giá cắt lỗ"),
    "stopLossRate": MessageLookupByLibrary.simpleMessage("Tỷ lệ cắt lỗ"),
    "stopLossRatePercent": MessageLookupByLibrary.simpleMessage(
      "Tỷ lệ cắt lỗ (%)",
    ),
    "stopWealth": MessageLookupByLibrary.simpleMessage("Dừng đầu tư"),
    "strOk": MessageLookupByLibrary.simpleMessage("Đã hiểu"),
    "successOrderPlace": MessageLookupByLibrary.simpleMessage(
      "Đặt lệnh thành công",
    ),
    "suggestedByVPBankS": MessageLookupByLibrary.simpleMessage(
      "Danh mục VPBankS khuyến nghị",
    ),
    "suggestedByVPBankSContent": MessageLookupByLibrary.simpleMessage(
      "Danh mục cổ phiếu VPBankS đánh giá và chọn lọc theo các ngành trên thị trường",
    ),
    "symbol": MessageLookupByLibrary.simpleMessage("Mã"),
    "symbolSuggestionByVPBankS": MessageLookupByLibrary.simpleMessage(
      "Đây là danh sách cổ phiếu chọn lọc bởi VPBankS.",
    ),
    "systemSendOTP": MessageLookupByLibrary.simpleMessage(
      "Hệ thống đã gửi OTP vào số điện thoại của bạn. Vui lòng nhập mã OTP đã nhận",
    ),
    "takeProfit": MessageLookupByLibrary.simpleMessage("Chốt lời"),
    "takeProfitCommand": MessageLookupByLibrary.simpleMessage("Lệnh chốt lời"),
    "takeProfitCommandDescription": MessageLookupByLibrary.simpleMessage(
      "Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo kỳ vọng chốt lời của NĐT",
    ),
    "takeProfitRatePercent": MessageLookupByLibrary.simpleMessage(
      "Tỷ lệ chốt lời (%)",
    ),
    "target": MessageLookupByLibrary.simpleMessage("Mục tiêu"),
    "targetInvestment": MessageLookupByLibrary.simpleMessage("Mục tiêu đầu tư"),
    "termsAndConditions": MessageLookupByLibrary.simpleMessage(
      "Điều khoản và điều kiện",
    ),
    "titleErrorBank": MessageLookupByLibrary.simpleMessage(
      "Ngân hàng đã được liên kết",
    ),
    "titlePlanName": MessageLookupByLibrary.simpleMessage("Tên kế hoạch"),
    "toTheList": MessageLookupByLibrary.simpleMessage("vào danh sách"),
    "topStocks": MessageLookupByLibrary.simpleMessage("Top cổ phiếu"),
    "totalAmountShares": MessageLookupByLibrary.simpleMessage(
      "Tổng số lượng CP",
    ),
    "totalCapital": MessageLookupByLibrary.simpleMessage("Tổng vốn"),
    "totalMass": MessageLookupByLibrary.simpleMessage("KL tổng"),
    "totalProfitLoss": MessageLookupByLibrary.simpleMessage("Tổng lãi/lỗ"),
    "totalValue": MessageLookupByLibrary.simpleMessage("Tổng giá trị"),
    "totalVolume": MessageLookupByLibrary.simpleMessage("Tổng KL"),
    "triggerCondition": MessageLookupByLibrary.simpleMessage(
      "Điều kiện kích hoạt",
    ),
    "tryNow": MessageLookupByLibrary.simpleMessage("Trải nghiệm ngay"),
    "ts": MessageLookupByLibrary.simpleMessage("Trạng thái GD"),
    "tt": MessageLookupByLibrary.simpleMessage("KLGD"),
    "tv": MessageLookupByLibrary.simpleMessage("GTGD"),
    "typeCommand": MessageLookupByLibrary.simpleMessage("Loại lệnh"),
    "unLink": MessageLookupByLibrary.simpleMessage("Huỷ liên kết"),
    "utilities": MessageLookupByLibrary.simpleMessage("Tiện ích"),
    "validateNameCategory": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập tên danh mục",
    ),
    "validateVolume": MessageLookupByLibrary.simpleMessage(
      "Khối lượng không hợp lệ",
    ),
    "value": MessageLookupByLibrary.simpleMessage("Giá trị"),
    "vnd": MessageLookupByLibrary.simpleMessage("đ"),
    "volTitle": MessageLookupByLibrary.simpleMessage("KL đặt/\nKL khớp"),
    "volume": MessageLookupByLibrary.simpleMessage("Khối lượng"),
    "volumeKL": MessageLookupByLibrary.simpleMessage("KL"),
    "volumeMustEvenLot": MessageLookupByLibrary.simpleMessage(
      "Khối lượng tối thiểu 100 và là bội số 100",
    ),
    "volumeWaiting": MessageLookupByLibrary.simpleMessage("KL mua chờ về"),
    "waitingCommand": MessageLookupByLibrary.simpleMessage("Lệnh chờ"),
    "warrants": MessageLookupByLibrary.simpleMessage("Chứng quyền"),
    "watchList": MessageLookupByLibrary.simpleMessage("Danh mục theo dõi"),
    "wealContinue": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
    "wealth": MessageLookupByLibrary.simpleMessage("Tích sản"),
    "wealthGoldsContent": MessageLookupByLibrary.simpleMessage(
      "Phù hợp với nhà đầu tư có kinh nghiệm, mong muốn tự thực hiện đầu tư và sẵn sàng chấp nhận rủi ro cao để tìm kiến mức lợi nhuận lớn.",
    ),
    "wealthPlan": MessageLookupByLibrary.simpleMessage("Bản kế hoạch"),
    "wealthProductDes": MessageLookupByLibrary.simpleMessage(
      "Là sản phẩm VPBankS hỗ trợ và đồng hành cùng quý khách hàng trên chặng đường đầu tư và tích lũy tài sản nhằm đạt được các mục tiêu tài chính mong muốn.Bắt đầu ngay hôm nay cùng VPBankS!",
    ),
    "wealthProductTitle": MessageLookupByLibrary.simpleMessage(
      "Sản phẩm Tích sản",
    ),
    "wealthStart": MessageLookupByLibrary.simpleMessage("Bắt đầu"),
    "whatAreYourInvestmentGoals": MessageLookupByLibrary.simpleMessage(
      "Bạn muốn đầu tư lĩnh vực nào?",
    ),
    "whatAreYourInvestmentGoalsContent": MessageLookupByLibrary.simpleMessage(
      "Chúng tôi đã hiểu khẩu vị rủi ro của bạn. Giờ hãy cho chúng tôi biết nhu cầu đầu tư của bạn.",
    ),
    "whenHaveEventToDiluteStock": MessageLookupByLibrary.simpleMessage(
      "Khi có sự kiện quyền pha loãng giá cổ phiếu",
    ),
    "withInvestmentFrequency": MessageLookupByLibrary.simpleMessage(
      "với tần suất",
    ),
    "wrongStepPrice": MessageLookupByLibrary.simpleMessage("Sai bước giá"),
    "wrongTransactionPrice": MessageLookupByLibrary.simpleMessage(
      "Giá giao dịch phải nằm trong khoảng trần - sàn",
    ),
    "year": MessageLookupByLibrary.simpleMessage("năm"),
    "yesContinueInvesting": MessageLookupByLibrary.simpleMessage(
      "Có, tiếp tục đầu tư",
    ),
    "yesStopInvesting": MessageLookupByLibrary.simpleMessage("Có, dừng đầu tư"),
    "youNotHoldingThisStock": MessageLookupByLibrary.simpleMessage(
      "Bạn không nắm giữ mã chứng khoán này",
    ),
  };
}
