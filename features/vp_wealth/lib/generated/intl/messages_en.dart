// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "accountIsNotLinkedToTheDelegation": MessageLookupByLibrary.simpleMessage(
      "Account is not linked to the delegation. Contact ********** for assistance.",
    ),
    "activationPrice": MessageLookupByLibrary.simpleMessage("Giá kích hoạt"),
    "active": MessageLookupByLibrary.simpleMessage("Active"),
    "add": MessageLookupByLibrary.simpleMessage("Add"),
    "addCode": MessageLookupByLibrary.simpleMessage("Add code"),
    "addNewCategory": MessageLookupByLibrary.simpleMessage("Add new category"),
    "addStock": MessageLookupByLibrary.simpleMessage(""),
    "addToCategory": MessageLookupByLibrary.simpleMessage("Add to category"),
    "addWatchList": MessageLookupByLibrary.simpleMessage("Add to watch list"),
    "added": MessageLookupByLibrary.simpleMessage("Added"),
    "addedSymbolFollowList": MessageLookupByLibrary.simpleMessage(
      "Đã thêm vào danh sách Mã CK đang chọn",
    ),
    "addedToList": MessageLookupByLibrary.simpleMessage("Added to list"),
    "allSymbol": MessageLookupByLibrary.simpleMessage("Tất cả mã"),
    "allocationPlan": MessageLookupByLibrary.simpleMessage("Allocation Plan"),
    "allocationRules": MessageLookupByLibrary.simpleMessage(
      "Allocation rules 50/30/20",
    ),
    "allocationRulesContent": MessageLookupByLibrary.simpleMessage(
      "Rule 50/30/20 is a popular rule recommended for effective personal financial management. Accordingly, 50% of income is spent on essential spending needs, 30% of income is used to serve personal needs, and the remaining 20% of income will be used for savings and investments. VPBankS recommends that customers allocate at least 20% of their monthly income to accumulated savings and investments, in order to achieve their future personal financial goals.",
    ),
    "allowMultiSelect": MessageLookupByLibrary.simpleMessage(
      "Cho phép chọn nhiều mã",
    ),
    "amountInitialStart": MessageLookupByLibrary.simpleMessage(
      "Amount Initial Start",
    ),
    "anErrorOccurred": MessageLookupByLibrary.simpleMessage(
      "An error occurred",
    ),
    "apply": MessageLookupByLibrary.simpleMessage("Apply"),
    "areYouCancelCreatePlan": MessageLookupByLibrary.simpleMessage(
      "Are you sure want to cancel\nwealth plan?",
    ),
    "asset": MessageLookupByLibrary.simpleMessage("Assets"),
    "atm": MessageLookupByLibrary.simpleMessage("Hòa vốn"),
    "autoAdjustTriggerPriceAndSetPrice": MessageLookupByLibrary.simpleMessage(
      "Tự động điều chỉnh giá kích hoạt & giá đặt",
    ),
    "availableBalances": MessageLookupByLibrary.simpleMessage("Balances"),
    "availableVolume": MessageLookupByLibrary.simpleMessage("KL khả dụng"),
    "averageCostPrice": MessageLookupByLibrary.simpleMessage(
      "Average cost price",
    ),
    "averageCostPriceConditional": MessageLookupByLibrary.simpleMessage(
      "Giá vốn bình quân",
    ),
    "averageMatchingPrice": MessageLookupByLibrary.simpleMessage(
      "Average matching price",
    ),
    "averagePrice": MessageLookupByLibrary.simpleMessage("Average price"),
    "back": MessageLookupByLibrary.simpleMessage("Quay lại"),
    "bankLinking": MessageLookupByLibrary.simpleMessage("Bank linking"),
    "bidPriceTitle": MessageLookupByLibrary.simpleMessage("Giá đặt/\nGiá khớp"),
    "bigger": MessageLookupByLibrary.simpleMessage("Lớn hơn"),
    "bil": MessageLookupByLibrary.simpleMessage("billion"),
    "bondRL": MessageLookupByLibrary.simpleMessage("Bonds RL"),
    "bondsfunds": MessageLookupByLibrary.simpleMessage("Bonds/Bond Funds:"),
    "buttonAccept": MessageLookupByLibrary.simpleMessage("Accept"),
    "buttonBack": MessageLookupByLibrary.simpleMessage("Back"),
    "buttonClose": MessageLookupByLibrary.simpleMessage("Close"),
    "buttonContinue": MessageLookupByLibrary.simpleMessage("Tiếp tục"),
    "buttonSaveChange": MessageLookupByLibrary.simpleMessage("Save change"),
    "buttonSeeDetails": MessageLookupByLibrary.simpleMessage("See details"),
    "canNotEditCommand": MessageLookupByLibrary.simpleMessage(
      "Không thể sửa lệnh",
    ),
    "canNotEditCommandWarning": MessageLookupByLibrary.simpleMessage(
      "Bạn không thể sửa lệnh do không còn nắm giữ\nmã chứng khoán",
    ),
    "cancelAll": MessageLookupByLibrary.simpleMessage("Hủy tất cả lệnh"),
    "cancelCreatePlan": MessageLookupByLibrary.simpleMessage(
      "Cancel create plan",
    ),
    "cancelLinkingAccountFail": MessageLookupByLibrary.simpleMessage(
      "Hủy liên kết tài khoản không thành công",
    ),
    "cancelLinkingAccountSuccess": MessageLookupByLibrary.simpleMessage(
      "Hủy liên kết tài khoản thành công",
    ),
    "cancelOrder": MessageLookupByLibrary.simpleMessage("Cancel order"),
    "cancelPlan": MessageLookupByLibrary.simpleMessage("Cancel wealth plan"),
    "cancelPlanPage": MessageLookupByLibrary.simpleMessage(
      "Cancel wealth plan",
    ),
    "cancelSurvey": MessageLookupByLibrary.simpleMessage("Cancel Survey"),
    "cancelSurveyNew": MessageLookupByLibrary.simpleMessage("Cancel Survey"),
    "cannotOrderOddLotWithMarketCommand": MessageLookupByLibrary.simpleMessage(
      "Không thể đặt lô lẻ với lệnh thị trường",
    ),
    "capitalCost": MessageLookupByLibrary.simpleMessage("Capital cost"),
    "capitalValue": MessageLookupByLibrary.simpleMessage("Capital value"),
    "cashInTitle": MessageLookupByLibrary.simpleMessage("CashIn"),
    "cashMoney": MessageLookupByLibrary.simpleMessage("Cash/Money"),
    "category": MessageLookupByLibrary.simpleMessage("Category"),
    "chooseInvestmentFrequency": MessageLookupByLibrary.simpleMessage(
      "Choose investment frequency",
    ),
    "chooseTarget": MessageLookupByLibrary.simpleMessage("Choose target"),
    "codeTitle": MessageLookupByLibrary.simpleMessage("Mã CK"),
    "comingSoon": MessageLookupByLibrary.simpleMessage("Coming soon"),
    "commandClose": MessageLookupByLibrary.simpleMessage("Đóng"),
    "commandDialogCancel": MessageLookupByLibrary.simpleMessage(
      "Xác nhận dừng đặt lệnh",
    ),
    "commandType": MessageLookupByLibrary.simpleMessage("Command type"),
    "completed": MessageLookupByLibrary.simpleMessage("Completed"),
    "conditionalOrderWaitingConfirmGuide": MessageLookupByLibrary.simpleMessage(
      "Bằng việc %&Xác nhận lệnh%&, bạn hiểu và chấp thuận với các lệnh giao dịch  được phát sinh khi thỏa mãn các điều kiện kích hoạt.",
    ),
    "conditionalOrderWaitingConfirmNote": MessageLookupByLibrary.simpleMessage(
      "Lệnh chỉ được kích hoạt 01 lần duy nhất trong thời gian hiệu lực.",
    ),
    "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
    "confirmDeleteAll": MessageLookupByLibrary.simpleMessage(
      "Bạn có muốn xóa tất cả cổ phiếu trong danh sách đã chọn không?",
    ),
    "confirmPlan": MessageLookupByLibrary.simpleMessage("Confirm Plan"),
    "contactCustomerService": MessageLookupByLibrary.simpleMessage(
      "Liên hệ DVKH",
    ),
    "continueInvesting": MessageLookupByLibrary.simpleMessage(
      "Continue investing",
    ),
    "coutionInfoPlan": MessageLookupByLibrary.simpleMessage(
      "Note: Investment amount less than market price of stock code will affect the order placement of the plan.",
    ),
    "createInvestmentTaste": MessageLookupByLibrary.simpleMessage(
      "Create your own investment taste",
    ),
    "date": MessageLookupByLibrary.simpleMessage("Ngày"),
    "deceptionLinkAccount": MessageLookupByLibrary.simpleMessage(
      "Bạn sẽ được chuyển đến trang web của ngân hàng đối tác. Bạn có muốn tiếp tục không?",
    ),
    "deceptionUnlink": MessageLookupByLibrary.simpleMessage(
      "Do you unregister linking",
    ),
    "deleteAll": MessageLookupByLibrary.simpleMessage("Xoá tất cả"),
    "deleteAllSelected": MessageLookupByLibrary.simpleMessage(
      "Xoá tất cả cổ phiếu đã chọn",
    ),
    "deposit": MessageLookupByLibrary.simpleMessage("TK ký quỹ"),
    "depositFull": MessageLookupByLibrary.simpleMessage("Tiểu khoản ký quỹ"),
    "descAddNewCategory": MessageLookupByLibrary.simpleMessage(
      "Name your new list",
    ),
    "descriptionInvestmentPhilosophy": MessageLookupByLibrary.simpleMessage(
      "Để xây dựng chiến lược đầu tư tích sản phù hợp, cùng VPBankS thực hiện bộ khảo sát để tìm ra phong cách đầu tư và danh mục tích sản bền vững.",
    ),
    "dilutionChoiceTitle": MessageLookupByLibrary.simpleMessage(
      "Khi có sự kiện quyền pha loãng giá cổ phiếu",
    ),
    "doYouWantToStopInvesting": MessageLookupByLibrary.simpleMessage(
      "Do you want to stop investing?",
    ),
    "downloadFail": MessageLookupByLibrary.simpleMessage("Download Fail"),
    "downloadSuccess": MessageLookupByLibrary.simpleMessage("Download Success"),
    "downloading": MessageLookupByLibrary.simpleMessage("Downloading"),
    "editCategory": MessageLookupByLibrary.simpleMessage(
      "Category updated successfully",
    ),
    "editPlanName": MessageLookupByLibrary.simpleMessage("Edit plan name"),
    "effectiveDate": MessageLookupByLibrary.simpleMessage("Ngày hiệu lực"),
    "effectiveTime": MessageLookupByLibrary.simpleMessage("Thời gian hiệu lực"),
    "emptyListFilterPlanWhenNoResultMatching":
        MessageLookupByLibrary.simpleMessage("There are no matching results"),
    "emptyListSearchWhenNoResultMatching": MessageLookupByLibrary.simpleMessage(
      "There are no matching results.\nPlease refer to the list of stocks\nselected by VPBankS.",
    ),
    "emptyListSearchWhenSymbolExist": MessageLookupByLibrary.simpleMessage(
      "The stock symbol already exists in your portfolio.\nPlease refer to additional stocks\nselected by VPBankS.",
    ),
    "emptyStockCategory": MessageLookupByLibrary.simpleMessage(
      "No Stock on category",
    ),
    "etf": MessageLookupByLibrary.simpleMessage("ETF"),
    "evenLot": MessageLookupByLibrary.simpleMessage("Lô Chẵn"),
    "expectValue": MessageLookupByLibrary.simpleMessage("Giá trị dự kiến"),
    "expected": MessageLookupByLibrary.simpleMessage("Expected"),
    "expectedGrowth": MessageLookupByLibrary.simpleMessage("Expected growth"),
    "expectedProfit": MessageLookupByLibrary.simpleMessage("Expected profit"),
    "expectedProfitLoss": MessageLookupByLibrary.simpleMessage(
      "Expected profit/loss",
    ),
    "exrightDate": MessageLookupByLibrary.simpleMessage(
      "Giao dịch không hưởng quyền",
    ),
    "feeTransfer": MessageLookupByLibrary.simpleMessage("Money Transfer Fee"),
    "filterDataEmpty": MessageLookupByLibrary.simpleMessage(
      "There are no results matching the filter. Please change the filter condition and try again",
    ),
    "followList": MessageLookupByLibrary.simpleMessage("Danh sách theo dõi"),
    "heldAsset": MessageLookupByLibrary.simpleMessage("Held Assets"),
    "hintTextEnterAmount": MessageLookupByLibrary.simpleMessage("Enter amount"),
    "hintTextEnterPlanName": MessageLookupByLibrary.simpleMessage(
      "Enter a plan name",
    ),
    "hintTextEnterYearNumber": MessageLookupByLibrary.simpleMessage(
      "Enter the year number",
    ),
    "hintTextSearchPlan": MessageLookupByLibrary.simpleMessage(
      "Search for plans",
    ),
    "hintTextSearchStockSymbol": MessageLookupByLibrary.simpleMessage(
      "Search for stock symbols",
    ),
    "historyStockCode": MessageLookupByLibrary.simpleMessage("Stock code"),
    "holdVolume": MessageLookupByLibrary.simpleMessage("Khối lượng nắm giữ"),
    "howDoYouAlignWithAnyInvestmentPhilosophy":
        MessageLookupByLibrary.simpleMessage(
          "How do you align with any investment philosophy?",
        ),
    "inDebit": MessageLookupByLibrary.simpleMessage("Debit"),
    "individual": MessageLookupByLibrary.simpleMessage("Cá nhân"),
    "initialInvestmentAmount": MessageLookupByLibrary.simpleMessage(
      "Initial investment amount",
    ),
    "initialInvestmentAmountAnd": MessageLookupByLibrary.simpleMessage(
      "And Initial investment amount",
    ),
    "inputAnswer": MessageLookupByLibrary.simpleMessage("Enter your answer"),
    "invalidActivationPrice": MessageLookupByLibrary.simpleMessage(
      "Giá kích hoạt không hợp lệ",
    ),
    "invalidConditionalPrice": MessageLookupByLibrary.simpleMessage(
      "Giá đặt không hợp lệ",
    ),
    "invalidInput": MessageLookupByLibrary.simpleMessage("Không hợp lệ"),
    "invalidMaxBuy": MessageLookupByLibrary.simpleMessage(
      "Vượt quá sức mua của tiểu khoản",
    ),
    "invalidMaxSell": MessageLookupByLibrary.simpleMessage(
      "Bạn không nắm giữ cổ phiếu này",
    ),
    "invalidMaxVolume": MessageLookupByLibrary.simpleMessage(
      "Vượt khối lượng tối đa là",
    ),
    "invalidMinMass": MessageLookupByLibrary.simpleMessage(
      "Khối lượng tối thiểu 100 và phải là bội số của 100",
    ),
    "invalidOddLot": MessageLookupByLibrary.simpleMessage(
      "Giao dịch lô lẻ chỉ sử dụng Lệnh LO",
    ),
    "invalidOutOfRange": MessageLookupByLibrary.simpleMessage(
      "Giá nhập phải nằm trong khoảng giá trần và giá sàn",
    ),
    "invalidRateProfit": MessageLookupByLibrary.simpleMessage("Không hợp lệ"),
    "invalidSlipPagePrice": MessageLookupByLibrary.simpleMessage(
      "Biên trượt không hợp lệ",
    ),
    "invalidStep10": MessageLookupByLibrary.simpleMessage(
      "Bước giá không hợp lệ phải chia hết cho 10 đ",
    ),
    "invalidStep100": MessageLookupByLibrary.simpleMessage(
      "Bước giá không hợp lệ phải chia hết cho 100 đ",
    ),
    "invalidStep50": MessageLookupByLibrary.simpleMessage(
      "Bước giá không hợp lệ phải chia hết cho 50 đ",
    ),
    "investmentCategory": MessageLookupByLibrary.simpleMessage(
      "Investment category",
    ),
    "investmentFrequency": MessageLookupByLibrary.simpleMessage(
      "Investment frequency",
    ),
    "investmentGoals": MessageLookupByLibrary.simpleMessage("Investment Goals"),
    "investmentPhilosophy": MessageLookupByLibrary.simpleMessage(
      "Investment Philosophy",
    ),
    "investmentTime": MessageLookupByLibrary.simpleMessage("Investment time"),
    "issueDate": MessageLookupByLibrary.simpleMessage("Ngày thực hiện"),
    "itm": MessageLookupByLibrary.simpleMessage("Đang lời"),
    "jointVolume": MessageLookupByLibrary.simpleMessage("Joint Volume"),
    "k": MessageLookupByLibrary.simpleMessage("k"),
    "less": MessageLookupByLibrary.simpleMessage("Nhỏ hơn"),
    "linkAccount": MessageLookupByLibrary.simpleMessage("Liên kết tài khoản"),
    "linkedAccount": MessageLookupByLibrary.simpleMessage("Linked account"),
    "marginRate": MessageLookupByLibrary.simpleMessage("Tỷ lệ vay"),
    "market": MessageLookupByLibrary.simpleMessage("Market"),
    "marketPrice": MessageLookupByLibrary.simpleMessage("Market price"),
    "marketPriceAcr": MessageLookupByLibrary.simpleMessage("MP"),
    "maxBuyVolume": MessageLookupByLibrary.simpleMessage("KL tối đa"),
    "maxMoneyTransfer": MessageLookupByLibrary.simpleMessage(
      "Maximum transfer amount",
    ),
    "maxVolumeIs": MessageLookupByLibrary.simpleMessage("Khối lượng tối đa là"),
    "messageErrorWhenEnterPlanName": MessageLookupByLibrary.simpleMessage(
      "Please enter a name for the plan",
    ),
    "mil": MessageLookupByLibrary.simpleMessage("mil"),
    "minValueTransfer": MessageLookupByLibrary.simpleMessage(
      "The minimum transfer amount is 1 VND. Please try again.",
    ),
    "money": MessageLookupByLibrary.simpleMessage("Tiền"),
    "moneyCategory": MessageLookupByLibrary.simpleMessage("Money Category"),
    "moneySource": MessageLookupByLibrary.simpleMessage("Money source"),
    "month": MessageLookupByLibrary.simpleMessage("tháng"),
    "monthlyInvestmentDay": MessageLookupByLibrary.simpleMessage(
      "The monthly recurring investment date is the date",
    ),
    "mpOtp": MessageLookupByLibrary.simpleMessage(
      "OTP code is sent from MBBank to phone number",
    ),
    "myCategory": MessageLookupByLibrary.simpleMessage("My category"),
    "nameAlreadyExists": MessageLookupByLibrary.simpleMessage(
      "Name already exists",
    ),
    "nameCategory": MessageLookupByLibrary.simpleMessage("Name category"),
    "newCategory": MessageLookupByLibrary.simpleMessage("New category"),
    "newOrderInterfaceIntro1": MessageLookupByLibrary.simpleMessage(
      "Giao diện đặt lệnh nâng cao mới",
    ),
    "newOrderInterfaceIntro2": MessageLookupByLibrary.simpleMessage(
      "Để thay đổi giao diện đặt lệnh truy cập",
    ),
    "newOrderInterfaceIntro3": MessageLookupByLibrary.simpleMessage(
      "Cài đặt -> Cấu hình giao dịch",
    ),
    "nextInvestmentPeriod": MessageLookupByLibrary.simpleMessage(
      "Next Investment Period",
    ),
    "nextQuestion": MessageLookupByLibrary.simpleMessage("The next question"),
    "no": MessageLookupByLibrary.simpleMessage("No"),
    "noData": MessageLookupByLibrary.simpleMessage("Hiện tại không có dữ liệu"),
    "noDataFound": MessageLookupByLibrary.simpleMessage(
      "Không tìm thấy dữ liệu",
    ),
    "noFilter": MessageLookupByLibrary.simpleMessage(
      "Không có kết quả nào phù hợp với bộ lọc. Vui lòng thay đổi điều kiện lọc và thử lại",
    ),
    "noHoldingPortfolio": MessageLookupByLibrary.simpleMessage(
      "You haven\'t held any shares yet",
    ),
    "noHoldingPortfolioForSell": MessageLookupByLibrary.simpleMessage(
      "You haven\'t owned any sellable stocks yet",
    ),
    "noOrder": MessageLookupByLibrary.simpleMessage("Không có lệnh"),
    "noStock": MessageLookupByLibrary.simpleMessage(
      "The list has no codes yet",
    ),
    "notAddSymbolFollowList": MessageLookupByLibrary.simpleMessage(
      "Thêm vào danh sách Mã CK đang chọn thất bại",
    ),
    "notConfirmCBTTClose": MessageLookupByLibrary.simpleMessage("Thông báo"),
    "noteAboutInvestmentTime": MessageLookupByLibrary.simpleMessage(
      "Minimum investment period is 1 year, maximum is 50 years.",
    ),
    "noteAboutPeriodicInvestmentAmount": MessageLookupByLibrary.simpleMessage(
      "The recommended minimum investment allocation is 20% of total income.",
    ),
    "noteCanNotEditCommand": MessageLookupByLibrary.simpleMessage(
      "Bạn không thể sửa lệnh do không còn nắm giữ mã chứng khoán",
    ),
    "notificationCashIn": MessageLookupByLibrary.simpleMessage("Notification"),
    "numberBlockedShares": MessageLookupByLibrary.simpleMessage(
      "Blocked shares",
    ),
    "numberSharesTraded": MessageLookupByLibrary.simpleMessage("Shares traded"),
    "oddLot": MessageLookupByLibrary.simpleMessage("Lô lẻ"),
    "offerForSaleList": MessageLookupByLibrary.simpleMessage(
      "Offer for sale list",
    ),
    "offerList": MessageLookupByLibrary.simpleMessage("Offer list"),
    "operational": MessageLookupByLibrary.simpleMessage("Operational"),
    "orderConfirmLate": MessageLookupByLibrary.simpleMessage("Để sau"),
    "orderDescriptionAtc": MessageLookupByLibrary.simpleMessage(
      "Lệnh mua hoặc lệnh bán được thực hiện tại thời điểm giá đang đóng cửa",
    ),
    "orderDescriptionAto": MessageLookupByLibrary.simpleMessage(
      "Lệnh tranh mua bán tại mức giá mở cửa",
    ),
    "orderDescriptionBuyin": MessageLookupByLibrary.simpleMessage(
      "Lệnh bán áp dụng với các mã chứng khoán trong phiên Buy-in từ 8h45 - 9h00",
    ),
    "orderDescriptionGtc": MessageLookupByLibrary.simpleMessage(
      "Lệnh được kích hoạt khi giá đặt lệnh nằm trong khoảng trần - sàn của ngày giao dịch có hiệu lực trong khoảng thời gian thiết lập",
    ),
    "orderDescriptionLo": MessageLookupByLibrary.simpleMessage(
      "Lệnh đặt mua - bán chứng khoán theo giá mong muốn",
    ),
    "orderDescriptionMak": MessageLookupByLibrary.simpleMessage(
      "Lệnh thị trường khớp và huỷ tức có thể thực hiện toàn bộ hoặc một phần phần còn lại sẽ bị huỷ ngay sau khi khớp lệnh",
    ),
    "orderDescriptionMok": MessageLookupByLibrary.simpleMessage(
      "Lệnh thị trường khớp toàn bộ hoặc huỷ nếu không thực hiện được toàn bộ thì bị huỷ ngay sau khi nhập",
    ),
    "orderDescriptionMp": MessageLookupByLibrary.simpleMessage(
      "Lệnh đặt mua - bán chứng khoán với bất kỳ mức giá nào trong thời gian giao dịch",
    ),
    "orderDescriptionMtl": MessageLookupByLibrary.simpleMessage(
      "Lệnh thị trường giới hạn nếu không thực hiện được toàn bộ thì phần còn lại chuyển thành lệnh LO và áp dụng các quy định về sửa huỷ đối với lệnh LO",
    ),
    "orderDescriptionPlo": MessageLookupByLibrary.simpleMessage(
      "Lệnh mua hoặc bán chứng khoán tại mức giá đóng cửa sau khi kết thúc phiên ATC",
    ),
    "orderFundCertificates": MessageLookupByLibrary.simpleMessage(
      "Order fund certificates",
    ),
    "orderPrice": MessageLookupByLibrary.simpleMessage("Order price"),
    "orderTime": MessageLookupByLibrary.simpleMessage("Order time"),
    "orderTypeAtc": MessageLookupByLibrary.simpleMessage("Lệnh ATC"),
    "orderTypeAto": MessageLookupByLibrary.simpleMessage("Lệnh ATO"),
    "orderTypeBuyin": MessageLookupByLibrary.simpleMessage("Lệnh Buy-in"),
    "orderTypeCondition": MessageLookupByLibrary.simpleMessage(
      "Lệnh điều kiện",
    ),
    "orderTypeGtc": MessageLookupByLibrary.simpleMessage("Lệnh GTC"),
    "orderTypeLo": MessageLookupByLibrary.simpleMessage("Lệnh thường"),
    "orderTypeMak": MessageLookupByLibrary.simpleMessage("Lệnh MAK"),
    "orderTypeMok": MessageLookupByLibrary.simpleMessage("Lệnh MOK"),
    "orderTypeMp": MessageLookupByLibrary.simpleMessage("Lệnh MP"),
    "orderTypeMtl": MessageLookupByLibrary.simpleMessage("Lệnh MTL"),
    "orderTypePlo": MessageLookupByLibrary.simpleMessage("Lệnh PLO"),
    "orderValue": MessageLookupByLibrary.simpleMessage("Giá trị lệnh"),
    "orderValue2": MessageLookupByLibrary.simpleMessage("Order value"),
    "ordinary": MessageLookupByLibrary.simpleMessage("TK thường"),
    "ordinaryFull": MessageLookupByLibrary.simpleMessage("Tiểu khoản thường"),
    "organization": MessageLookupByLibrary.simpleMessage("Tổ chức"),
    "otherVolume": MessageLookupByLibrary.simpleMessage("Other volume"),
    "otm": MessageLookupByLibrary.simpleMessage("Đang lỗ"),
    "overTransferMoney": MessageLookupByLibrary.simpleMessage(
      "The account balance is not enough to make the transaction",
    ),
    "overbought": MessageLookupByLibrary.simpleMessage("Dư mua"),
    "oversold": MessageLookupByLibrary.simpleMessage("Dư bán"),
    "pendingBuyCommand": MessageLookupByLibrary.simpleMessage("Lệnh chờ mua"),
    "pendingOrderCommandDescription": MessageLookupByLibrary.simpleMessage(
      "Lệnh được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo điều kiện NĐT cài đặt",
    ),
    "pendingSellCommand": MessageLookupByLibrary.simpleMessage("Lệnh chờ bán"),
    "percenCompleted": MessageLookupByLibrary.simpleMessage(
      "Percent Completed",
    ),
    "periodicInvestmentAmount": MessageLookupByLibrary.simpleMessage(
      "Periodic investment amount",
    ),
    "periodicInvestmentDate": MessageLookupByLibrary.simpleMessage(
      "Periodic Investment Date",
    ),
    "periodicInvestmentStartDate": MessageLookupByLibrary.simpleMessage(
      "Periodic investment start date",
    ),
    "placeVolume": MessageLookupByLibrary.simpleMessage("KL đặt"),
    "plan": MessageLookupByLibrary.simpleMessage("Plan"),
    "pleaseEnterPrice": MessageLookupByLibrary.simpleMessage(
      "Vui lòng điền giá",
    ),
    "pleaseInputVolume": MessageLookupByLibrary.simpleMessage(
      "Vui lòng nhập khối lượng",
    ),
    "pleaseSelectMax": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chọn tối đa {} mã",
    ),
    "price": MessageLookupByLibrary.simpleMessage("Giá"),
    "priceCeiling": MessageLookupByLibrary.simpleMessage("Trần"),
    "priceFloor": MessageLookupByLibrary.simpleMessage("Sàn"),
    "priceHigh": MessageLookupByLibrary.simpleMessage("Cao"),
    "priceList": MessageLookupByLibrary.simpleMessage("Price list"),
    "priceLow": MessageLookupByLibrary.simpleMessage("Thấp"),
    "priceOrder": MessageLookupByLibrary.simpleMessage("Giá đặt"),
    "pricePrice": MessageLookupByLibrary.simpleMessage("Giá khớp"),
    "priceSlippageInvalid": MessageLookupByLibrary.simpleMessage(
      "Biên trượt giá không hợp lệ",
    ),
    "priceThresholdDialogContent": MessageLookupByLibrary.simpleMessage(
      "Tiềm năng tăng giá danh mục hiện tại nhỏ hơn 10% so với giá kỳ vọng. Quý khách cân nhắc trước khi đầu tư theo danh mục!",
    ),
    "priceThresholdDialogTitle": MessageLookupByLibrary.simpleMessage(
      "Cân nhắc phương án đầu tư",
    ),
    "processingOrder": MessageLookupByLibrary.simpleMessage("lệnh đang xử lý"),
    "profit": MessageLookupByLibrary.simpleMessage("Lợi nhuận"),
    "profitLoss": MessageLookupByLibrary.simpleMessage("Profit/Loss"),
    "profitLossExpected": MessageLookupByLibrary.simpleMessage(
      "Expected profit/loss",
    ),
    "profitLossToday": MessageLookupByLibrary.simpleMessage(
      "Profit/loss today",
    ),
    "profitMargin": MessageLookupByLibrary.simpleMessage("Biên giá chốt lời"),
    "publicDate": MessageLookupByLibrary.simpleMessage("Ngày công bố"),
    "purchasingAbility": MessageLookupByLibrary.simpleMessage("Sức mua"),
    "rate": MessageLookupByLibrary.simpleMessage("Rate"),
    "rateInvestmentAmount": MessageLookupByLibrary.simpleMessage(""),
    "redo": MessageLookupByLibrary.simpleMessage("Redo"),
    "removeCategory": MessageLookupByLibrary.simpleMessage("Deleted category"),
    "reset": MessageLookupByLibrary.simpleMessage("Reset"),
    "retry": MessageLookupByLibrary.simpleMessage("Retry"),
    "revenue": MessageLookupByLibrary.simpleMessage("Doanh thu"),
    "scheduleInvestment": MessageLookupByLibrary.simpleMessage(
      "Schedule Investment",
    ),
    "search": MessageLookupByLibrary.simpleMessage("Search"),
    "searchStocks": MessageLookupByLibrary.simpleMessage("Search stocks"),
    "searchStocks2": MessageLookupByLibrary.simpleMessage("Search securities"),
    "searchSymbol": MessageLookupByLibrary.simpleMessage("Tìm mã"),
    "selectLinkingAccount": MessageLookupByLibrary.simpleMessage(
      "Select linking account",
    ),
    "selectedSymbolDeleted": MessageLookupByLibrary.simpleMessage(
      "Đã xoá tất cả mã CK đang chọn",
    ),
    "selectedSymbols": MessageLookupByLibrary.simpleMessage(
      "Mã chứng khoán đang chọn",
    ),
    "selfCreatedDirectory": MessageLookupByLibrary.simpleMessage(
      "Self-created directory",
    ),
    "setCommand": MessageLookupByLibrary.simpleMessage("Set command"),
    "share": MessageLookupByLibrary.simpleMessage("Cổ phiếu"),
    "shortExrightDate": MessageLookupByLibrary.simpleMessage("Ngày GDKHQ"),
    "showMessageNextTime": MessageLookupByLibrary.simpleMessage(
      "Hiển thị thông báo này trong lần sau",
    ),
    "skip": MessageLookupByLibrary.simpleMessage("Skip"),
    "slippageMargin": MessageLookupByLibrary.simpleMessage("Biên trượt giá"),
    "startSurvey": MessageLookupByLibrary.simpleMessage("Start Survey"),
    "status": MessageLookupByLibrary.simpleMessage("Trạng thái"),
    "statusTitle": MessageLookupByLibrary.simpleMessage("Trạng thái"),
    "stock": MessageLookupByLibrary.simpleMessage("Stock"),
    "stockFilter": MessageLookupByLibrary.simpleMessage("Stock filter"),
    "stockType": MessageLookupByLibrary.simpleMessage("Stock type"),
    "stocksStockFunds": MessageLookupByLibrary.simpleMessage(
      "Stocks/Stock Funds:",
    ),
    "stopLoss": MessageLookupByLibrary.simpleMessage("Cắt lỗ"),
    "stopLossCommand": MessageLookupByLibrary.simpleMessage("Lệnh cắt lỗ"),
    "stopLossCommandDescription": MessageLookupByLibrary.simpleMessage(
      "Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động đến điểm cắt lỗ của NĐT",
    ),
    "stopLossMargin": MessageLookupByLibrary.simpleMessage("Biên giá cắt lỗ"),
    "stopLossRate": MessageLookupByLibrary.simpleMessage("Tỷ lệ cắt lỗ"),
    "stopLossRatePercent": MessageLookupByLibrary.simpleMessage(
      "Tỷ lệ cắt lỗ (%)",
    ),
    "stopWealth": MessageLookupByLibrary.simpleMessage("Stop Wealth"),
    "strOk": MessageLookupByLibrary.simpleMessage("OK"),
    "successOrderPlace": MessageLookupByLibrary.simpleMessage(
      "Đặt lệnh thành công",
    ),
    "suggestedByVPBankS": MessageLookupByLibrary.simpleMessage(
      "Suggestion by VPBankS",
    ),
    "suggestedByVPBankSContent": MessageLookupByLibrary.simpleMessage(""),
    "symbol": MessageLookupByLibrary.simpleMessage("Mã"),
    "symbolSuggestionByVPBankS": MessageLookupByLibrary.simpleMessage(
      "This is a list of stocks selected by VPBankS.",
    ),
    "systemSendOTP": MessageLookupByLibrary.simpleMessage(
      "The system has sent OTP to your phone number. Please enter the OTP received",
    ),
    "takeProfit": MessageLookupByLibrary.simpleMessage("Chốt lời"),
    "takeProfitCommand": MessageLookupByLibrary.simpleMessage("Lệnh chốt lời"),
    "takeProfitCommandDescription": MessageLookupByLibrary.simpleMessage(
      "Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo kỳ vọng chốt lời của NĐT",
    ),
    "takeProfitRatePercent": MessageLookupByLibrary.simpleMessage(
      "Tỷ lệ chốt lời (%)",
    ),
    "target": MessageLookupByLibrary.simpleMessage("Target"),
    "targetInvestment": MessageLookupByLibrary.simpleMessage(
      "Target Investment",
    ),
    "termsAndConditions": MessageLookupByLibrary.simpleMessage(
      "Terms and Conditions",
    ),
    "titleErrorBank": MessageLookupByLibrary.simpleMessage("Bank had linked"),
    "titlePlanName": MessageLookupByLibrary.simpleMessage("Plan name"),
    "toTheList": MessageLookupByLibrary.simpleMessage("to the list"),
    "topStocks": MessageLookupByLibrary.simpleMessage("Top stocks"),
    "totalAmountShares": MessageLookupByLibrary.simpleMessage(
      "Total number of shares",
    ),
    "totalCapital": MessageLookupByLibrary.simpleMessage("Total capital"),
    "totalMass": MessageLookupByLibrary.simpleMessage("Total mass"),
    "totalProfitLoss": MessageLookupByLibrary.simpleMessage(
      "Total profit/loss",
    ),
    "totalValue": MessageLookupByLibrary.simpleMessage("Total value"),
    "totalVolume": MessageLookupByLibrary.simpleMessage("Total volume"),
    "triggerCondition": MessageLookupByLibrary.simpleMessage(
      "Điều kiện kích hoạt",
    ),
    "tryNow": MessageLookupByLibrary.simpleMessage("Trải nghiệm ngay"),
    "ts": MessageLookupByLibrary.simpleMessage("TS"),
    "tt": MessageLookupByLibrary.simpleMessage("TT"),
    "tv": MessageLookupByLibrary.simpleMessage("TV"),
    "typeCommand": MessageLookupByLibrary.simpleMessage("Loại lệnh"),
    "unLink": MessageLookupByLibrary.simpleMessage("unLink"),
    "utilities": MessageLookupByLibrary.simpleMessage("Utilities"),
    "validateNameCategory": MessageLookupByLibrary.simpleMessage(
      "Please enter a category name",
    ),
    "validateVolume": MessageLookupByLibrary.simpleMessage(
      "Khối lượng không hợp lệ",
    ),
    "value": MessageLookupByLibrary.simpleMessage("Giá trị"),
    "vnd": MessageLookupByLibrary.simpleMessage("đ"),
    "volTitle": MessageLookupByLibrary.simpleMessage("KL đặt/\nKL khớp"),
    "volume": MessageLookupByLibrary.simpleMessage("Khối lượng"),
    "volumeKL": MessageLookupByLibrary.simpleMessage("KL"),
    "volumeMustEvenLot": MessageLookupByLibrary.simpleMessage(
      "Khối lượng tối thiểu 100 và là bội số 100",
    ),
    "volumeWaiting": MessageLookupByLibrary.simpleMessage(
      "Buying awaiting volume",
    ),
    "waitingCommand": MessageLookupByLibrary.simpleMessage("Lệnh chờ"),
    "warrants": MessageLookupByLibrary.simpleMessage("Warrants"),
    "watchList": MessageLookupByLibrary.simpleMessage("Watch list"),
    "wealContinue": MessageLookupByLibrary.simpleMessage("Continue"),
    "wealth": MessageLookupByLibrary.simpleMessage("Wealth"),
    "wealthPlan": MessageLookupByLibrary.simpleMessage("Plan"),
    "wealthProductDes": MessageLookupByLibrary.simpleMessage(
      "Là sản phẩm VPBankS hỗ trợ và đồng hành cùng quý khách hàng trên chặng đường đầu tư và tích lũy tài sản nhằm đạt được các mục tiêu tài chính mong muốn.Bắt đầu ngay hôm nay cùng VPBankS!",
    ),
    "wealthProductTitle": MessageLookupByLibrary.simpleMessage(
      "Sản phẩm Tích sản",
    ),
    "wealthStart": MessageLookupByLibrary.simpleMessage("Start"),
    "whatAreYourInvestmentGoals": MessageLookupByLibrary.simpleMessage(
      "What are your investment goals?",
    ),
    "whenHaveEventToDiluteStock": MessageLookupByLibrary.simpleMessage(
      "Khi có sự kiện quyền pha loãng giá cổ phiếu",
    ),
    "wrongStepPrice": MessageLookupByLibrary.simpleMessage("Sai bước giá"),
    "wrongTransactionPrice": MessageLookupByLibrary.simpleMessage(
      "Giá giao dịch phải nằm trong khoảng trần - sàn",
    ),
    "yesContinueInvesting": MessageLookupByLibrary.simpleMessage(
      "Yes, continue investing",
    ),
    "yesStopInvesting": MessageLookupByLibrary.simpleMessage(
      "Yes, stop investing",
    ),
    "youNotHoldingThisStock": MessageLookupByLibrary.simpleMessage(
      "Bạn không nắm giữ mã chứng khoán này",
    ),
  };
}
