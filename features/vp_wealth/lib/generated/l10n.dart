// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class WealthStock {
  WealthStock();

  static WealthStock? _current;

  static WealthStock get current {
    assert(
      _current != null,
      'No instance of WealthStock was loaded. Try to initialize the WealthStock delegate before accessing WealthStock.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<WealthStock> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = WealthStock();
      WealthStock._current = instance;

      return instance;
    });
  }

  static WealthStock of(BuildContext context) {
    final instance = WealthStock.maybeOf(context);
    assert(
      instance != null,
      'No instance of WealthStock present in the widget tree. Did you add WealthStock.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static WealthStock? maybeOf(BuildContext context) {
    return Localizations.of<WealthStock>(context, WealthStock);
  }

  /// `Danh mục`
  String get category {
    return Intl.message('Danh mục', name: 'category', desc: '', args: []);
  }

  /// `Thị trường`
  String get market {
    return Intl.message('Thị trường', name: 'market', desc: '', args: []);
  }

  /// `Tiện ích`
  String get utilities {
    return Intl.message('Tiện ích', name: 'utilities', desc: '', args: []);
  }

  /// `Đặt lệnh`
  String get setCommand {
    return Intl.message('Đặt lệnh', name: 'setCommand', desc: '', args: []);
  }

  /// `Hiện tại không có dữ liệu`
  String get noData {
    return Intl.message(
      'Hiện tại không có dữ liệu',
      name: 'noData',
      desc: '',
      args: [],
    );
  }

  /// `Bảng giá`
  String get priceList {
    return Intl.message('Bảng giá', name: 'priceList', desc: '', args: []);
  }

  /// `Chứng quyền`
  String get warrants {
    return Intl.message('Chứng quyền', name: 'warrants', desc: '', args: []);
  }

  /// `ETF`
  String get etf {
    return Intl.message('ETF', name: 'etf', desc: '', args: []);
  }

  /// `Trái phiếu RL`
  String get bondRL {
    return Intl.message('Trái phiếu RL', name: 'bondRL', desc: '', args: []);
  }

  /// `Top cổ phiếu`
  String get topStocks {
    return Intl.message('Top cổ phiếu', name: 'topStocks', desc: '', args: []);
  }

  /// `Cổ phiếu`
  String get stock {
    return Intl.message('Cổ phiếu', name: 'stock', desc: '', args: []);
  }

  /// `Danh sách cổ phiếu`
  String get offerList {
    return Intl.message(
      'Danh sách cổ phiếu',
      name: 'offerList',
      desc: '',
      args: [],
    );
  }

  /// `Danh sách chào bán`
  String get offerForSaleList {
    return Intl.message(
      'Danh sách chào bán',
      name: 'offerForSaleList',
      desc: '',
      args: [],
    );
  }

  /// `Bộ lọc cổ phiếu`
  String get stockFilter {
    return Intl.message(
      'Bộ lọc cổ phiếu',
      name: 'stockFilter',
      desc: '',
      args: [],
    );
  }

  /// `Giá`
  String get price {
    return Intl.message('Giá', name: 'price', desc: '', args: []);
  }

  /// `Tìm kiếm mã cổ phiếu`
  String get searchStocks {
    return Intl.message(
      'Tìm kiếm mã cổ phiếu',
      name: 'searchStocks',
      desc: '',
      args: [],
    );
  }

  /// `Tìm mã chứng khoán`
  String get searchStocks2 {
    return Intl.message(
      'Tìm mã chứng khoán',
      name: 'searchStocks2',
      desc: '',
      args: [],
    );
  }

  /// `Thêm vào danh sách`
  String get addWatchList {
    return Intl.message(
      'Thêm vào danh sách',
      name: 'addWatchList',
      desc: '',
      args: [],
    );
  }

  /// `Danh mục theo dõi`
  String get watchList {
    return Intl.message(
      'Danh mục theo dõi',
      name: 'watchList',
      desc: '',
      args: [],
    );
  }

  /// `Danh mục tự tạo`
  String get selfCreatedDirectory {
    return Intl.message(
      'Danh mục tự tạo',
      name: 'selfCreatedDirectory',
      desc: '',
      args: [],
    );
  }

  /// `Danh sách mới`
  String get newCategory {
    return Intl.message(
      'Danh sách mới',
      name: 'newCategory',
      desc: '',
      args: [],
    );
  }

  /// `Thêm vào danh sách`
  String get addToCategory {
    return Intl.message(
      'Thêm vào danh sách',
      name: 'addToCategory',
      desc: '',
      args: [],
    );
  }

  /// `Thêm danh sách mới`
  String get addNewCategory {
    return Intl.message(
      'Thêm danh sách mới',
      name: 'addNewCategory',
      desc: '',
      args: [],
    );
  }

  /// `Đặt tên cho danh sách mới của bạn`
  String get descAddNewCategory {
    return Intl.message(
      'Đặt tên cho danh sách mới của bạn',
      name: 'descAddNewCategory',
      desc: '',
      args: [],
    );
  }

  /// `Thêm`
  String get add {
    return Intl.message('Thêm', name: 'add', desc: '', args: []);
  }

  /// `Tên danh sách`
  String get nameCategory {
    return Intl.message(
      'Tên danh sách',
      name: 'nameCategory',
      desc: '',
      args: [],
    );
  }

  /// `Đã thêm`
  String get added {
    return Intl.message('Đã thêm', name: 'added', desc: '', args: []);
  }

  /// `vào danh sách`
  String get toTheList {
    return Intl.message('vào danh sách', name: 'toTheList', desc: '', args: []);
  }

  /// `Danh sách chưa có mã nào`
  String get noStock {
    return Intl.message(
      'Danh sách chưa có mã nào',
      name: 'noStock',
      desc: '',
      args: [],
    );
  }

  /// `Vui lòng nhập tên danh mục`
  String get validateNameCategory {
    return Intl.message(
      'Vui lòng nhập tên danh mục',
      name: 'validateNameCategory',
      desc: '',
      args: [],
    );
  }

  /// `Tên đã tồn tại`
  String get nameAlreadyExists {
    return Intl.message(
      'Tên đã tồn tại',
      name: 'nameAlreadyExists',
      desc: '',
      args: [],
    );
  }

  /// `Đã lưu danh sách`
  String get editCategory {
    return Intl.message(
      'Đã lưu danh sách',
      name: 'editCategory',
      desc: '',
      args: [],
    );
  }

  /// `Đã xóa danh sách`
  String get removeCategory {
    return Intl.message(
      'Đã xóa danh sách',
      name: 'removeCategory',
      desc: '',
      args: [],
    );
  }

  /// `Bạn chưa nắm giữ cổ phiếu nào`
  String get noHoldingPortfolio {
    return Intl.message(
      'Bạn chưa nắm giữ cổ phiếu nào',
      name: 'noHoldingPortfolio',
      desc: '',
      args: [],
    );
  }

  /// `Bạn chưa sở hữu mã cổ phiếu nào có thể bán`
  String get noHoldingPortfolioForSell {
    return Intl.message(
      'Bạn chưa sở hữu mã cổ phiếu nào có thể bán',
      name: 'noHoldingPortfolioForSell',
      desc: '',
      args: [],
    );
  }

  /// `Giá trung bình`
  String get averagePrice {
    return Intl.message(
      'Giá trung bình',
      name: 'averagePrice',
      desc: '',
      args: [],
    );
  }

  /// `Giá trị thị trường`
  String get marketPrice {
    return Intl.message(
      'Giá trị thị trường',
      name: 'marketPrice',
      desc: '',
      args: [],
    );
  }

  /// `Lãi/lỗ`
  String get profitLoss {
    return Intl.message('Lãi/lỗ', name: 'profitLoss', desc: '', args: []);
  }

  /// `Lãi/lỗ dự kiến`
  String get profitLossExpected {
    return Intl.message(
      'Lãi/lỗ dự kiến',
      name: 'profitLossExpected',
      desc: '',
      args: [],
    );
  }

  /// `Tổng vốn`
  String get totalCapital {
    return Intl.message('Tổng vốn', name: 'totalCapital', desc: '', args: []);
  }

  /// `KL khả dụng`
  String get availableVolume {
    return Intl.message(
      'KL khả dụng',
      name: 'availableVolume',
      desc: '',
      args: [],
    );
  }

  /// `KL khác`
  String get otherVolume {
    return Intl.message('KL khác', name: 'otherVolume', desc: '', args: []);
  }

  /// `KL mua chờ về`
  String get volumeWaiting {
    return Intl.message(
      'KL mua chờ về',
      name: 'volumeWaiting',
      desc: '',
      args: [],
    );
  }

  /// `Tổng KL`
  String get totalVolume {
    return Intl.message('Tổng KL', name: 'totalVolume', desc: '', args: []);
  }

  /// `Tổng giá trị`
  String get totalValue {
    return Intl.message('Tổng giá trị', name: 'totalValue', desc: '', args: []);
  }

  /// `Thêm mã`
  String get addCode {
    return Intl.message('Thêm mã', name: 'addCode', desc: '', args: []);
  }

  /// `Giá TT`
  String get marketPriceAcr {
    return Intl.message('Giá TT', name: 'marketPriceAcr', desc: '', args: []);
  }

  /// `Đã thêm vào danh sách`
  String get addedToList {
    return Intl.message(
      'Đã thêm vào danh sách',
      name: 'addedToList',
      desc: '',
      args: [],
    );
  }

  /// `Lãi dự kiến`
  String get expectedProfit {
    return Intl.message(
      'Lãi dự kiến',
      name: 'expectedProfit',
      desc: '',
      args: [],
    );
  }

  /// `KL tổng`
  String get totalMass {
    return Intl.message('KL tổng', name: 'totalMass', desc: '', args: []);
  }

  /// `Giá vốn`
  String get capitalCost {
    return Intl.message('Giá vốn', name: 'capitalCost', desc: '', args: []);
  }

  /// `Giá trị vốn`
  String get capitalValue {
    return Intl.message(
      'Giá trị vốn',
      name: 'capitalValue',
      desc: '',
      args: [],
    );
  }

  /// `Lãi/lỗ (dự kiến)`
  String get expectedProfitLoss {
    return Intl.message(
      'Lãi/lỗ (dự kiến)',
      name: 'expectedProfitLoss',
      desc: '',
      args: [],
    );
  }

  /// `Tổng số lượng CP`
  String get totalAmountShares {
    return Intl.message(
      'Tổng số lượng CP',
      name: 'totalAmountShares',
      desc: '',
      args: [],
    );
  }

  /// `Giá vốn trung bình`
  String get averageCostPrice {
    return Intl.message(
      'Giá vốn trung bình',
      name: 'averageCostPrice',
      desc: '',
      args: [],
    );
  }

  /// `Lãi/lỗ hôm nay`
  String get profitLossToday {
    return Intl.message(
      'Lãi/lỗ hôm nay',
      name: 'profitLossToday',
      desc: '',
      args: [],
    );
  }

  /// `dự kiến`
  String get expected {
    return Intl.message('dự kiến', name: 'expected', desc: '', args: []);
  }

  /// `Tổng lãi/lỗ`
  String get totalProfitLoss {
    return Intl.message(
      'Tổng lãi/lỗ',
      name: 'totalProfitLoss',
      desc: '',
      args: [],
    );
  }

  /// `CP giao dịch`
  String get numberSharesTraded {
    return Intl.message(
      'CP giao dịch',
      name: 'numberSharesTraded',
      desc: '',
      args: [],
    );
  }

  /// `CP phong tỏa`
  String get numberBlockedShares {
    return Intl.message(
      'CP phong tỏa',
      name: 'numberBlockedShares',
      desc: '',
      args: [],
    );
  }

  /// `Kế hoạch`
  String get plan {
    return Intl.message('Kế hoạch', name: 'plan', desc: '', args: []);
  }

  /// `Mục tiêu`
  String get target {
    return Intl.message('Mục tiêu', name: 'target', desc: '', args: []);
  }

  /// `Mục tiêu đầu tư`
  String get targetInvestment {
    return Intl.message(
      'Mục tiêu đầu tư',
      name: 'targetInvestment',
      desc: '',
      args: [],
    );
  }

  /// `Tiếp tục`
  String get buttonContinue {
    return Intl.message('Tiếp tục', name: 'buttonContinue', desc: '', args: []);
  }

  /// `Đóng`
  String get buttonClose {
    return Intl.message('Đóng', name: 'buttonClose', desc: '', args: []);
  }

  /// `Xác nhận`
  String get buttonAccept {
    return Intl.message('Xác nhận', name: 'buttonAccept', desc: '', args: []);
  }

  /// `Lưu thay đổi`
  String get buttonSaveChange {
    return Intl.message(
      'Lưu thay đổi',
      name: 'buttonSaveChange',
      desc: '',
      args: [],
    );
  }

  /// `Xem danh mục đầu tư`
  String get buttonSeeDetails {
    return Intl.message(
      'Xem danh mục đầu tư',
      name: 'buttonSeeDetails',
      desc: '',
      args: [],
    );
  }

  /// `Quay lại`
  String get buttonBack {
    return Intl.message('Quay lại', name: 'buttonBack', desc: '', args: []);
  }

  /// `Sắp ra mắt`
  String get comingSoon {
    return Intl.message('Sắp ra mắt', name: 'comingSoon', desc: '', args: []);
  }

  /// `Quy tắc phân bổ 50/30/20`
  String get allocationRules {
    return Intl.message(
      'Quy tắc phân bổ 50/30/20',
      name: 'allocationRules',
      desc: '',
      args: [],
    );
  }

  /// `Quy tắc 50/30/20 là quy tắc phổ biến được khuyến nghị để quản lý tài chính cá nhân hiệu quả.`
  String get allocationRulesContent {
    return Intl.message(
      'Quy tắc 50/30/20 là quy tắc phổ biến được khuyến nghị để quản lý tài chính cá nhân hiệu quả.',
      name: 'allocationRulesContent',
      desc: '',
      args: [],
    );
  }

  /// `Không có kết quả nào phù hợp`
  String get emptyListFilterPlanWhenNoResultMatching {
    return Intl.message(
      'Không có kết quả nào phù hợp',
      name: 'emptyListFilterPlanWhenNoResultMatching',
      desc: '',
      args: [],
    );
  }

  /// `Tìm kiếm mã cổ phiếu`
  String get hintTextSearchStockSymbol {
    return Intl.message(
      'Tìm kiếm mã cổ phiếu',
      name: 'hintTextSearchStockSymbol',
      desc: '',
      args: [],
    );
  }

  /// `Tìm kiếm kế hoạch`
  String get hintTextSearchPlan {
    return Intl.message(
      'Tìm kiếm kế hoạch',
      name: 'hintTextSearchPlan',
      desc: '',
      args: [],
    );
  }

  /// `Nhập số tiền`
  String get hintTextEnterAmount {
    return Intl.message(
      'Nhập số tiền',
      name: 'hintTextEnterAmount',
      desc: '',
      args: [],
    );
  }

  /// `Nhập số năm`
  String get hintTextEnterYearNumber {
    return Intl.message(
      'Nhập số năm',
      name: 'hintTextEnterYearNumber',
      desc: '',
      args: [],
    );
  }

  /// `Nhập tên kế hoạch`
  String get hintTextEnterPlanName {
    return Intl.message(
      'Nhập tên kế hoạch',
      name: 'hintTextEnterPlanName',
      desc: '',
      args: [],
    );
  }

  /// `Mã cổ phiếu đã tồn tại trong danh mục của bạn.\nVui lòng tham khảo thêm các cổ phiếu\nchọn lọc bởi VPBankS.`
  String get emptyListSearchWhenSymbolExist {
    return Intl.message(
      'Mã cổ phiếu đã tồn tại trong danh mục của bạn.\nVui lòng tham khảo thêm các cổ phiếu\nchọn lọc bởi VPBankS.',
      name: 'emptyListSearchWhenSymbolExist',
      desc: '',
      args: [],
    );
  }

  /// `Không có kết quả nào phù hợp.\nVui lòng tham khảo danh sách cổ phiếu\nchọn lọc bởi VPBankS.`
  String get emptyListSearchWhenNoResultMatching {
    return Intl.message(
      'Không có kết quả nào phù hợp.\nVui lòng tham khảo danh sách cổ phiếu\nchọn lọc bởi VPBankS.',
      name: 'emptyListSearchWhenNoResultMatching',
      desc: '',
      args: [],
    );
  }

  /// `Đây là danh sách cổ phiếu chọn lọc bởi VPBankS.`
  String get symbolSuggestionByVPBankS {
    return Intl.message(
      'Đây là danh sách cổ phiếu chọn lọc bởi VPBankS.',
      name: 'symbolSuggestionByVPBankS',
      desc: '',
      args: [],
    );
  }

  /// `Tên kế hoạch`
  String get titlePlanName {
    return Intl.message(
      'Tên kế hoạch',
      name: 'titlePlanName',
      desc: '',
      args: [],
    );
  }

  /// `Bắt đầu đầu tư với`
  String get initialInvestmentAmount {
    return Intl.message(
      'Bắt đầu đầu tư với',
      name: 'initialInvestmentAmount',
      desc: '',
      args: [],
    );
  }

  /// `Số tiền đầu tư ban đầu`
  String get amountInitialStart {
    return Intl.message(
      'Số tiền đầu tư ban đầu',
      name: 'amountInitialStart',
      desc: '',
      args: [],
    );
  }

  /// `và đầu tư định kỳ thêm`
  String get initialInvestmentAmountAnd {
    return Intl.message(
      'và đầu tư định kỳ thêm',
      name: 'initialInvestmentAmountAnd',
      desc: '',
      args: [],
    );
  }

  /// `Số tiền đầu tư định kỳ`
  String get periodicInvestmentAmount {
    return Intl.message(
      'Số tiền đầu tư định kỳ',
      name: 'periodicInvestmentAmount',
      desc: '',
      args: [],
    );
  }

  /// `Nên phân bổ đầu tư tối thiểu bao nhiêu % tổng thu nhập?`
  String get noteAboutPeriodicInvestmentAmount {
    return Intl.message(
      'Nên phân bổ đầu tư tối thiểu bao nhiêu % tổng thu nhập?',
      name: 'noteAboutPeriodicInvestmentAmount',
      desc: '',
      args: [],
    );
  }

  /// `Thời gian đầu tư tối thiểu từ 1 năm, tối đa là 50 năm.`
  String get noteAboutInvestmentTime {
    return Intl.message(
      'Thời gian đầu tư tối thiểu từ 1 năm, tối đa là 50 năm.',
      name: 'noteAboutInvestmentTime',
      desc: '',
      args: [],
    );
  }

  /// `Thời gian đầu tư`
  String get investmentTime {
    return Intl.message(
      'Thời gian đầu tư',
      name: 'investmentTime',
      desc: '',
      args: [],
    );
  }

  /// `Tần suất đầu tư`
  String get investmentFrequency {
    return Intl.message(
      'Tần suất đầu tư',
      name: 'investmentFrequency',
      desc: '',
      args: [],
    );
  }

  /// `Chọn tần suất đầu tư`
  String get chooseInvestmentFrequency {
    return Intl.message(
      'Chọn tần suất đầu tư',
      name: 'chooseInvestmentFrequency',
      desc: '',
      args: [],
    );
  }

  /// `Ngày bắt đầu đầu tư`
  String get periodicInvestmentStartDate {
    return Intl.message(
      'Ngày bắt đầu đầu tư',
      name: 'periodicInvestmentStartDate',
      desc: '',
      args: [],
    );
  }

  /// `Ngày đầu tư định kỳ hàng tháng là ngày`
  String get monthlyInvestmentDay {
    return Intl.message(
      'Ngày đầu tư định kỳ hàng tháng là ngày',
      name: 'monthlyInvestmentDay',
      desc: '',
      args: [],
    );
  }

  /// `Danh mục đầu tư`
  String get investmentCategory {
    return Intl.message(
      'Danh mục đầu tư',
      name: 'investmentCategory',
      desc: '',
      args: [],
    );
  }

  /// `Tạo ra khẩu vị đầu tư của riêng bạn`
  String get createInvestmentTaste {
    return Intl.message(
      'Tạo ra khẩu vị đầu tư của riêng bạn',
      name: 'createInvestmentTaste',
      desc: '',
      args: [],
    );
  }

  /// `Tăng trưởng kỳ vọng`
  String get expectedGrowth {
    return Intl.message(
      'Tăng trưởng kỳ vọng',
      name: 'expectedGrowth',
      desc: '',
      args: [],
    );
  }

  /// `Sửa tên kế hoạch`
  String get editPlanName {
    return Intl.message(
      'Sửa tên kế hoạch',
      name: 'editPlanName',
      desc: '',
      args: [],
    );
  }

  /// `Vui lòng nhập tên cho kế hoạch`
  String get messageErrorWhenEnterPlanName {
    return Intl.message(
      'Vui lòng nhập tên cho kế hoạch',
      name: 'messageErrorWhenEnterPlanName',
      desc: '',
      args: [],
    );
  }

  /// `Chọn mục tiêu`
  String get chooseTarget {
    return Intl.message(
      'Chọn mục tiêu',
      name: 'chooseTarget',
      desc: '',
      args: [],
    );
  }

  /// `Sản phẩm Tích sản`
  String get wealthProductTitle {
    return Intl.message(
      'Sản phẩm Tích sản',
      name: 'wealthProductTitle',
      desc: '',
      args: [],
    );
  }

  /// `Là sản phẩm VPBankS hỗ trợ và đồng hành cùng quý khách hàng trên chặng đường đầu tư và tích lũy tài sản nhằm đạt được các mục tiêu tài chính mong muốn.Bắt đầu ngay hôm nay cùng VPBankS!`
  String get wealthProductDes {
    return Intl.message(
      'Là sản phẩm VPBankS hỗ trợ và đồng hành cùng quý khách hàng trên chặng đường đầu tư và tích lũy tài sản nhằm đạt được các mục tiêu tài chính mong muốn.Bắt đầu ngay hôm nay cùng VPBankS!',
      name: 'wealthProductDes',
      desc: '',
      args: [],
    );
  }

  /// `Bắt đầu`
  String get wealthStart {
    return Intl.message('Bắt đầu', name: 'wealthStart', desc: '', args: []);
  }

  /// `Tích sản`
  String get wealth {
    return Intl.message('Tích sản', name: 'wealth', desc: '', args: []);
  }

  /// `Lựa chọn mục tiêu đầu tư`
  String get investmentGoals {
    return Intl.message(
      'Lựa chọn mục tiêu đầu tư',
      name: 'investmentGoals',
      desc: '',
      args: [],
    );
  }

  /// `Trường phái đầu tư`
  String get investmentPhilosophy {
    return Intl.message(
      'Trường phái đầu tư',
      name: 'investmentPhilosophy',
      desc: '',
      args: [],
    );
  }

  /// `Bạn muốn đầu tư lĩnh vực nào?`
  String get whatAreYourInvestmentGoals {
    return Intl.message(
      'Bạn muốn đầu tư lĩnh vực nào?',
      name: 'whatAreYourInvestmentGoals',
      desc: '',
      args: [],
    );
  }

  /// `Chúng tôi đã hiểu khẩu vị rủi ro của bạn. Giờ hãy cho chúng tôi biết nhu cầu đầu tư của bạn.`
  String get whatAreYourInvestmentGoalsContent {
    return Intl.message(
      'Chúng tôi đã hiểu khẩu vị rủi ro của bạn. Giờ hãy cho chúng tôi biết nhu cầu đầu tư của bạn.',
      name: 'whatAreYourInvestmentGoalsContent',
      desc: '',
      args: [],
    );
  }

  /// `Bạn thuộc trường phái đầu tư nào?`
  String get howDoYouAlignWithAnyInvestmentPhilosophy {
    return Intl.message(
      'Bạn thuộc trường phái đầu tư nào?',
      name: 'howDoYouAlignWithAnyInvestmentPhilosophy',
      desc: '',
      args: [],
    );
  }

  /// `Để xây dựng chiến lược đầu tư tích sản phù hợp, cùng VPBankS thực hiện bộ khảo sát để tìm ra phong cách đầu tư và danh mục tích sản bền vững.`
  String get descriptionInvestmentPhilosophy {
    return Intl.message(
      'Để xây dựng chiến lược đầu tư tích sản phù hợp, cùng VPBankS thực hiện bộ khảo sát để tìm ra phong cách đầu tư và danh mục tích sản bền vững.',
      name: 'descriptionInvestmentPhilosophy',
      desc: '',
      args: [],
    );
  }

  /// `Bỏ qua`
  String get skip {
    return Intl.message('Bỏ qua', name: 'skip', desc: '', args: []);
  }

  /// `Bắt đầu khảo sát`
  String get startSurvey {
    return Intl.message(
      'Bắt đầu khảo sát',
      name: 'startSurvey',
      desc: '',
      args: [],
    );
  }

  /// `Câu hỏi tiếp theo`
  String get nextQuestion {
    return Intl.message(
      'Câu hỏi tiếp theo',
      name: 'nextQuestion',
      desc: '',
      args: [],
    );
  }

  /// `Làm lại`
  String get redo {
    return Intl.message('Làm lại', name: 'redo', desc: '', args: []);
  }

  /// `Tiếp tục`
  String get wealContinue {
    return Intl.message('Tiếp tục', name: 'wealContinue', desc: '', args: []);
  }

  /// `Bản kế hoạch`
  String get wealthPlan {
    return Intl.message('Bản kế hoạch', name: 'wealthPlan', desc: '', args: []);
  }

  /// `Danh mục của tôi`
  String get myCategory {
    return Intl.message(
      'Danh mục của tôi',
      name: 'myCategory',
      desc: '',
      args: [],
    );
  }

  /// `Hủy tạo kế hoạch tích sản`
  String get cancelPlan {
    return Intl.message(
      'Hủy tạo kế hoạch tích sản',
      name: 'cancelPlan',
      desc: '',
      args: [],
    );
  }

  /// `Bạn có muốn hủy tạo kế hoạch`
  String get cancelPlanPage {
    return Intl.message(
      'Bạn có muốn hủy tạo kế hoạch',
      name: 'cancelPlanPage',
      desc: '',
      args: [],
    );
  }

  /// `Có, hủy kế hoạch`
  String get cancelCreatePlan {
    return Intl.message(
      'Có, hủy kế hoạch',
      name: 'cancelCreatePlan',
      desc: '',
      args: [],
    );
  }

  /// `Bạn có chắc chắn muốn hủy tạo kế hoạch\ntích sản?`
  String get areYouCancelCreatePlan {
    return Intl.message(
      'Bạn có chắc chắn muốn hủy tạo kế hoạch\ntích sản?',
      name: 'areYouCancelCreatePlan',
      desc: '',
      args: [],
    );
  }

  /// `Chiến lược đầu tư với mục tiêu bảo toàn vốn và hạn chế khả năng mất vốn, là lựa chọn phù hợp cho Nhà đầu tư có khẩu vị rủi ro thấp và sẵn sàng chấp nhận mức lợi nhuận thấp để đổi lấy sự an tâm nhất định`
  String get descriptionLevelLow {
    return Intl.message(
      'Chiến lược đầu tư với mục tiêu bảo toàn vốn và hạn chế khả năng mất vốn, là lựa chọn phù hợp cho Nhà đầu tư có khẩu vị rủi ro thấp và sẵn sàng chấp nhận mức lợi nhuận thấp để đổi lấy sự an tâm nhất định',
      name: 'descriptionLevelLow',
      desc: '',
      args: [],
    );
  }

  /// `Chiến lược đầu tư với mục tiêu gia tăng tài sản một cách vừa phải, là lựa chọn phù hợp cho Nhà đầu tư có khẩu vị rủi ro trung bình và sẵn sàng chấp nhận một mức độ rủi ro nhất định`
  String get descriptionLevelMedium {
    return Intl.message(
      'Chiến lược đầu tư với mục tiêu gia tăng tài sản một cách vừa phải, là lựa chọn phù hợp cho Nhà đầu tư có khẩu vị rủi ro trung bình và sẵn sàng chấp nhận một mức độ rủi ro nhất định',
      name: 'descriptionLevelMedium',
      desc: '',
      args: [],
    );
  }

  /// `Chiến lược đầu tư tập trung vào sự tăng trưởng vốn trong trung và dài hạn, là lựa chọn phù hợp cho Nhà đầu tư có khả năng chấp nhận rủi ro cao và sẵn sàng chấp nhận sự biến động đáng kể với giá trị khoản đầu tư của mình`
  String get descriptionLevelHight {
    return Intl.message(
      'Chiến lược đầu tư tập trung vào sự tăng trưởng vốn trong trung và dài hạn, là lựa chọn phù hợp cho Nhà đầu tư có khả năng chấp nhận rủi ro cao và sẵn sàng chấp nhận sự biến động đáng kể với giá trị khoản đầu tư của mình',
      name: 'descriptionLevelHight',
      desc: '',
      args: [],
    );
  }

  /// `Phù hợp với nhà đầu tư có kinh nghiệm, mong muốn tự thực hiện đầu tư và sẵn sàng chấp nhận rủi ro cao để tìm kiến mức lợi nhuận lớn.`
  String get wealthGoldsContent {
    return Intl.message(
      'Phù hợp với nhà đầu tư có kinh nghiệm, mong muốn tự thực hiện đầu tư và sẵn sàng chấp nhận rủi ro cao để tìm kiến mức lợi nhuận lớn.',
      name: 'wealthGoldsContent',
      desc: '',
      args: [],
    );
  }

  /// `Phù hợp với nhà đầu tư mới tham gia thị trường, không có thời gian theo dõi thị trường thường xuyên, mong muốn đầu tư vào danh mục được đa dạng hóa và quản lý bởi đội ngũ chuyên gia giàu kinh nghiệm.`
  String get fundGoalsContent {
    return Intl.message(
      'Phù hợp với nhà đầu tư mới tham gia thị trường, không có thời gian theo dõi thị trường thường xuyên, mong muốn đầu tư vào danh mục được đa dạng hóa và quản lý bởi đội ngũ chuyên gia giàu kinh nghiệm.',
      name: 'fundGoalsContent',
      desc: '',
      args: [],
    );
  }

  /// `Trái phiếu/Quỹ trái phiếu:`
  String get bondsfunds {
    return Intl.message(
      'Trái phiếu/Quỹ trái phiếu:',
      name: 'bondsfunds',
      desc: '',
      args: [],
    );
  }

  /// `Cổ phiếu/Quỹ cổ phiếu:`
  String get stocksStockFunds {
    return Intl.message(
      'Cổ phiếu/Quỹ cổ phiếu:',
      name: 'stocksStockFunds',
      desc: '',
      args: [],
    );
  }

  /// `Tiền mặt/Quỹ thị trường tiền tệ:`
  String get cashMoney {
    return Intl.message(
      'Tiền mặt/Quỹ thị trường tiền tệ:',
      name: 'cashMoney',
      desc: '',
      args: [],
    );
  }

  /// `Có, huỷ khảo sát`
  String get cancelSurvey {
    return Intl.message(
      'Có, huỷ khảo sát',
      name: 'cancelSurvey',
      desc: '',
      args: [],
    );
  }

  /// `Thông tin kế hoạch`
  String get inforPlan {
    return Intl.message(
      'Thông tin kế hoạch',
      name: 'inforPlan',
      desc: '',
      args: [],
    );
  }

  /// `Số vốn đầu tư`
  String get investmentMoney {
    return Intl.message(
      'Số vốn đầu tư',
      name: 'investmentMoney',
      desc: '',
      args: [],
    );
  }

  /// `Xem ngay`
  String get seeNow {
    return Intl.message('Xem ngay', name: 'seeNow', desc: '', args: []);
  }

  /// `Đầu tư trong`
  String get investmentTimeIn {
    return Intl.message(
      'Đầu tư trong',
      name: 'investmentTimeIn',
      desc: '',
      args: [],
    );
  }

  /// `năm`
  String get year {
    return Intl.message('năm', name: 'year', desc: '', args: []);
  }

  /// `với tần suất`
  String get withInvestmentFrequency {
    return Intl.message(
      'với tần suất',
      name: 'withInvestmentFrequency',
      desc: '',
      args: [],
    );
  }

  /// `Phân bổ đầu tư`
  String get investmentAllocation {
    return Intl.message(
      'Phân bổ đầu tư',
      name: 'investmentAllocation',
      desc: '',
      args: [],
    );
  }

  /// `Xem minh hoạ`
  String get seeSample {
    return Intl.message('Xem minh hoạ', name: 'seeSample', desc: '', args: []);
  }

  /// `Tiền tích luỹ kỳ vọng`
  String get expectedSavings {
    return Intl.message(
      'Tiền tích luỹ kỳ vọng',
      name: 'expectedSavings',
      desc: '',
      args: [],
    );
  }

  /// `Số tiền kỳ vọng`
  String get expectedMoney {
    return Intl.message(
      'Số tiền kỳ vọng',
      name: 'expectedMoney',
      desc: '',
      args: [],
    );
  }

  /// `Số tiền kỳ vọng đạt được`
  String get expectedMoneyTarget {
    return Intl.message(
      'Số tiền kỳ vọng đạt được',
      name: 'expectedMoneyTarget',
      desc: '',
      args: [],
    );
  }

  /// `Chi tiết danh mục`
  String get detailCategory {
    return Intl.message(
      'Chi tiết danh mục',
      name: 'detailCategory',
      desc: '',
      args: [],
    );
  }

  /// `Đã hiểu`
  String get strOk {
    return Intl.message('Đã hiểu', name: 'strOk', desc: '', args: []);
  }

  /// `Tỷ lệ phân bổ`
  String get rateInvestmentAmount {
    return Intl.message(
      'Tỷ lệ phân bổ',
      name: 'rateInvestmentAmount',
      desc: '',
      args: [],
    );
  }

  /// `Bạn chưa có cổ phiếu nào trong danh mục`
  String get emptyStockCategory {
    return Intl.message(
      'Bạn chưa có cổ phiếu nào trong danh mục',
      name: 'emptyStockCategory',
      desc: '',
      args: [],
    );
  }

  /// `Danh mục VPBankS khuyến nghị`
  String get suggestedByVPBankS {
    return Intl.message(
      'Danh mục VPBankS khuyến nghị',
      name: 'suggestedByVPBankS',
      desc: '',
      args: [],
    );
  }

  /// `Danh mục cổ phiếu VPBankS đánh giá và chọn lọc theo các ngành trên thị trường`
  String get suggestedByVPBankSContent {
    return Intl.message(
      'Danh mục cổ phiếu VPBankS đánh giá và chọn lọc theo các ngành trên thị trường',
      name: 'suggestedByVPBankSContent',
      desc: '',
      args: [],
    );
  }

  /// `Thêm cổ phiếu`
  String get addStock {
    return Intl.message('Thêm cổ phiếu', name: 'addStock', desc: '', args: []);
  }

  /// `Xác nhận thông tin kế hoạch`
  String get confirmPlan {
    return Intl.message(
      'Xác nhận thông tin kế hoạch',
      name: 'confirmPlan',
      desc: '',
      args: [],
    );
  }

  /// `Lưu ý: số tiền đầu tư nhỏ hơn thị giá của mã cổ phiếu sẽ ảnh hưởng tới việc đặt lệnh của kế hoạch`
  String get coutionInfoPlan {
    return Intl.message(
      'Lưu ý: số tiền đầu tư nhỏ hơn thị giá của mã cổ phiếu sẽ ảnh hưởng tới việc đặt lệnh của kế hoạch',
      name: 'coutionInfoPlan',
      desc: '',
      args: [],
    );
  }

  /// `Tài sản nắm giữ`
  String get heldAsset {
    return Intl.message(
      'Tài sản nắm giữ',
      name: 'heldAsset',
      desc: '',
      args: [],
    );
  }

  /// `Tài sản`
  String get asset {
    return Intl.message('Tài sản', name: 'asset', desc: '', args: []);
  }

  /// `Nợ`
  String get inDebit {
    return Intl.message('Nợ', name: 'inDebit', desc: '', args: []);
  }

  /// `Tỷ lệ hoàn thành mục tiêu`
  String get percenCompleted {
    return Intl.message(
      'Tỷ lệ hoàn thành mục tiêu',
      name: 'percenCompleted',
      desc: '',
      args: [],
    );
  }

  /// `Hiệu quả tích sản`
  String get operational {
    return Intl.message(
      'Hiệu quả tích sản',
      name: 'operational',
      desc: '',
      args: [],
    );
  }

  /// `Số tiền tích luỹ`
  String get moneyCategory {
    return Intl.message(
      'Số tiền tích luỹ',
      name: 'moneyCategory',
      desc: '',
      args: [],
    );
  }

  /// `Hoàn thành`
  String get completed {
    return Intl.message('Hoàn thành', name: 'completed', desc: '', args: []);
  }

  /// `Trạng thái`
  String get status {
    return Intl.message('Trạng thái', name: 'status', desc: '', args: []);
  }

  /// `Đang hoạt động`
  String get active {
    return Intl.message('Đang hoạt động', name: 'active', desc: '', args: []);
  }

  /// `Dừng đầu tư`
  String get stopWealth {
    return Intl.message('Dừng đầu tư', name: 'stopWealth', desc: '', args: []);
  }

  /// `Kế hoạch phân bổ`
  String get allocationPlan {
    return Intl.message(
      'Kế hoạch phân bổ',
      name: 'allocationPlan',
      desc: '',
      args: [],
    );
  }

  /// `Ngày đầu tư định kỳ`
  String get periodicInvestmentDate {
    return Intl.message(
      'Ngày đầu tư định kỳ',
      name: 'periodicInvestmentDate',
      desc: '',
      args: [],
    );
  }

  /// `Kỳ đầu tư tiếp theo`
  String get nextInvestmentPeriod {
    return Intl.message(
      'Kỳ đầu tư tiếp theo',
      name: 'nextInvestmentPeriod',
      desc: '',
      args: [],
    );
  }

  /// `Bạn có muốn dừng đầu tư?`
  String get doYouWantToStopInvesting {
    return Intl.message(
      'Bạn có muốn dừng đầu tư?',
      name: 'doYouWantToStopInvesting',
      desc: '',
      args: [],
    );
  }

  /// `Không`
  String get no {
    return Intl.message('Không', name: 'no', desc: '', args: []);
  }

  /// `Có, dừng đầu tư`
  String get yesStopInvesting {
    return Intl.message(
      'Có, dừng đầu tư',
      name: 'yesStopInvesting',
      desc: '',
      args: [],
    );
  }

  /// `Bạn có muốn tiếp tục đầu tư?`
  String get continueInvesting {
    return Intl.message(
      'Bạn có muốn tiếp tục đầu tư?',
      name: 'continueInvesting',
      desc: '',
      args: [],
    );
  }

  /// `Có, tiếp tục đầu tư`
  String get yesContinueInvesting {
    return Intl.message(
      'Có, tiếp tục đầu tư',
      name: 'yesContinueInvesting',
      desc: '',
      args: [],
    );
  }

  /// `Điều khoản và điều kiện`
  String get termsAndConditions {
    return Intl.message(
      'Điều khoản và điều kiện',
      name: 'termsAndConditions',
      desc: '',
      args: [],
    );
  }

  /// `Ngày đầu tư định kỳ`
  String get scheduleInvestment {
    return Intl.message(
      'Ngày đầu tư định kỳ',
      name: 'scheduleInvestment',
      desc: '',
      args: [],
    );
  }

  /// `Bạn muốn huỷ khảo sát?`
  String get cancelSurveyNew {
    return Intl.message(
      'Bạn muốn huỷ khảo sát?',
      name: 'cancelSurveyNew',
      desc: '',
      args: [],
    );
  }

  /// `Tải xuống thành công`
  String get downloadSuccess {
    return Intl.message(
      'Tải xuống thành công',
      name: 'downloadSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Tải xuống thất bại`
  String get downloadFail {
    return Intl.message(
      'Tải xuống thất bại',
      name: 'downloadFail',
      desc: '',
      args: [],
    );
  }

  /// `Đang tải xuống`
  String get downloading {
    return Intl.message(
      'Đang tải xuống',
      name: 'downloading',
      desc: '',
      args: [],
    );
  }

  /// `Không có kết quả nào phù hợp với bộ lọc. Vui lòng thay đổi điều kiện lọc và thử lại`
  String get filterDataEmpty {
    return Intl.message(
      'Không có kết quả nào phù hợp với bộ lọc. Vui lòng thay đổi điều kiện lọc và thử lại',
      name: 'filterDataEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Đặt mua chứng chỉ quỹ`
  String get orderFundCertificates {
    return Intl.message(
      'Đặt mua chứng chỉ quỹ',
      name: 'orderFundCertificates',
      desc: '',
      args: [],
    );
  }

  /// `Thử lại`
  String get retry {
    return Intl.message('Thử lại', name: 'retry', desc: '', args: []);
  }

  /// `Tìm kiếm`
  String get search {
    return Intl.message('Tìm kiếm', name: 'search', desc: '', args: []);
  }

  /// `Áp dụng`
  String get apply {
    return Intl.message('Áp dụng', name: 'apply', desc: '', args: []);
  }

  /// `Đặt lại`
  String get reset {
    return Intl.message('Đặt lại', name: 'reset', desc: '', args: []);
  }

  /// `Nhập câu trả lời của bạn`
  String get inputAnswer {
    return Intl.message(
      'Nhập câu trả lời của bạn',
      name: 'inputAnswer',
      desc: '',
      args: [],
    );
  }

  /// `tỷ`
  String get bil {
    return Intl.message('tỷ', name: 'bil', desc: '', args: []);
  }

  /// `triệu`
  String get mil {
    return Intl.message('triệu', name: 'mil', desc: '', args: []);
  }

  /// `nghìn`
  String get k {
    return Intl.message('nghìn', name: 'k', desc: '', args: []);
  }

  /// `Nguồn tiền`
  String get moneySource {
    return Intl.message('Nguồn tiền', name: 'moneySource', desc: '', args: []);
  }

  /// `Chọn tài khoản đã liên kết`
  String get selectLinkingAccount {
    return Intl.message(
      'Chọn tài khoản đã liên kết',
      name: 'selectLinkingAccount',
      desc: '',
      args: [],
    );
  }

  /// `Số dư`
  String get availableBalances {
    return Intl.message('Số dư', name: 'availableBalances', desc: '', args: []);
  }

  /// `Số tiền nạp`
  String get cashInTitle {
    return Intl.message('Số tiền nạp', name: 'cashInTitle', desc: '', args: []);
  }

  /// `Số tiền chuyển tối thiểu là 1 đ. Vui lòng thử lại.`
  String get minValueTransfer {
    return Intl.message(
      'Số tiền chuyển tối thiểu là 1 đ. Vui lòng thử lại.',
      name: 'minValueTransfer',
      desc: '',
      args: [],
    );
  }

  /// `Số tiền chuyển tối đa`
  String get maxMoneyTransfer {
    return Intl.message(
      'Số tiền chuyển tối đa',
      name: 'maxMoneyTransfer',
      desc: '',
      args: [],
    );
  }

  /// `Vượt quá số dư tài khoản`
  String get overTransferMoney {
    return Intl.message(
      'Vượt quá số dư tài khoản',
      name: 'overTransferMoney',
      desc: '',
      args: [],
    );
  }

  /// `Phí chuyển tiền`
  String get feeTransfer {
    return Intl.message(
      'Phí chuyển tiền',
      name: 'feeTransfer',
      desc: '',
      args: [],
    );
  }

  /// `Miễn phí`
  String get free {
    return Intl.message('Miễn phí', name: 'free', desc: '', args: []);
  }

  /// `Tài khoản đã liên kết`
  String get linkedAccount {
    return Intl.message(
      'Tài khoản đã liên kết',
      name: 'linkedAccount',
      desc: '',
      args: [],
    );
  }

  /// `Huỷ liên kết`
  String get unLink {
    return Intl.message('Huỷ liên kết', name: 'unLink', desc: '', args: []);
  }

  /// `Bạn có xác nhận muốn hủy đăng ký liên kết`
  String get deceptionUnlink {
    return Intl.message(
      'Bạn có xác nhận muốn hủy đăng ký liên kết',
      name: 'deceptionUnlink',
      desc: '',
      args: [],
    );
  }

  /// `Xác nhận`
  String get confirm {
    return Intl.message('Xác nhận', name: 'confirm', desc: '', args: []);
  }

  /// `Ngân hàng hỗ trợ liên kết`
  String get bankLinking {
    return Intl.message(
      'Ngân hàng hỗ trợ liên kết',
      name: 'bankLinking',
      desc: '',
      args: [],
    );
  }

  /// `Thông báo`
  String get notificationCashIn {
    return Intl.message(
      'Thông báo',
      name: 'notificationCashIn',
      desc: '',
      args: [],
    );
  }

  /// `Tài khoản không được liên kết ủy quyền, liên hệ ********** để được hỗ trợ.`
  String get accountIsNotLinkedToTheDelegation {
    return Intl.message(
      'Tài khoản không được liên kết ủy quyền, liên hệ ********** để được hỗ trợ.',
      name: 'accountIsNotLinkedToTheDelegation',
      desc: '',
      args: [],
    );
  }

  /// `Có lỗi xảy ra`
  String get anErrorOccurred {
    return Intl.message(
      'Có lỗi xảy ra',
      name: 'anErrorOccurred',
      desc: '',
      args: [],
    );
  }

  /// `Ngân hàng đã được liên kết`
  String get titleErrorBank {
    return Intl.message(
      'Ngân hàng đã được liên kết',
      name: 'titleErrorBank',
      desc: '',
      args: [],
    );
  }

  /// `Liên kết tài khoản`
  String get linkAccount {
    return Intl.message(
      'Liên kết tài khoản',
      name: 'linkAccount',
      desc: '',
      args: [],
    );
  }

  /// `Hủy liên kết tài khoản thành công`
  String get cancelLinkingAccountSuccess {
    return Intl.message(
      'Hủy liên kết tài khoản thành công',
      name: 'cancelLinkingAccountSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Hủy liên kết tài khoản không thành công`
  String get cancelLinkingAccountFail {
    return Intl.message(
      'Hủy liên kết tài khoản không thành công',
      name: 'cancelLinkingAccountFail',
      desc: '',
      args: [],
    );
  }

  /// `Bạn sẽ được chuyển đến trang web của ngân hàng đối tác. Bạn có muốn tiếp tục không?`
  String get deceptionLinkAccount {
    return Intl.message(
      'Bạn sẽ được chuyển đến trang web của ngân hàng đối tác. Bạn có muốn tiếp tục không?',
      name: 'deceptionLinkAccount',
      desc: '',
      args: [],
    );
  }

  /// `Hệ thống đã gửi OTP vào số điện thoại của bạn. Vui lòng nhập mã OTP đã nhận`
  String get systemSendOTP {
    return Intl.message(
      'Hệ thống đã gửi OTP vào số điện thoại của bạn. Vui lòng nhập mã OTP đã nhận',
      name: 'systemSendOTP',
      desc: '',
      args: [],
    );
  }

  /// `Mã OTP được gửi từ ngân hàng MBBank đến số điện thoại`
  String get mpOtp {
    return Intl.message(
      'Mã OTP được gửi từ ngân hàng MBBank đến số điện thoại',
      name: 'mpOtp',
      desc: '',
      args: [],
    );
  }

  /// `Tỷ lệ`
  String get rate {
    return Intl.message('Tỷ lệ', name: 'rate', desc: '', args: []);
  }

  /// `Trạng thái GD`
  String get ts {
    return Intl.message('Trạng thái GD', name: 'ts', desc: '', args: []);
  }

  /// `GTGD`
  String get tv {
    return Intl.message('GTGD', name: 'tv', desc: '', args: []);
  }

  /// `KLGD`
  String get tt {
    return Intl.message('KLGD', name: 'tt', desc: '', args: []);
  }

  /// `Mã cổ phiếu`
  String get historyStockCode {
    return Intl.message(
      'Mã cổ phiếu',
      name: 'historyStockCode',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh`
  String get stockType {
    return Intl.message('Lệnh', name: 'stockType', desc: '', args: []);
  }

  /// `Loại lệnh`
  String get commandType {
    return Intl.message('Loại lệnh', name: 'commandType', desc: '', args: []);
  }

  /// `Giá đặt lệnh`
  String get orderPrice {
    return Intl.message('Giá đặt lệnh', name: 'orderPrice', desc: '', args: []);
  }

  /// `Thời gian đặt lệnh`
  String get orderTime {
    return Intl.message(
      'Thời gian đặt lệnh',
      name: 'orderTime',
      desc: '',
      args: [],
    );
  }

  /// `Khối lượng khớp`
  String get jointVolume {
    return Intl.message(
      'Khối lượng khớp',
      name: 'jointVolume',
      desc: '',
      args: [],
    );
  }

  /// `Giá khớp trung bình`
  String get averageMatchingPrice {
    return Intl.message(
      'Giá khớp trung bình',
      name: 'averageMatchingPrice',
      desc: '',
      args: [],
    );
  }

  /// `Giá trị lệnh đặt`
  String get orderValue2 {
    return Intl.message(
      'Giá trị lệnh đặt',
      name: 'orderValue2',
      desc: '',
      args: [],
    );
  }

  /// `Hủy lệnh`
  String get cancelOrder {
    return Intl.message('Hủy lệnh', name: 'cancelOrder', desc: '', args: []);
  }

  /// `TK thường`
  String get ordinary {
    return Intl.message('TK thường', name: 'ordinary', desc: '', args: []);
  }

  /// `Tiểu khoản thường`
  String get ordinaryFull {
    return Intl.message(
      'Tiểu khoản thường',
      name: 'ordinaryFull',
      desc: '',
      args: [],
    );
  }

  /// `TK ký quỹ`
  String get deposit {
    return Intl.message('TK ký quỹ', name: 'deposit', desc: '', args: []);
  }

  /// `Tiểu khoản ký quỹ`
  String get depositFull {
    return Intl.message(
      'Tiểu khoản ký quỹ',
      name: 'depositFull',
      desc: '',
      args: [],
    );
  }

  /// `KL tối đa`
  String get maxBuyVolume {
    return Intl.message('KL tối đa', name: 'maxBuyVolume', desc: '', args: []);
  }

  /// `Đã thêm vào danh sách Mã CK đang chọn`
  String get addedSymbolFollowList {
    return Intl.message(
      'Đã thêm vào danh sách Mã CK đang chọn',
      name: 'addedSymbolFollowList',
      desc: '',
      args: [],
    );
  }

  /// `Thêm vào danh sách Mã CK đang chọn thất bại`
  String get notAddSymbolFollowList {
    return Intl.message(
      'Thêm vào danh sách Mã CK đang chọn thất bại',
      name: 'notAddSymbolFollowList',
      desc: '',
      args: [],
    );
  }

  /// `Tất cả mã`
  String get allSymbol {
    return Intl.message('Tất cả mã', name: 'allSymbol', desc: '', args: []);
  }

  /// `Danh sách theo dõi`
  String get followList {
    return Intl.message(
      'Danh sách theo dõi',
      name: 'followList',
      desc: '',
      args: [],
    );
  }

  /// `Lô lẻ`
  String get oddLot {
    return Intl.message('Lô lẻ', name: 'oddLot', desc: '', args: []);
  }

  /// `Lô Chẵn`
  String get evenLot {
    return Intl.message('Lô Chẵn', name: 'evenLot', desc: '', args: []);
  }

  /// `lệnh đang xử lý`
  String get processingOrder {
    return Intl.message(
      'lệnh đang xử lý',
      name: 'processingOrder',
      desc: '',
      args: [],
    );
  }

  /// `KL đặt`
  String get placeVolume {
    return Intl.message('KL đặt', name: 'placeVolume', desc: '', args: []);
  }

  /// `Mã`
  String get symbol {
    return Intl.message('Mã', name: 'symbol', desc: '', args: []);
  }

  /// `Giá đặt`
  String get priceOrder {
    return Intl.message('Giá đặt', name: 'priceOrder', desc: '', args: []);
  }

  /// `Trải nghiệm ngay`
  String get tryNow {
    return Intl.message('Trải nghiệm ngay', name: 'tryNow', desc: '', args: []);
  }

  /// `Giao diện đặt lệnh nâng cao mới`
  String get newOrderInterfaceIntro1 {
    return Intl.message(
      'Giao diện đặt lệnh nâng cao mới',
      name: 'newOrderInterfaceIntro1',
      desc: '',
      args: [],
    );
  }

  /// `Để thay đổi giao diện đặt lệnh truy cập`
  String get newOrderInterfaceIntro2 {
    return Intl.message(
      'Để thay đổi giao diện đặt lệnh truy cập',
      name: 'newOrderInterfaceIntro2',
      desc: '',
      args: [],
    );
  }

  /// `Cài đặt -> Cấu hình giao dịch`
  String get newOrderInterfaceIntro3 {
    return Intl.message(
      'Cài đặt -> Cấu hình giao dịch',
      name: 'newOrderInterfaceIntro3',
      desc: '',
      args: [],
    );
  }

  /// `Cho phép chọn nhiều mã`
  String get allowMultiSelect {
    return Intl.message(
      'Cho phép chọn nhiều mã',
      name: 'allowMultiSelect',
      desc: '',
      args: [],
    );
  }

  /// `Mã chứng khoán đang chọn`
  String get selectedSymbols {
    return Intl.message(
      'Mã chứng khoán đang chọn',
      name: 'selectedSymbols',
      desc: '',
      args: [],
    );
  }

  /// `Xoá tất cả`
  String get deleteAll {
    return Intl.message('Xoá tất cả', name: 'deleteAll', desc: '', args: []);
  }

  /// `Xoá tất cả cổ phiếu đã chọn`
  String get deleteAllSelected {
    return Intl.message(
      'Xoá tất cả cổ phiếu đã chọn',
      name: 'deleteAllSelected',
      desc: '',
      args: [],
    );
  }

  /// `Bạn có muốn xóa tất cả cổ phiếu trong danh sách đã chọn không?`
  String get confirmDeleteAll {
    return Intl.message(
      'Bạn có muốn xóa tất cả cổ phiếu trong danh sách đã chọn không?',
      name: 'confirmDeleteAll',
      desc: '',
      args: [],
    );
  }

  /// `Đã xoá tất cả mã CK đang chọn`
  String get selectedSymbolDeleted {
    return Intl.message(
      'Đã xoá tất cả mã CK đang chọn',
      name: 'selectedSymbolDeleted',
      desc: '',
      args: [],
    );
  }

  /// `Tìm mã`
  String get searchSymbol {
    return Intl.message('Tìm mã', name: 'searchSymbol', desc: '', args: []);
  }

  /// `Không có lệnh`
  String get noOrder {
    return Intl.message('Không có lệnh', name: 'noOrder', desc: '', args: []);
  }

  /// `Vui lòng chọn tối đa {} mã`
  String get pleaseSelectMax {
    return Intl.message(
      'Vui lòng chọn tối đa {} mã',
      name: 'pleaseSelectMax',
      desc: '',
      args: [],
    );
  }

  /// `Giá trị lệnh`
  String get orderValue {
    return Intl.message('Giá trị lệnh', name: 'orderValue', desc: '', args: []);
  }

  /// `Dư mua`
  String get overbought {
    return Intl.message('Dư mua', name: 'overbought', desc: '', args: []);
  }

  /// `Dư bán`
  String get oversold {
    return Intl.message('Dư bán', name: 'oversold', desc: '', args: []);
  }

  /// `Giá giao dịch phải nằm trong khoảng trần - sàn`
  String get wrongTransactionPrice {
    return Intl.message(
      'Giá giao dịch phải nằm trong khoảng trần - sàn',
      name: 'wrongTransactionPrice',
      desc: '',
      args: [],
    );
  }

  /// `Sai bước giá`
  String get wrongStepPrice {
    return Intl.message(
      'Sai bước giá',
      name: 'wrongStepPrice',
      desc: '',
      args: [],
    );
  }

  /// `Giá nhập phải nằm trong khoảng giá trần và giá sàn`
  String get invalidOutOfRange {
    return Intl.message(
      'Giá nhập phải nằm trong khoảng giá trần và giá sàn',
      name: 'invalidOutOfRange',
      desc: '',
      args: [],
    );
  }

  /// `Bước giá không hợp lệ phải chia hết cho 10 đ`
  String get invalidStep10 {
    return Intl.message(
      'Bước giá không hợp lệ phải chia hết cho 10 đ',
      name: 'invalidStep10',
      desc: '',
      args: [],
    );
  }

  /// `Bước giá không hợp lệ phải chia hết cho 50 đ`
  String get invalidStep50 {
    return Intl.message(
      'Bước giá không hợp lệ phải chia hết cho 50 đ',
      name: 'invalidStep50',
      desc: '',
      args: [],
    );
  }

  /// `Bước giá không hợp lệ phải chia hết cho 100 đ`
  String get invalidStep100 {
    return Intl.message(
      'Bước giá không hợp lệ phải chia hết cho 100 đ',
      name: 'invalidStep100',
      desc: '',
      args: [],
    );
  }

  /// `Khối lượng tối thiểu 100 và phải là bội số của 100`
  String get invalidMinMass {
    return Intl.message(
      'Khối lượng tối thiểu 100 và phải là bội số của 100',
      name: 'invalidMinMass',
      desc: '',
      args: [],
    );
  }

  /// `Vượt khối lượng tối đa là`
  String get invalidMaxVolume {
    return Intl.message(
      'Vượt khối lượng tối đa là',
      name: 'invalidMaxVolume',
      desc: '',
      args: [],
    );
  }

  /// `Vượt quá sức mua của tiểu khoản`
  String get invalidMaxBuy {
    return Intl.message(
      'Vượt quá sức mua của tiểu khoản',
      name: 'invalidMaxBuy',
      desc: '',
      args: [],
    );
  }

  /// `Bạn không nắm giữ cổ phiếu này`
  String get invalidMaxSell {
    return Intl.message(
      'Bạn không nắm giữ cổ phiếu này',
      name: 'invalidMaxSell',
      desc: '',
      args: [],
    );
  }

  /// `Giao dịch lô lẻ chỉ sử dụng Lệnh LO`
  String get invalidOddLot {
    return Intl.message(
      'Giao dịch lô lẻ chỉ sử dụng Lệnh LO',
      name: 'invalidOddLot',
      desc: '',
      args: [],
    );
  }

  /// `Trần`
  String get priceCeiling {
    return Intl.message('Trần', name: 'priceCeiling', desc: '', args: []);
  }

  /// `Sàn`
  String get priceFloor {
    return Intl.message('Sàn', name: 'priceFloor', desc: '', args: []);
  }

  /// `Giá khớp`
  String get pricePrice {
    return Intl.message('Giá khớp', name: 'pricePrice', desc: '', args: []);
  }

  /// `Thấp`
  String get priceLow {
    return Intl.message('Thấp', name: 'priceLow', desc: '', args: []);
  }

  /// `Cao`
  String get priceHigh {
    return Intl.message('Cao', name: 'priceHigh', desc: '', args: []);
  }

  /// `Nhỏ hơn`
  String get less {
    return Intl.message('Nhỏ hơn', name: 'less', desc: '', args: []);
  }

  /// `Lớn hơn`
  String get bigger {
    return Intl.message('Lớn hơn', name: 'bigger', desc: '', args: []);
  }

  /// `KL`
  String get volumeKL {
    return Intl.message('KL', name: 'volumeKL', desc: '', args: []);
  }

  /// `Giá kích hoạt`
  String get activationPrice {
    return Intl.message(
      'Giá kích hoạt',
      name: 'activationPrice',
      desc: '',
      args: [],
    );
  }

  /// `Khi có sự kiện quyền pha loãng giá cổ phiếu`
  String get whenHaveEventToDiluteStock {
    return Intl.message(
      'Khi có sự kiện quyền pha loãng giá cổ phiếu',
      name: 'whenHaveEventToDiluteStock',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh chờ`
  String get waitingCommand {
    return Intl.message('Lệnh chờ', name: 'waitingCommand', desc: '', args: []);
  }

  /// `Lệnh cắt lỗ`
  String get stopLossCommand {
    return Intl.message(
      'Lệnh cắt lỗ',
      name: 'stopLossCommand',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh chốt lời`
  String get takeProfitCommand {
    return Intl.message(
      'Lệnh chốt lời',
      name: 'takeProfitCommand',
      desc: '',
      args: [],
    );
  }

  /// `Khi có sự kiện quyền pha loãng giá cổ phiếu`
  String get dilutionChoiceTitle {
    return Intl.message(
      'Khi có sự kiện quyền pha loãng giá cổ phiếu',
      name: 'dilutionChoiceTitle',
      desc: '',
      args: [],
    );
  }

  /// `Tự động điều chỉnh giá kích hoạt & giá đặt`
  String get autoAdjustTriggerPriceAndSetPrice {
    return Intl.message(
      'Tự động điều chỉnh giá kích hoạt & giá đặt',
      name: 'autoAdjustTriggerPriceAndSetPrice',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh chờ mua`
  String get pendingBuyCommand {
    return Intl.message(
      'Lệnh chờ mua',
      name: 'pendingBuyCommand',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh chờ bán`
  String get pendingSellCommand {
    return Intl.message(
      'Lệnh chờ bán',
      name: 'pendingSellCommand',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh chỉ được kích hoạt 01 lần duy nhất trong thời gian hiệu lực.`
  String get conditionalOrderWaitingConfirmNote {
    return Intl.message(
      'Lệnh chỉ được kích hoạt 01 lần duy nhất trong thời gian hiệu lực.',
      name: 'conditionalOrderWaitingConfirmNote',
      desc: '',
      args: [],
    );
  }

  /// `Bằng việc %&Xác nhận lệnh%&, bạn hiểu và chấp thuận với các lệnh giao dịch  được phát sinh khi thỏa mãn các điều kiện kích hoạt.`
  String get conditionalOrderWaitingConfirmGuide {
    return Intl.message(
      'Bằng việc %&Xác nhận lệnh%&, bạn hiểu và chấp thuận với các lệnh giao dịch  được phát sinh khi thỏa mãn các điều kiện kích hoạt.',
      name: 'conditionalOrderWaitingConfirmGuide',
      desc: '',
      args: [],
    );
  }

  /// `Giá vốn bình quân`
  String get averageCostPriceConditional {
    return Intl.message(
      'Giá vốn bình quân',
      name: 'averageCostPriceConditional',
      desc: '',
      args: [],
    );
  }

  /// `Khối lượng nắm giữ`
  String get holdVolume {
    return Intl.message(
      'Khối lượng nắm giữ',
      name: 'holdVolume',
      desc: '',
      args: [],
    );
  }

  /// `Biên trượt giá`
  String get slippageMargin {
    return Intl.message(
      'Biên trượt giá',
      name: 'slippageMargin',
      desc: '',
      args: [],
    );
  }

  /// `Điều kiện kích hoạt`
  String get triggerCondition {
    return Intl.message(
      'Điều kiện kích hoạt',
      name: 'triggerCondition',
      desc: '',
      args: [],
    );
  }

  /// `Tỷ lệ chốt lời (%)`
  String get takeProfitRatePercent {
    return Intl.message(
      'Tỷ lệ chốt lời (%)',
      name: 'takeProfitRatePercent',
      desc: '',
      args: [],
    );
  }

  /// `Biên giá chốt lời`
  String get profitMargin {
    return Intl.message(
      'Biên giá chốt lời',
      name: 'profitMargin',
      desc: '',
      args: [],
    );
  }

  /// `Biên giá cắt lỗ`
  String get stopLossMargin {
    return Intl.message(
      'Biên giá cắt lỗ',
      name: 'stopLossMargin',
      desc: '',
      args: [],
    );
  }

  /// `Tỷ lệ cắt lỗ (%)`
  String get stopLossRatePercent {
    return Intl.message(
      'Tỷ lệ cắt lỗ (%)',
      name: 'stopLossRatePercent',
      desc: '',
      args: [],
    );
  }

  /// `Tỷ lệ cắt lỗ`
  String get stopLossRate {
    return Intl.message(
      'Tỷ lệ cắt lỗ',
      name: 'stopLossRate',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo điều kiện NĐT cài đặt`
  String get pendingOrderCommandDescription {
    return Intl.message(
      'Lệnh được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo điều kiện NĐT cài đặt',
      name: 'pendingOrderCommandDescription',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo kỳ vọng chốt lời của NĐT`
  String get takeProfitCommandDescription {
    return Intl.message(
      'Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo kỳ vọng chốt lời của NĐT',
      name: 'takeProfitCommandDescription',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động đến điểm cắt lỗ của NĐT`
  String get stopLossCommandDescription {
    return Intl.message(
      'Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động đến điểm cắt lỗ của NĐT',
      name: 'stopLossCommandDescription',
      desc: '',
      args: [],
    );
  }

  /// `Không hợp lệ`
  String get invalidRateProfit {
    return Intl.message(
      'Không hợp lệ',
      name: 'invalidRateProfit',
      desc: '',
      args: [],
    );
  }

  /// `Không hợp lệ`
  String get invalidInput {
    return Intl.message(
      'Không hợp lệ',
      name: 'invalidInput',
      desc: '',
      args: [],
    );
  }

  /// `Biên trượt không hợp lệ`
  String get invalidSlipPagePrice {
    return Intl.message(
      'Biên trượt không hợp lệ',
      name: 'invalidSlipPagePrice',
      desc: '',
      args: [],
    );
  }

  /// `Bạn không nắm giữ mã chứng khoán này`
  String get youNotHoldingThisStock {
    return Intl.message(
      'Bạn không nắm giữ mã chứng khoán này',
      name: 'youNotHoldingThisStock',
      desc: '',
      args: [],
    );
  }

  /// `Không thể sửa lệnh`
  String get canNotEditCommand {
    return Intl.message(
      'Không thể sửa lệnh',
      name: 'canNotEditCommand',
      desc: '',
      args: [],
    );
  }

  /// `Bạn không thể sửa lệnh do không còn nắm giữ\nmã chứng khoán`
  String get canNotEditCommandWarning {
    return Intl.message(
      'Bạn không thể sửa lệnh do không còn nắm giữ\nmã chứng khoán',
      name: 'canNotEditCommandWarning',
      desc: '',
      args: [],
    );
  }

  /// `Bạn không thể sửa lệnh do không còn nắm giữ mã chứng khoán`
  String get noteCanNotEditCommand {
    return Intl.message(
      'Bạn không thể sửa lệnh do không còn nắm giữ mã chứng khoán',
      name: 'noteCanNotEditCommand',
      desc: '',
      args: [],
    );
  }

  /// `Vui lòng nhập khối lượng`
  String get pleaseInputVolume {
    return Intl.message(
      'Vui lòng nhập khối lượng',
      name: 'pleaseInputVolume',
      desc: '',
      args: [],
    );
  }

  /// `Biên trượt giá không hợp lệ`
  String get priceSlippageInvalid {
    return Intl.message(
      'Biên trượt giá không hợp lệ',
      name: 'priceSlippageInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Khối lượng tối đa là`
  String get maxVolumeIs {
    return Intl.message(
      'Khối lượng tối đa là',
      name: 'maxVolumeIs',
      desc: '',
      args: [],
    );
  }

  /// `Khối lượng không hợp lệ`
  String get validateVolume {
    return Intl.message(
      'Khối lượng không hợp lệ',
      name: 'validateVolume',
      desc: '',
      args: [],
    );
  }

  /// `Giá trị dự kiến`
  String get expectValue {
    return Intl.message(
      'Giá trị dự kiến',
      name: 'expectValue',
      desc: '',
      args: [],
    );
  }

  /// `Giá đặt không hợp lệ`
  String get invalidConditionalPrice {
    return Intl.message(
      'Giá đặt không hợp lệ',
      name: 'invalidConditionalPrice',
      desc: '',
      args: [],
    );
  }

  /// `Giá kích hoạt không hợp lệ`
  String get invalidActivationPrice {
    return Intl.message(
      'Giá kích hoạt không hợp lệ',
      name: 'invalidActivationPrice',
      desc: '',
      args: [],
    );
  }

  /// `Không thể đặt lô lẻ với lệnh thị trường`
  String get cannotOrderOddLotWithMarketCommand {
    return Intl.message(
      'Không thể đặt lô lẻ với lệnh thị trường',
      name: 'cannotOrderOddLotWithMarketCommand',
      desc: '',
      args: [],
    );
  }

  /// `Khối lượng tối thiểu 100 và là bội số 100`
  String get volumeMustEvenLot {
    return Intl.message(
      'Khối lượng tối thiểu 100 và là bội số 100',
      name: 'volumeMustEvenLot',
      desc: '',
      args: [],
    );
  }

  /// `Hủy tất cả lệnh`
  String get cancelAll {
    return Intl.message(
      'Hủy tất cả lệnh',
      name: 'cancelAll',
      desc: '',
      args: [],
    );
  }

  /// `Không có kết quả nào phù hợp với bộ lọc. Vui lòng thay đổi điều kiện lọc và thử lại`
  String get noFilter {
    return Intl.message(
      'Không có kết quả nào phù hợp với bộ lọc. Vui lòng thay đổi điều kiện lọc và thử lại',
      name: 'noFilter',
      desc: '',
      args: [],
    );
  }

  /// `Liên hệ DVKH`
  String get contactCustomerService {
    return Intl.message(
      'Liên hệ DVKH',
      name: 'contactCustomerService',
      desc: '',
      args: [],
    );
  }

  /// `Cân nhắc phương án đầu tư`
  String get priceThresholdDialogTitle {
    return Intl.message(
      'Cân nhắc phương án đầu tư',
      name: 'priceThresholdDialogTitle',
      desc: '',
      args: [],
    );
  }

  /// `Tiềm năng tăng giá danh mục hiện tại nhỏ hơn 10% so với giá kỳ vọng. Quý khách cân nhắc trước khi đầu tư theo danh mục!`
  String get priceThresholdDialogContent {
    return Intl.message(
      'Tiềm năng tăng giá danh mục hiện tại nhỏ hơn 10% so với giá kỳ vọng. Quý khách cân nhắc trước khi đầu tư theo danh mục!',
      name: 'priceThresholdDialogContent',
      desc: '',
      args: [],
    );
  }

  /// `Cắt lỗ`
  String get stopLoss {
    return Intl.message('Cắt lỗ', name: 'stopLoss', desc: '', args: []);
  }

  /// `Chốt lời`
  String get takeProfit {
    return Intl.message('Chốt lời', name: 'takeProfit', desc: '', args: []);
  }

  /// `Sức mua`
  String get purchasingAbility {
    return Intl.message(
      'Sức mua',
      name: 'purchasingAbility',
      desc: '',
      args: [],
    );
  }

  /// `Tỷ lệ vay`
  String get marginRate {
    return Intl.message('Tỷ lệ vay', name: 'marginRate', desc: '', args: []);
  }

  /// `Khối lượng`
  String get volume {
    return Intl.message('Khối lượng', name: 'volume', desc: '', args: []);
  }

  /// `Quay lại`
  String get back {
    return Intl.message('Quay lại', name: 'back', desc: '', args: []);
  }

  /// `Giá trị`
  String get value {
    return Intl.message('Giá trị', name: 'value', desc: '', args: []);
  }

  /// `Thời gian hiệu lực`
  String get effectiveTime {
    return Intl.message(
      'Thời gian hiệu lực',
      name: 'effectiveTime',
      desc: '',
      args: [],
    );
  }

  /// `Ngày hiệu lực`
  String get effectiveDate {
    return Intl.message(
      'Ngày hiệu lực',
      name: 'effectiveDate',
      desc: '',
      args: [],
    );
  }

  /// `Đặt lệnh thành công`
  String get successOrderPlace {
    return Intl.message(
      'Đặt lệnh thành công',
      name: 'successOrderPlace',
      desc: '',
      args: [],
    );
  }

  /// `Vui lòng điền giá`
  String get pleaseEnterPrice {
    return Intl.message(
      'Vui lòng điền giá',
      name: 'pleaseEnterPrice',
      desc: '',
      args: [],
    );
  }

  /// `đ`
  String get vnd {
    return Intl.message('đ', name: 'vnd', desc: '', args: []);
  }

  /// `Hiển thị thông báo này trong lần sau`
  String get showMessageNextTime {
    return Intl.message(
      'Hiển thị thông báo này trong lần sau',
      name: 'showMessageNextTime',
      desc: '',
      args: [],
    );
  }

  /// `Đóng`
  String get commandClose {
    return Intl.message('Đóng', name: 'commandClose', desc: '', args: []);
  }

  /// `Xác nhận dừng đặt lệnh`
  String get commandDialogCancel {
    return Intl.message(
      'Xác nhận dừng đặt lệnh',
      name: 'commandDialogCancel',
      desc: '',
      args: [],
    );
  }

  /// `Để sau`
  String get orderConfirmLate {
    return Intl.message('Để sau', name: 'orderConfirmLate', desc: '', args: []);
  }

  /// `Thông báo`
  String get notConfirmCBTTClose {
    return Intl.message(
      'Thông báo',
      name: 'notConfirmCBTTClose',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh thường`
  String get orderTypeLo {
    return Intl.message('Lệnh thường', name: 'orderTypeLo', desc: '', args: []);
  }

  /// `Lệnh Buy-in`
  String get orderTypeBuyin {
    return Intl.message(
      'Lệnh Buy-in',
      name: 'orderTypeBuyin',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh ATO`
  String get orderTypeAto {
    return Intl.message('Lệnh ATO', name: 'orderTypeAto', desc: '', args: []);
  }

  /// `Lệnh ATC`
  String get orderTypeAtc {
    return Intl.message('Lệnh ATC', name: 'orderTypeAtc', desc: '', args: []);
  }

  /// `Lệnh MP`
  String get orderTypeMp {
    return Intl.message('Lệnh MP', name: 'orderTypeMp', desc: '', args: []);
  }

  /// `Lệnh MAK`
  String get orderTypeMak {
    return Intl.message('Lệnh MAK', name: 'orderTypeMak', desc: '', args: []);
  }

  /// `Lệnh MOK`
  String get orderTypeMok {
    return Intl.message('Lệnh MOK', name: 'orderTypeMok', desc: '', args: []);
  }

  /// `Lệnh MTL`
  String get orderTypeMtl {
    return Intl.message('Lệnh MTL', name: 'orderTypeMtl', desc: '', args: []);
  }

  /// `Lệnh PLO`
  String get orderTypePlo {
    return Intl.message('Lệnh PLO', name: 'orderTypePlo', desc: '', args: []);
  }

  /// `Lệnh điều kiện`
  String get orderTypeCondition {
    return Intl.message(
      'Lệnh điều kiện',
      name: 'orderTypeCondition',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh GTC`
  String get orderTypeGtc {
    return Intl.message('Lệnh GTC', name: 'orderTypeGtc', desc: '', args: []);
  }

  /// `Lệnh tranh mua bán tại mức giá mở cửa`
  String get orderDescriptionAto {
    return Intl.message(
      'Lệnh tranh mua bán tại mức giá mở cửa',
      name: 'orderDescriptionAto',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh đặt mua - bán chứng khoán với bất kỳ mức giá nào trong thời gian giao dịch`
  String get orderDescriptionMp {
    return Intl.message(
      'Lệnh đặt mua - bán chứng khoán với bất kỳ mức giá nào trong thời gian giao dịch',
      name: 'orderDescriptionMp',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh thị trường khớp và huỷ tức có thể thực hiện toàn bộ hoặc một phần phần còn lại sẽ bị huỷ ngay sau khi khớp lệnh`
  String get orderDescriptionMak {
    return Intl.message(
      'Lệnh thị trường khớp và huỷ tức có thể thực hiện toàn bộ hoặc một phần phần còn lại sẽ bị huỷ ngay sau khi khớp lệnh',
      name: 'orderDescriptionMak',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh thị trường khớp toàn bộ hoặc huỷ nếu không thực hiện được toàn bộ thì bị huỷ ngay sau khi nhập`
  String get orderDescriptionMok {
    return Intl.message(
      'Lệnh thị trường khớp toàn bộ hoặc huỷ nếu không thực hiện được toàn bộ thì bị huỷ ngay sau khi nhập',
      name: 'orderDescriptionMok',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh thị trường giới hạn nếu không thực hiện được toàn bộ thì phần còn lại chuyển thành lệnh LO và áp dụng các quy định về sửa huỷ đối với lệnh LO`
  String get orderDescriptionMtl {
    return Intl.message(
      'Lệnh thị trường giới hạn nếu không thực hiện được toàn bộ thì phần còn lại chuyển thành lệnh LO và áp dụng các quy định về sửa huỷ đối với lệnh LO',
      name: 'orderDescriptionMtl',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh đặt mua - bán chứng khoán theo giá mong muốn`
  String get orderDescriptionLo {
    return Intl.message(
      'Lệnh đặt mua - bán chứng khoán theo giá mong muốn',
      name: 'orderDescriptionLo',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh bán áp dụng với các mã chứng khoán trong phiên Buy-in từ 8h45 - 9h00`
  String get orderDescriptionBuyin {
    return Intl.message(
      'Lệnh bán áp dụng với các mã chứng khoán trong phiên Buy-in từ 8h45 - 9h00',
      name: 'orderDescriptionBuyin',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh mua hoặc lệnh bán được thực hiện tại thời điểm giá đang đóng cửa`
  String get orderDescriptionAtc {
    return Intl.message(
      'Lệnh mua hoặc lệnh bán được thực hiện tại thời điểm giá đang đóng cửa',
      name: 'orderDescriptionAtc',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh mua hoặc bán chứng khoán tại mức giá đóng cửa sau khi kết thúc phiên ATC`
  String get orderDescriptionPlo {
    return Intl.message(
      'Lệnh mua hoặc bán chứng khoán tại mức giá đóng cửa sau khi kết thúc phiên ATC',
      name: 'orderDescriptionPlo',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh được kích hoạt khi giá đặt lệnh nằm trong khoảng trần - sàn của ngày giao dịch có hiệu lực trong khoảng thời gian thiết lập`
  String get orderDescriptionGtc {
    return Intl.message(
      'Lệnh được kích hoạt khi giá đặt lệnh nằm trong khoảng trần - sàn của ngày giao dịch có hiệu lực trong khoảng thời gian thiết lập',
      name: 'orderDescriptionGtc',
      desc: '',
      args: [],
    );
  }

  /// `Loại lệnh`
  String get typeCommand {
    return Intl.message('Loại lệnh', name: 'typeCommand', desc: '', args: []);
  }

  /// `Mã CK`
  String get codeTitle {
    return Intl.message('Mã CK', name: 'codeTitle', desc: '', args: []);
  }

  /// `Giá đặt/\nGiá khớp`
  String get bidPriceTitle {
    return Intl.message(
      'Giá đặt/\nGiá khớp',
      name: 'bidPriceTitle',
      desc: '',
      args: [],
    );
  }

  /// `KL đặt/\nKL khớp`
  String get volTitle {
    return Intl.message(
      'KL đặt/\nKL khớp',
      name: 'volTitle',
      desc: '',
      args: [],
    );
  }

  /// `Trạng thái`
  String get statusTitle {
    return Intl.message('Trạng thái', name: 'statusTitle', desc: '', args: []);
  }

  /// `Không tìm thấy dữ liệu`
  String get noDataFound {
    return Intl.message(
      'Không tìm thấy dữ liệu',
      name: 'noDataFound',
      desc: '',
      args: [],
    );
  }

  /// `Ngày`
  String get date {
    return Intl.message('Ngày', name: 'date', desc: '', args: []);
  }

  /// `tháng`
  String get month {
    return Intl.message('tháng', name: 'month', desc: '', args: []);
  }

  /// `Đang lời`
  String get itm {
    return Intl.message('Đang lời', name: 'itm', desc: '', args: []);
  }

  /// `Hòa vốn`
  String get atm {
    return Intl.message('Hòa vốn', name: 'atm', desc: '', args: []);
  }

  /// `Đang lỗ`
  String get otm {
    return Intl.message('Đang lỗ', name: 'otm', desc: '', args: []);
  }

  /// `Tiền`
  String get money {
    return Intl.message('Tiền', name: 'money', desc: '', args: []);
  }

  /// `Cổ phiếu`
  String get share {
    return Intl.message('Cổ phiếu', name: 'share', desc: '', args: []);
  }

  /// `Cá nhân`
  String get individual {
    return Intl.message('Cá nhân', name: 'individual', desc: '', args: []);
  }

  /// `Tổ chức`
  String get organization {
    return Intl.message('Tổ chức', name: 'organization', desc: '', args: []);
  }

  /// `Doanh thu`
  String get revenue {
    return Intl.message('Doanh thu', name: 'revenue', desc: '', args: []);
  }

  /// `Lợi nhuận`
  String get profit {
    return Intl.message('Lợi nhuận', name: 'profit', desc: '', args: []);
  }

  /// `Ngày GDKHQ`
  String get shortExrightDate {
    return Intl.message(
      'Ngày GDKHQ',
      name: 'shortExrightDate',
      desc: '',
      args: [],
    );
  }

  /// `Giao dịch không hưởng quyền`
  String get exrightDate {
    return Intl.message(
      'Giao dịch không hưởng quyền',
      name: 'exrightDate',
      desc: '',
      args: [],
    );
  }

  /// `Ngày công bố`
  String get publicDate {
    return Intl.message('Ngày công bố', name: 'publicDate', desc: '', args: []);
  }

  /// `Ngày thực hiện`
  String get issueDate {
    return Intl.message(
      'Ngày thực hiện',
      name: 'issueDate',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<WealthStock> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'vi'),
      Locale.fromSubtags(languageCode: 'en'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<WealthStock> load(Locale locale) => WealthStock.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
