import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/condition_command_const.dart';

class ConditionNormalTitle extends StatelessWidget {
  const ConditionNormalTitle({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        left: SizeUtils.kSize16,
        right: SizeUtils.kSize16,
        top: SizeUtils.kSize12,
      ),
      child: Row(
        children: [
          Expanded(
            flex: ConditionCommandConst.expandTitleWidget[0],
            child: _Text(
              title: WealthStock.current.codeTitle,
              textAlign: TextAlign.start,
            ),
          ),
          Expanded(
            flex: ConditionCommandConst.expandTitleWidget[1],
            child: _Text(
              title: WealthStock.current.bidPriceTitle,
              textAlign: TextAlign.end,
            ),
          ),
          Expanded(
            flex: ConditionCommandConst.expandTitleWidget[2],
            child: _Text(
              title: WealthStock.current.volTitle,
              textAlign: TextAlign.end,
            ),
          ),
          Expanded(
            flex: ConditionCommandConst.expandTitleWidget[3],
            child: _Text(
              title: WealthStock.current.statusTitle,
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }
}

class _Text extends StatelessWidget {
  final String title;
  final TextAlign? textAlign;

  const _Text({Key? key, required this.title, this.textAlign})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      textAlign: textAlign,
      style: vpTextStyle.captionRegular,
    );
  }
}
