import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/widget/vpbank_loading.dart';
import 'package:vp_design_system/custom_widget/app_refresh_view.dart';
import 'package:vp_design_system/custom_widget/listview_helper.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/enum/transaction_filter_enum.dart';
import 'package:vp_wealth/data/enum/transaction_type_enum.dart';
import 'package:vp_wealth/data/model/asset/copier_info.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/stock_right/widget/error_widget.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/cubit/command_history_cubit.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/hearder_command_history_widget.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/widget/command_history_item.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/widget/command_history_loading_widget.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/widget/condition_normal_title_view.dart';
import 'package:vp_wealth/presentation/wealths/order/cubit/order_container_cubit.dart';
import 'package:vp_wealth/presentation/wealths/widgets/none_widget.dart';

class CommandHistoryPage extends StatefulWidget {
  final OrderContainerCubit containerCubit;

  const CommandHistoryPage({Key? key, required this.containerCubit})
      : super(key: key);

  @override
  State<CommandHistoryPage> createState() => _CommandHistoryPageState();
}

class _CommandHistoryPageState extends State<CommandHistoryPage> {
  final _cubit = CommandHistoryCubit();

  OrderContainerCubit get containerCubit => widget.containerCubit;

  @override
  void initState() {
    super.initState();
    _cubit.initial();
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<CommandHistoryCubit>.value(
      value: _cubit,
      child: BlocListener<CommandHistoryCubit, CommandHistoryState>(
        listener: (context, state) {},
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BlocBuilder<CommandHistoryCubit, CommandHistoryState>(
                  builder: (_, state) => HeaderCommandHistoryWidget(
                    enable: true,
                    transaction: state.transaction,
                    investmentType: state.investmentType,
                    symbol: state.symbol,
                    listStatus: state.listStatus,
                    listTime: state.listTime,
                    accountModel: state.accountModel,
                    onChangeStatus: (value) => _cubit.onChangeStatus(value),
                    onChangeType: (value) =>
                        _cubit.onChangeTransactionType(value as TransactionFilterEnum?),
                    onChangeSymbol: (value) => _cubit.onChangeSymbol(value as ItemData?),
                    onChangeTime: (value) => _cubit.onChangeTime(value),
                    onChangePlan: (value) => _cubit.onChangePlan(value as AccountModel?),
                    onChangeInvestmentType: (value) =>
                        _cubit.onChangeInvestmentType(value as TransactionTypeEnum?),
                  ),
                ),
                Expanded(
                  child: BlocBuilder<CommandHistoryCubit, CommandHistoryState>(
                    buildWhen: (previous, current) =>
                        previous.loading != current.loading ||
                        previous.allItem.length != current.allItem.length,
                    builder: (_, state) {
                      if (state.loading) {
                        return const CommandHistoryLoadingWidget();
                      } else if (state.error && state.allItem.isEmpty) {
                        return ErrorNetworkWidget(
                          tryAgain: true,
                          onPressed: () => _cubit.onRefresh(),
                        );
                      } else {
                        if (_cubit.state.noData) {
                          return buildNoDataView();
                        }

                        if (_cubit.state.noFilterData) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: SizeUtils.kSize24),
                            child: Container(),
                          );
                        }

                        return Column(
                          children: [
                            BlocBuilder<OrderContainerCubit,
                                OrderContainerState>(
                              builder: (_, state) {
                                return const ConditionNormalTitle();
                              },
                            ),
                            Expanded(child: buildListView()),
                          ],
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
            buildLoadingView(),
          ],
        ),
      ),
    );
  }

  Widget buildNoDataView() {
    return PullToRefreshView(
      onRefresh: () => _cubit.onRefresh(),
      child: NoneWidget(
        height: double.infinity,
        physics: const AlwaysScrollableScrollPhysics(),
        desc: WealthStock.current.noData,
        padding: const EdgeInsets.only(top: SizeUtils.kSize100),
      ),
    );
  }

  Widget buildListView() {
    return PullToRefreshView(
      onRefresh: () => _cubit.onRefresh(),
      child: ListViewHelper(
        physics: const AlwaysScrollableScrollPhysics(),
        hasMore: _cubit.hasLoadMore,
        loadMore: () => _cubit.onLoadMore(),
        refresh: () => _cubit.onRefresh(),
        loadMoreView: () => const VPBankLoading(),
        itemBuilder: (_, index) {
          var data = (_cubit.state.listItems ?? [])[index];
          final key = '${data.symbol}${data.orderId}';

          return CommandHistoryItem(
            key: ValueKey(key),
            item: data,
            onDeleteCommand: () => _cubit.onDeleteCommand(data),
          );
        },
        itemCount: () => (_cubit.state.listItems ?? []).length,
      ),
    );
  }

  Widget buildLoadingView() {
    return BlocSelector<CommandHistoryCubit, CommandHistoryState, bool>(
      selector: (state) => state.loading,
      builder: (_, loading) {
        if (!loading) return const SizedBox();

        return const Positioned.fill(
          child: VPBankLoadingDefault(bgColor: Colors.transparent),
        );
      },
    );
  }
}
