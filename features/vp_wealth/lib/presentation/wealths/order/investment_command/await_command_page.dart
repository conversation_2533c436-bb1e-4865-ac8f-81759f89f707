import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/utils/money_utils.dart';
import 'package:vp_common/widget/vpbank_loading.dart';
import 'package:vp_core/cubit/auth_cubit.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/string_extensions.dart';
import 'package:vp_wealth/data/enum/money_tranfer_enum.dart';
import 'package:vp_wealth/data/enum/transaction_type_enum.dart';
import 'package:vp_wealth/data/model/assets/item_assets_model.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/wealth_notification_dialog.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/stock_right/widget/error_widget.dart';
import 'package:vp_wealth/presentation/wealths/order/investment_command/cubit/investment_command_cubit.dart';
import 'package:vp_wealth/presentation/wealths/order/investment_command/widget/list_stock_category_investment.dart';
import 'package:vp_wealth/router/wealth_router.dart';

import '../../../../data/enum/investment_command_status_enum.dart';
import '../../widgets/utils/item_row_text.dart';

class AwaitCommandPage extends StatefulWidget {
  final int? commandId;

  const AwaitCommandPage({super.key, this.commandId});

  @override
  State<AwaitCommandPage> createState() => _AwaitCommandPageState();
}

class _AwaitCommandPageState extends State<AwaitCommandPage> {
  int? get _commandId => widget.commandId;

  final cubit = InvestmentCommandCubit();

  @override
  void initState() {
    super.initState();
    cubit.getDetailCommand(_commandId ?? 0);
  }

  @override
  void dispose() {
    cubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<InvestmentCommandCubit>(
      create: (context) => cubit,
      child: BlocBuilder<InvestmentCommandCubit, InvestmentCommandState>(
        buildWhen:
            (previous, current) =>
                previous.loading != current.loading ||
                previous.detailSuccess != current.detailSuccess,
        builder: (context, state) {
          if (state.loading) {
            return Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.white,
              child: const VPBankLoadingDefault(bgColor: Colors.transparent),
            );
          } else if (state.error) {
            return ErrorNetworkWidget(
              tryAgain: true,
              onPressed:
                  () => () {
                    print('refresh data');
                  },
            );
          } else {
            return VPScaffold(
              appBar: VPAppBar.flows(title: 'Xác nhận lệnh'),
              backgroundColor: vpColor.backgroundElevation0,
              body: SingleChildScrollView(child: _infoCommandView()),
              bottomNavigationBar: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(height: 1, color: vpColor.strokeNormal),
                  SizedBox(
                    height: 100,
                    child:
                        cubit.investCommand.status ==
                                InvestmentCommandStatusEnum.expired
                            ? _closeAcceptCommand
                            : _actionAcceptCommand,
                  ),
                ],
              ),
            );
          }
        },
      ),
    );
  }

  Widget _infoCommandView() {
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(height: 8, color: vpColor.backgroundElevationMinus1),
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                kSpacingHeight12,
                Text(
                  'Thông tin lệnh',
                  style: vpTextStyle.subtitle16?.copyWith(
                    color: vpColor.textPrimary,
                  ),
                ),
                if (cubit.investCommand.status ==
                    InvestmentCommandStatusEnum.expired)
                  ItemRowText(
                    title: 'Trạng thái',
                    value: 'Hết hiệu lực',
                    color: vpColor.backgroundAccentNeutral,
                    isStatus: true,
                  ),
                ItemRowText(
                  title: 'Loại giao dịch',
                  value: 'Mua',
                  textColor: vpColor.textAccentGreen,
                ),
                ItemRowText(
                  title: 'Loại đầu tư',
                  value: cubit.investCommand.transactionType?.sortTitle,
                ),
                ItemRowText(
                  title: 'Ngày giao dịch',
                  value: cubit.investCommand.createdDate?.dateToDateDdMmYyyy(
                    'dd/MM/yyyy hh:mm',
                  ),
                ),
                ItemRowText(
                  title: 'Số tài khoản',
                  value:
                      GetIt.instance<AuthCubit>().userInfo?.userinfo?.custodycd,
                ),
                const ItemRowText(title: 'Loại Lệnh', value: 'Lệnh thường'),
                ItemRowText(
                  title: 'Tổng giá trị giao dịch dự kiến',
                  value: MoneyUtils.formatMoney(
                    (cubit.investCommand.totalExpectedValue).toDouble(),
                  ),
                ),
              ],
            ),
          ),
          kSpacingHeight12,
          _divider,
          kSpacingHeight12,
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Danh mục lệnh',
                  style: vpTextStyle.subtitle16?.copyWith(
                    color: vpColor.textPrimary,
                  ),
                ),
                ListStockCategoryInvestment(model: cubit.investCommand),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget get _closeAcceptCommand {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          VpsButton.secondarySmall(
            title: 'Đóng',
            width: double.infinity,
            textAlign: TextAlign.center,
            alignment: Alignment.center,
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  Widget get _actionAcceptCommand {
    bool isDisableButton =
        ((cubit.investCommand.investmentNoticeList ?? []).fold<num>(
          0,
          (total, item) => total + (item.orderQty ?? 0),
        )) ==
        0;
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          VpsButton.primarySmall(
            width: double.infinity,
            disabled: isDisableButton,
            title: 'Xác nhận mua',
            onPressed:
                () => cubit.acceptCommand(
                  id: cubit.investCommand.id ?? -1,
                  copierId: cubit.investCommand.copierId ?? -1,
                  totalCurrentValue: cubit.investCommand.totalAmount ?? 0,
                  onTransferMoney:
                      () => _onDialogHaveNotEnoughMoneyInvestment(),
                ),
          ),
        ],
      ),
    );
  }

  Widget get _divider {
    return SizedBox(
      height: 2,
      width: double.infinity,
      child: ColoredBox(color: vpColor.backgroundElevationMinus1),
    );
  }

  void _onDialogHaveNotEnoughMoneyInvestment() {
    showNotifyDialog(
      barrierDismissible: false,
      context: context,
      title: 'Không đủ số dư',
      iconSize: 96,
      content:
          'Qúy khách không đủ số dư trong tiểu khoản của kế hoạch để đặt lệnh. Vui lòng nạp tiền vào tiểu khoản để thực hiện đầu tư.',
      image: Assets.icons.icCancel.path,
      textButtonLeft: WealthStock.current.buttonClose,
      colorButtonRight: vpColor.backgroundBrand,
      colorButtonLeft: Colors.transparent,
      colorBorderButtonLeft: vpColor.textPrimary,
      textStyleLeft: vpTextStyle.body14?.copyWith(color: vpColor.textPrimary),
      onPressedLeft: () => Navigator.pop(context),
      textButtonRight: 'Nạp tiền',
      onPressedRight: () {
        context.pop();
        context.push(
          WealthRouter.moneyTransferPage,
          extra: ItemAssetsModel(
            moneyTranferEnum: MoneyTranferEnum.AwaitCommandPage,
          ),
        );
      },
    );
  }
}
