import 'package:json_annotation/json_annotation.dart';
import 'package:vp_wealth/common/utils/string_extensions.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/generated/l10n.dart';

import '../../domain/entity/cwinfo_entity.dart';
import '../../domain/enum/cw_info_status.dart';

part 'cwinfo_model.g.dart';

@JsonSerializable()
class CwInfoModel extends CwInfoEntity {
  CwInfoModel({
    required super.issuerOrganCode,
    required super.derivativeTypeCode,
    required super.rightStyleCode,
    required num exercisePrice,
    required num breakEvenPoint,
    required num maturityTime,
    required String termStageCode,
    required num shareIssue,
    required num issuePrice,
    required super.underlyingSymbol,
    required num closePriceUnderlyingSymbol,
    required num changeUnderlyingSymbol,
    required super.status,
    required super.exerciseRatioString,
    required num warrantDeviation,
    required String maturityDate,
    required String issueDate,
    required String listingDate,
  }) : super(
         exercisePrice: exercisePrice.getPriceFormatted(
           convertToThousand: true,
         ),
         breakEvenPoint: breakEvenPoint.getPriceFormatted(
           convertToThousand: true,
         ),
         maturityTime:
             '$maturityTime ${WealthStock.current.date.toLowerCase()}',
         termStageCode: termStageCode.termStageCode,
         shareIssue: shareIssue.toMoney(showSymbol: false),
         issuePrice: issuePrice.getPriceFormatted(convertToThousand: true),
         closePriceUnderlyingSymbol: closePriceUnderlyingSymbol
             .getPriceFormatted(convertToThousand: true),
         changeUnderlyingSymbol: changeUnderlyingSymbol.getPriceFormatted(
           convertToThousand: true,
           prefix: true,
         ),
         warrantDeviation: warrantDeviation.getPriceFormatted(
           convertToThousand: true,
         ),
         maturityDate: maturityDate.stringToDateDdMmYyyyForCWInfo(),
         issueDate: issueDate.stringToDateDdMmYyyyForCWInfo(),
         listingDate: listingDate.stringToDateDdMmYyyyForCWInfo(),
       );

  factory CwInfoModel.fromJson(Map<String, dynamic> json) =>
      _$CwInfoModelFromJson(json);
}

extension on String {
  String get termStageCode =>
      endsWith('D')
          ? replaceFirst('D', ' ${WealthStock.current.date.toLowerCase()}')
          : replaceFirst('M', ' ${WealthStock.current.month.toLowerCase()}');
}
