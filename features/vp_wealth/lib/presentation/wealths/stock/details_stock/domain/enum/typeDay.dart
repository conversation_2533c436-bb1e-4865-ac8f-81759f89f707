import 'package:vp_wealth/generated/l10n.dart';


enum TypeDay {
  exrightDate,
  publicDate,
  issueDate;

  String get label => this == exrightDate
      ? WealthStock.current.shortExrightDate
      : this == issueDate
          ? WealthStock.current.issueDate
          : WealthStock.current.publicDate;

  String get labelBts =>
      this == exrightDate ? WealthStock.current.exrightDate : label;
}
