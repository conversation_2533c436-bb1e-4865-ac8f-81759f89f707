import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/generated/l10n.dart';

enum CWInfoStatus {
  ITM,
  ATM,
  OTM;

  Color get color => this == ATM
      ? themeData.yellow
      : this == ITM
          ? themeData.primary
          : themeData.red;

  String get label => this == ATM
      ? WealthStock.current.atm
      : this == ITM
          ? WealthStock.current.itm
          : WealthStock.current.otm;
}
