import 'package:flutter/material.dart';
import 'package:vp_common/widget/vpbank_loading.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/app_refresh_view.dart';
import 'package:vp_design_system/custom_widget/listview_helper.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_design_system/widget/appbar/appbar_view.dart';
import 'package:vp_design_system/widget/scaffold/scaffold_view.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/enum/investment_status_enum.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/padding_extends.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/stock_right/widget/error_widget.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/cubit/list_wealth_plan_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/widget/data_empty_component.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/widget/filter_data_empty_component.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/widget/listview_shimmer_view.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/widget/wealth_plan_item.dart';
import 'package:vp_wealth/presentation/wealths/plan/survey/widgets/survey_start_dialog.dart';
import 'package:vp_wealth/router/wealth_router.dart';

class ListWealthPlanScreen extends StatefulWidget {
  const ListWealthPlanScreen({Key? key}) : super(key: key);

  @override
  State<ListWealthPlanScreen> createState() => _ListWealthPlanScreenState();
}

class _ListWealthPlanScreenState extends State<ListWealthPlanScreen>
    with AutomaticKeepAliveClientMixin<ListWealthPlanScreen> {
  @override
  void initState() {
    super.initState();
    context.read<ListWealthPlanCubit>();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return VPScaffold(
      appBar: VPAppBar.flows(
        title: WealthStock.current.plan,
        leading: InkWell(
          onTap: () => Navigator.of(context).pop(),
          child: const Icon(Icons.close).paddingTop4(),
        ),
        actions: [_createPlanName],
      ),
      body: SafeArea(
        child: Column(
          children: [
            _surveyToInvestmentType,
            // Tạm ẩn
            // kSpacingHeight8,
            // const FilterOptionComponents(),
            kSpacingHeight8,
            Expanded(
              child: BlocBuilder<ListWealthPlanCubit, ListWealthPlanState>(
                buildWhen:
                    (previous, current) =>
                        previous.isLoading != current.isLoading ||
                        previous.data.length != current.data.length,
                builder: (context, state) {
                  if (state.isLoading) {
                    return const ListViewShimmerView();
                  } else if (state.isError && state.data.isEmpty) {
                    return ErrorNetworkWidget(
                      onPressed:
                          () => context.read<ListWealthPlanCubit>().onRefresh(),
                    );
                  } else {
                    if (state.data.isEmpty) {
                      if (state.statusFilter != InvestmentStatusEnum.all) {
                        return PullToRefreshView(
                          onRefresh:
                              () =>
                                  context
                                      .read<ListWealthPlanCubit>()
                                      .onRefresh(),
                          child: const FilterDataEmptyComponent(),
                        );
                      }
                      return PullToRefreshView(
                        onRefresh:
                            () =>
                                context.read<ListWealthPlanCubit>().onRefresh(),
                        child: DataEmptyComponent(
                          content: WealthStock.current.noData,
                        ),
                      );
                    }

                    return PullToRefreshView(
                      onRefresh:
                          () => context.read<ListWealthPlanCubit>().onRefresh(),
                      child: ListViewHelper(
                        physics: const AlwaysScrollableScrollPhysics(),
                        hasMore:
                            context.read<ListWealthPlanCubit>().hasloadMore,
                        loadMore:
                            () =>
                                context
                                    .read<ListWealthPlanCubit>()
                                    .onLoadMore(),
                        refresh:
                            () =>
                                context.read<ListWealthPlanCubit>().onRefresh(),
                        itemBuilder: (context, index) {
                          final item = state.data[index];
                          return WealthPlanItem(
                            model: item,
                            onRefreshList:
                                () =>
                                    context
                                        .read<ListWealthPlanCubit>()
                                        .onRefresh(),
                            onDelete:
                                () => context
                                    .read<ListWealthPlanCubit>()
                                    .onDeletePlan(
                                      item.id ?? -1,
                                      index,
                                      context,
                                    ),
                          );
                        },
                        loadMoreView: () => const VPBankLoading(),
                        itemCount: () => state.data.length,
                      ),
                    );
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget get _createPlanName {
    return BlocBuilder<ListWealthPlanCubit, ListWealthPlanState>(
      buildWhen:
          (previous, current) =>
              previous.data.length != current.data.length ||
              previous.isLoading != current.isLoading,
      builder: (context, state) {
        if (state.data.isNotEmpty || state.isLoading) {
          return const SizedBox.shrink();
        }
        return InkWell(
          onTap: () => context.push(WealthRouter.investmentGoalsPage),
          child: Padding(
            padding: const EdgeInsets.only(
              right: SizeUtils.kSize16,
              top: SizeUtils.kSize12,
              bottom: SizeUtils.kSize12,
            ),
            child: RichText(
              text: TextSpan(
                children: [
                  WidgetSpan(
                    alignment: PlaceholderAlignment.middle,
                    child: Padding(
                      padding: const EdgeInsets.only(right: SizeUtils.kSize2),
                      child: Icon(
                        Icons.add,
                        color: themeData.primary,
                        size: 17,
                      ),
                    ),
                  ),
                  TextSpan(
                    text: "Tạo mới",
                    style: vpTextStyle.body16?.copyWith(
                      color: themeData.primary,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget get _surveyToInvestmentType {
    return InkWell(
      onTap: () {
        showDialogStartSurvey(context, () {
          context
              .push(WealthRouter.surveyRequiredPage)
              .then((value) => context.read<ListWealthPlanCubit>().initial());
        });
      },
      child: Container(
        decoration: BoxDecoration(
          color: themeData.buttonDisableBg,
          border: Border(bottom: BorderSide(color: themeData.divider)),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: SizeUtils.kSize16,
            vertical: SizeUtils.kSize8,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              BlocBuilder<ListWealthPlanCubit, ListWealthPlanState>(
                buildWhen:
                    (previous, current) =>
                        previous.categorySuggest != current.categorySuggest,
                builder: (context, state) {
                  return Text(
                    'Trường phái đầu tư: ${state.categorySuggest?.invPhilosophyTypeName ?? '--'}',
                    textAlign: TextAlign.center,
                    style: vpTextStyle.body16?.copyWith(color: themeData.black),
                  );
                },
              ),
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: "Thực hiện khảo sát",
                      style: vpTextStyle.captionRegular?.copyWith(
                        color: themeData.black,
                      ),
                    ),
                    WidgetSpan(
                      alignment: PlaceholderAlignment.middle,
                      child: Padding(
                        padding: const EdgeInsets.only(
                          top: SizeUtils.kSize2,
                          left: SizeUtils.kSize4,
                        ),
                        child: Icon(
                          Icons.arrow_forward_ios,
                          color: themeData.black,
                          size: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
