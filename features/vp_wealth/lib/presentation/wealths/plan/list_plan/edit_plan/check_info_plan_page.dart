import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:vp_common/widget/vpbank_loading.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/utils/go_router_helper.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_design_system/widget/appbar/appbar_view.dart';
import 'package:vp_design_system/widget/button/vps_button.dart';
import 'package:vp_design_system/widget/scaffold/scaffold_view.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/enum/investment_frequency.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/utils/app_keyboard_utils.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/padding_extends.dart';
import 'package:vp_wealth/data/utils/wealth_constants.dart';
import 'package:vp_wealth/data/utils/wealth_notification_dialog.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/utils/place_order_utils.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/edit_plan/cubit/edit_wealth_plan_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/widget/remove_wealth_dialog.dart';
import 'package:vp_wealth/router/wealth_router.dart';

class CheckInfoPlanArguments {
  final PlanModel model;
  final EditWealthPlanCubit cubit;

  const CheckInfoPlanArguments({required this.model, required this.cubit});
}

class CheckInfoPlanPage extends StatefulWidget {
  final CheckInfoPlanArguments arg;

  const CheckInfoPlanPage({super.key, required this.arg});

  @override
  State<CheckInfoPlanPage> createState() => _CheckInfoPlanPageState();
}

class _CheckInfoPlanPageState extends State<CheckInfoPlanPage> {
  PlanModel get _planModel => widget.arg.model;

  EditWealthPlanCubit get _cubit => widget.arg.cubit;

  @override
  void initState() {
    super.initState();
    _cubit.onGetLinkContract(_cubit.state.requestIdContract ?? '');
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => AppKeyboardUtils.dismissKeyboard(),
      child: VPScaffold(
        backgroundColor: vpColor.backgroundElevation0,
        appBar: VPAppBar.flows(
          title: WealthStock.current.confirmPlan,
          leading: _close,
        ),
        body: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _targetInvestment,
                      _divider,
                      _listStockCategoryInvestment,
                      _divider,
                      // _headerContractAndTerm,
                      _contractAndTerm,
                      _divider,
                      // _actionContinue,
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        bottomNavigationBar: SizedBox(
          height: SizeUtils.kSize120 + SizeUtils.kSize40,
          child: BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
            bloc: _cubit,
            buildWhen:
                (previous, current) =>
                    previous.acceptContract != current.acceptContract,
            builder: (context, state) {
              return Column(
                children: [
                  Container(
                    padding: const EdgeInsets.only(
                      right: SizeUtils.kSize16,
                      top: SizeUtils.kSize10,
                      bottom: SizeUtils.kSize16,
                    ),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(color: themeData.divider, width: 1),
                      ),
                    ),
                    child: Row(
                      children: [
                        SizedBox(
                          height: SizeUtils.kSize24,
                          child: Checkbox(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4.0),
                            ),
                            side: MaterialStateBorderSide.resolveWith(
                              (states) => BorderSide(
                                width: 1.0,
                                color: vpColor.strokeDisable,
                              ),
                            ),
                            activeColor: vpColor.iconBrand,
                            value: state.acceptContract,
                            onChanged: (value) {
                              _cubit.acceptContract(value);
                            },
                          ),
                        ),
                        Expanded(
                          child: Text(
                            'Bằng việc ấn nút “Xác nhận”, tôi đã đọc, hiểu rõ và đồng ý với các nội dung trên.',
                            style: vpTextStyle.captionSemiBold?.copyWith(
                              color: vpColor.textPrimary,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  _divider.paddingBottom16(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      VpsButton.teriatySmall(
                        onPressed: () => context.pop(),
                        disabled: false,
                        alignment: Alignment.center,
                        title: WealthStock.current.buttonBack,
                      ),
                      VpsButton.primarySmall(
                        onPressed: () {
                          _cubit.updateChangePlan(() {
                            showSnackBar(
                              context,
                              'Cập nhật kế hoạch thành công.',
                            );
                            _cubit.onCalculateExpectedPerformance(_planModel);
                            Future.delayed(
                              const Duration(milliseconds: 300),
                            ).then((value) {
                              context.popUntilRoute(
                                WealthRouter.wealthMainPage,
                                extra: 0,
                              );
                            });
                          });
                        },
                        disabled: !state.acceptContract,
                        alignment: Alignment.center,
                        title: WealthStock.current.buttonAccept,
                      ),
                    ],
                  ).paddingHorizontal(16),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget get _targetInvestment {
    return Padding(
      padding: const EdgeInsets.all(SizeUtils.kSize16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            WealthStock.current.inforPlan,
            style: vpTextStyle.subtitle16?.copyWith(color: vpColor.textPrimary),
          ).paddingBottom12(),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              _itemRowText(
                title: WealthStock.current.target,
                value: 'Gia tăng tài sản',
              ),
              _itemRowText(
                title: 'Số tiền đầu tư ban đầu',
                value: _planModel.initialInvestment?.valueText,
              ),
              _itemRowText(
                title: WealthStock.current.periodicInvestmentAmount,
                value: _planModel.investment.valueText,
              ),
              _itemRowText(
                title: WealthStock.current.investmentTime,
                value: '${_planModel.investmentTime.toInt()} năm',
              ),
              _itemRowText(
                title: WealthStock.current.investmentFrequency,
                value: _planModel.investmentFrequency.title,
              ),
              _itemRowText(
                title: 'Ngày đầu tư định kỳ',
                value: _planModel.scheduleInvestment?.split('-').first,
              ),
              _itemRowText(
                title: 'Ngày tạo kế hoạch thành công',
                value:
                    _planModel.activeDate != null
                        ? _planModel.activeDate?.replaceAll('-', '/')
                        : '--',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget get _listStockCategoryInvestment {
    return Padding(
      padding: const EdgeInsets.all(SizeUtils.kSize16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            WealthStock.current.investmentCategory,
            style: vpTextStyle.subtitle16?.copyWith(color: vpColor.textPrimary),
          ).paddingBottom12(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex: 2,
                child: Text(
                  'Mã',
                  style: vpTextStyle.captionMedium?.copyWith(
                    color: vpColor.textTertiary,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  textAlign: TextAlign.end,
                  'Tỷ trọng',
                  style: vpTextStyle.captionMedium?.copyWith(
                    color: vpColor.textTertiary,
                  ),
                ),
              ),
              Visibility(
                visible: !(_planModel.initialInvestStatus == '3'),
                child: Expanded(
                  flex: 4,
                  child: Text(
                    'Ban đầu',
                    textAlign: TextAlign.end,
                    style: vpTextStyle.captionMedium?.copyWith(
                      color: vpColor.textTertiary,
                    ),
                  ),
                ),
              ),
              Expanded(
                flex: 4,
                child: Text(
                  'Định kỳ',
                  textAlign: TextAlign.end,
                  style: vpTextStyle.captionMedium?.copyWith(
                    color: vpColor.textTertiary,
                  ),
                ),
              ),
            ],
          ),
          Column(
            children:
                _planModel.itemList
                    .map(
                      (e) => _itemRowStockInvestment(
                        name: e.symbol,
                        rate: e.allocation.toInt(),
                        moneyInitial:
                            ((_planModel.initialInvestment ?? 0) *
                                    e.allocation ~/
                                    100)
                                .valueText,
                        moneyPeriodic:
                            (_planModel.investment * e.allocation ~/ 100)
                                .valueText,
                      ),
                    )
                    .toList(),
          ).paddingBottom8(),
          _divider,
          _itemRowStockInvestment(
            rate: _caculateTotalRate(_planModel.itemList),
            name: 'Tổng',
            isTotal: true,
            moneyInitial: (_planModel.initialInvestment ?? 0).valueText,
            moneyPeriodic: _planModel.investment.valueText,
          ).paddingBottom16(),
          Container(
            decoration: BoxDecoration(
              color: vpColor.backgroundAccentYellow.withOpacity(0.16),
              borderRadius: const BorderRadius.all(Radius.circular(8)),
            ),
            child: Row(
              children: [
                Assets.icons.dangerTriangle.svg().paddingRight8(),
                kSpacingWidth4,
                Flexible(
                  child: Text(
                    WealthStock.current.coutionInfoPlan,
                    style: vpTextStyle.captionMedium?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ),
                ),
              ],
            ).paddingSymmetric(horizontal: 12, vertical: 8),
          ).paddingTop12(),
        ],
      ),
    );
  }

  Widget get _contractAndTerm {
    return BlocBuilder<EditWealthPlanCubit, EditWealthPlanState>(
      bloc: _cubit,
      buildWhen: (previous, current) => previous.contract != current.contract,
      builder: (context, state) {
        return ExpansionTile(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Đề nghị thay đổi kế hoạch tích sản',
                style: vpTextStyle.subtitle16?.copyWith(
                  color: vpColor.textPrimary,
                ),
              ),
              if (state.contract != null)
                InkWell(
                  onTap:
                      () => _cubit.downloadContract(
                        context,
                        _cubit.state.contract,
                      ),
                  child: Assets.icons.icDownload.svg(
                    color: vpColor.iconPrimary,
                  ),
                ),
            ],
          ),
          shape: const Border(),
          tilePadding: const EdgeInsets.symmetric(
            horizontal: SizeUtils.kSize16,
          ),
          iconColor: vpColor.iconPrimary,
          collapsedIconColor: vpColor.iconPrimary,
          children: <Widget>[
            SizedBox(
              height: MediaQuery.of(context).size.height / 2,
              child: FutureBuilder<Uint8List?>(
                future: _cubit.getContractData(
                  _cubit.state.contract?.presignedUrl ?? '',
                ),
                builder: (cxt, snapshot) {
                  if (snapshot.hasData && snapshot.data != null) {
                    return SfPdfViewer.memory(
                      snapshot.data!,
                      scrollDirection: PdfScrollDirection.horizontal,
                      onDocumentLoaded: (value) {},
                      onDocumentLoadFailed: (value) {},
                      canShowScrollHead: false,
                    );
                  } else if (snapshot.hasError) {
                    return const Center(child: VPBankLoading());
                  }
                  return const SizedBox.shrink();
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _itemRowStockInvestment({
    required String name,
    required String moneyInitial,
    required String moneyPeriodic,
    bool isTotal = false,
    num rate = 0,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              name,
              style: vpTextStyle.subtitle14?.copyWith(
                color: vpColor.textPrimary,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              textAlign: TextAlign.end,
              '$rate%',
              style: vpTextStyle.body14?.copyWith(color: vpColor.textPrimary),
            ),
          ),
          _planModel.initialInvestStatus != null
              ? Visibility(
                visible:
                    !(_planModel.initialInvestStatus ==
                        WealthConstants.HAVE_INITIALMONEY),
                child: Expanded(
                  flex: 4,
                  child: Text(
                    _planModel.initialInvestStatus ==
                            WealthConstants.NOT_INITIALMONEY
                        ? '0 đ'
                        : moneyInitial,
                    textAlign: TextAlign.end,
                    style: vpTextStyle.body14?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ),
                ),
              )
              : Expanded(
                flex: 4,
                child: Text(
                  moneyInitial,
                  textAlign: TextAlign.end,
                  style: vpTextStyle.body14?.copyWith(
                    color: vpColor.textPrimary,
                  ),
                ),
              ),
          Expanded(
            flex: 4,
            child: Text(
              moneyPeriodic,
              textAlign: TextAlign.end,
              style: vpTextStyle.body14?.copyWith(color: vpColor.textPrimary),
            ),
          ),
        ],
      ),
    );
  }

  Widget _itemRowText({required String title, String? value}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: vpTextStyle.body14?.copyWith(color: vpColor.textTertiary),
        ),
        Text(
          value ?? '--',
          textAlign: TextAlign.end,
          style: vpTextStyle.subtitle14?.copyWith(color: vpColor.textPrimary),
        ),
      ],
    ).paddingBottom8();
  }

  showDialogNotRegisterSmartOTP(BuildContext context) {
    showNotifyDialog(
      allowDismiss: true,
      context: context,
      iconSize: 80,
      showCloseIcon: true,
      image: Assets.icons.icNotice.path,
      imagePadding: const EdgeInsets.only(bottom: 24),
      onPressedRight: () => context.push(WealthRouter.createPlanSuccessPage),
      title: 'Chưa đăng ký Smart OTP',
      content: 'Giao dịch này yêu cầu xác thực bằng Smart OTP',
      textButtonRight: 'Đăng ký ngay',
    );
  }

  Widget get _divider {
    return SizedBox(
      height: 2,
      width: double.infinity,
      child: ColoredBox(color: themeData.buttonDisableBg),
    );
  }

  int _caculateTotalRate(List<ItemData> listData) {
    int total = 0;
    for (var element in listData) {
      total += element.allocation.toInt();
    }
    return total;
  }

  Widget get _close {
    return InkWell(
      onTap: () => dialogClose(context),
      child: Icon(Icons.close, color: vpColor.textPrimary).paddingTop4(),
    );
  }
}
