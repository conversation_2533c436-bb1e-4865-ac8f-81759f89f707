import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/wealths/search/bloc/search_cubit.dart';

class AppSearchDataPopup<T> extends StatefulWidget {
  final SearchState<T> state;
  final Widget Function(T item) itemBuilder;
  final void Function(T item) itemOnTap;
  final double itemHeight;
  final FocusNode inputFocusNode;

  const AppSearchDataPopup({
    super.key,
    required this.state,
    required this.itemBuilder,
    required this.itemOnTap,
    this.itemHeight = SizeUtils.kSize56,
    required this.inputFocusNode,
  });

  @override
  State<AppSearchDataPopup<T>> createState() => _AppSearchDataPopupState<T>();
}

class _AppSearchDataPopupState<T> extends State<AppSearchDataPopup<T>> {
  late final FocusNode focusNode;

  @override
  void initState() {
    super.initState();
    focusNode = widget.inputFocusNode..addListener(() => setState(() {}));
  }

  List<T> get filter => widget.state.filter;

  @override
  Widget build(BuildContext context) =>
      !focusNode.hasFocus
          ? const SizedBox.shrink()
          : Flexible(
            child: LayoutBuilder(
              builder:
                  (_, constraints) => Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(SizeUtils.kSize8),
                      color: themeData.bgPopup,
                      boxShadow: [
                        BoxShadow(
                          color: themeData.boxShadow.withOpacity(0.35),
                          spreadRadius: 0,
                          blurRadius: 4.7,
                          offset: Offset.zero,
                        ),
                      ],
                    ),
                    height: filter.length * (widget.itemHeight + 1) - 1,
                    constraints: BoxConstraints(
                      maxHeight: constraints.maxHeight,
                      minHeight: widget.itemHeight,
                    ),
                    child: ListView.separated(
                      key: ValueKey(widget.state.query),
                      itemBuilder:
                          (_, index) => SizedBox(
                            height: widget.itemHeight,
                            child:
                                filter.isEmpty
                                    ? Row(
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.only(
                                            left: SizeUtils.kSize16,
                                          ),
                                          child: Text(
                                            WealthStock.current.noDataFound,
                                            style: vpTextStyle.body14?.copyWith(color: vpColor.textTertiary),
                                          ),
                                        ),
                                      ],
                                    )
                                    : GestureDetector(
                                      onTap: () {
                                        focusNode.unfocus();
                                        widget.itemOnTap(filter[index]);
                                      },
                                      behavior: HitTestBehavior.translucent,
                                      child: widget.itemBuilder(filter[index]),
                                    ),
                          ),
                      separatorBuilder: (_, __) => const DividerWidget(),
                      itemCount: filter.isEmpty ? 1 : filter.length,
                    ),
                  ),
            ),
          );
}
