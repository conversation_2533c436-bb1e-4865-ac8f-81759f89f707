import 'package:vp_core/vp_core.dart';

import '../models/request/check_right_off_register_request_model.dart';
import '../models/request/right_off_register_request_model.dart';
import '../models/right_off_path_api.dart';

part 'right_of_stock_service.g.dart';

@RestApi()
abstract class RightOfStockService {
  factory RightOfStockService(Dio dio, {String baseUrl}) = _RightOfStockService;

  @POST('/tran/accounts/accountId/checkRightOffRegister')
  Future<BaseResponse> postCheckRightOffRegister(
      @Path("accountId") String accountId,
      @Body() CheckRightOffRegisterRequestModel model);

  @POST("/tran/accounts/accountId/checkRightOffRegister")
  Future<BaseResponse> postRightOffRegister(@Path("accountId") String accountId,
      @Body() RightOffRegisterRequestModel model);

  @GET("/inq/accounts/accountId/rightOffList")
  Future<BaseResponse> getRightOffList(@Path("accountId") String accountId);

  @GET("/inq/accounts/{accountId}/ciinfo")
  Future<BaseResponse> getCiInfo(@Path("accountId") String accountId);
}
