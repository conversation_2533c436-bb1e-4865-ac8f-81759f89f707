import 'package:flutter/material.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/app_snackbar_utils.dart';
import 'package:vp_wealth/data/utils/wealth_notification_dialog.dart';
import 'package:vp_wealth/generated/assets.gen.dart' as wealthAssets;
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';

import '../../../../../../domain/wealth_repository.dart';
import '../domain/right_off_repository.dart';
import '../models/request/check_right_off_register_request_model.dart';
import '../models/request/right_off_register_request_model.dart';

part 'register_stock_right_state.dart';

class RegisterStockRightBloc extends Cubit<RegisterStockRightState> {
  RegisterStockRightBloc(int copierID, BuildContext context)
      : super(RegisterStockRightInit()) {
    _copierID = copierID;
    init(context);
  }

  bool _enable = false;

  bool get enable => _enable;

  final int _minBuy = 0;

  num? _availableTransferOnline;

  num? get availableTransferOnline => _availableTransferOnline;

  num _money = 0;

  num get money => _money;

  int _copierID = -1;

  num? volume;

  bool _loading = false;

  bool get loading => _loading;

  bool _visibleKeyVolume = false;

  bool get visibleKeyVolume => _visibleKeyVolume;

  final focusVolume = FocusNode();

  TextEditingController volumeController = TextEditingController();
  final volumeGlobalKey = GlobalKey<FormState>();
  Color? _volumeFillColor;

  Color? get volumeFillColor => _volumeFillColor;

  void init(BuildContext context) {
    getCiInfo(context);
    focusVolume.addListener(() {
      _visibleKeyVolume = focusVolume.hasFocus;
      emit(ListenFocus());
    });
  }

  void getCiInfo(BuildContext context) async {
    _loading = true;
    emit(RightOffLoading(_loading));
    await GetIt.instance
        .get<WealthRepository>()
        .cashDetail(copierId: _copierID.toString())
        .then((value) {
      _availableTransferOnline = int.parse(value.baldefovd ?? '0');
      emit(AvailableTransfer(_availableTransferOnline));
      _loading = false;
      emit(RightOffLoading(_loading));
    }).catchError((onError) {
      showError(onError);
      _loading = false;
      emit(RightOffLoading(_loading));
      emit(GetCinInfoFail());
    });
  }

  void checkRightOffRegister(
      BuildContext context, String? camastid, String accountID) async {
    _loading = true;
    emit(RightOffLoading(_loading));
    CheckRightOffRegisterRequestModel model =
        CheckRightOffRegisterRequestModel(quantity: volume, camastid: camastid);
    await GetIt.instance
        .get<RightOffRepository>()
        .postCheckRightOffRegister(accountID, model)
        .then((value) {
      _loading = false;
      emit(RightOffLoading(_loading));

      final request = RightOffRegisterRequestModel(
        authType: value.authtype,
        tokenid: value.tokenid,
        transactionId: value.transactionId,
        camastId: camastid,
        qtty: volume,
      );
      rightOffRegister(context, request, accountID);
    }).catchError((onError) {
      showError(onError);
      _loading = false;
      emit(RightOffLoading(_loading));
    });
  }

  void rightOffRegister(BuildContext context,
      RightOffRegisterRequestModel request, String accountID) async {
    _loading = true;
    emit(RightOffLoading(_loading));
    await GetIt.instance
        .get<RightOffRepository>()
        .postRightOffRegister(accountID, request)
        .then((value) {
      if (value) {
        context.pop(context);
        _loading = false;
        emit(RightOffLoading(_loading));
        showSnackBar(context, getStockLang(StockKeyLang.registeredSuccess));
      }
    }).catchError((onError) {
      showError(onError);
      _loading = false;
      emit(RightOffLoading(_loading));
    });
  }

  onChangeVolume(BuildContext context, num value, num price) {
    volume = value;
    _money = (volume ?? 0) * price;
    if (volumeGlobalKey.currentState!.validate()) {
      _volumeFillColor = null;
      if (volumeGlobalKey.currentState!.validate()) {
        _volumeFillColor = vpColor.backgroundElevationMinus1;
        _enable = true;
        emit(EnableButton(_enable));
      } else {
        _volumeFillColor = vpColor.backgroundAccentRed;
        _enable = false;
        emit(EnableButton(_enable));
      }
    } else {
      _volumeFillColor = vpColor.backgroundAccentRed;
      _enable = false;
      emit(EnableButton(_enable));
    }
  }

  String? validate(BuildContext context, int maxBuy) {
    if (volume != null) {
      if (volume! <= _minBuy) {
        return 'Số lượng tối thiểu 1';
      } else if (volume! > maxBuy) {
        return 'Vượt quá KL tối đa được mua';
      } else if (_money > (_availableTransferOnline ?? 0)) {
        return 'Số dư không đủ';
      } else {
        return null;
      }
    } else {
      return 'Vui lòng nhập số lượng';
    }
  }

  void register(BuildContext context, String? camastid, String symbol,
      String accountID, String amount) {
    showNotifyDialog(
        context: context,
        title: 'Đăng ký quyền mua',
        content:
            'Đang đăng ký quyền mua $amount CP $symbol. Quý khách có chắc chắn muốn thực hiện hành động này?',
        image: wealthAssets.Assets.icons.icStockRight.path,
        iconSize: 80,
        textButtonRight: 'Có, đăng ký',
        textButtonLeft: 'Đóng',
        textStyleLeft: vpTextStyle.body14?.copyWith(color: vpColor.textPrimary),
        onPressedLeft: () => Navigator.pop(context),
        onPressedRight: () {
          Navigator.pop(context);
          checkRightOffRegister(context, camastid, accountID);
        });
  }
}
