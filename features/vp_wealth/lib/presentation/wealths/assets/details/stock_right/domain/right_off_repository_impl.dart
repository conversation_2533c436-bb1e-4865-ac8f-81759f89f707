import 'package:dio/dio.dart';
import 'package:vp_common/error/handle_error.dart';
import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/widget/log_api/models/app_base_response.dart';
import 'package:vp_wealth/presentation/place_order/model/check_model.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/stock_right/models/right_off_path_api.dart';

import '../../../../../../data/model/assets/right_off_model.dart';
import '../models/request/check_right_off_register_request_model.dart';
import '../models/request/right_off_register_request_model.dart';
import '../models/response/ciinfo_model.dart';
import 'right_off_repository.dart';

class RightOffRepositoryImpl extends RightOffRepository {
  final Dio _restClient;
  RightOffRepositoryImpl({required Dio restClient}) : _restClient = restClient;
  @override
  Future<CiInfoModel> getCiInfo(
    String accountId, {
    String? transferType,
  }) async {
    try {
      Response response = await _restClient.get(
        RightOffPathApi.ciInfo(accountId),
      );
      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess() && result.data is List) {
        return CiInfoModel.fromJson(result.data[0]);
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<List<RightOffModel>> getRightOffList(String accountId) async {
    List<RightOffModel> list = [];
    try {
      Response response = await _restClient.get(
        RightOffPathApi.rightOffList(accountId),
      );
      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess() && result.data is List) {
        for (var jsonObj in result.data) {
          list.add(RightOffModel.fromJson(jsonObj));
        }
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return list;
  }

  @override
  Future<CheckModel> postCheckRightOffRegister(
    String accountId,
    CheckRightOffRegisterRequestModel model,
  ) async {
    try {
      Response response = await _restClient.post(
        RightOffPathApi.checkRightOffRegister(accountId),
        data: model.toJson(),
      );
      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess() && result.data != null) {
        return CheckModel.fromJson(result.data);
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<bool> postRightOffRegister(
    String accountId,
    RightOffRegisterRequestModel model,
  ) async {
    bool status = false;
    try {
      Response response = await _restClient.post(
        RightOffPathApi.rightOffRegister(accountId),
        data: model.toJson(),
      );
      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess()) {
        status = true;
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
    return status;
  }
}
