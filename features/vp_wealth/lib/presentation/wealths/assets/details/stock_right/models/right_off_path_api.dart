class RightOffPathApi {
  // //danh sách quyền mua
  // static const String rightOffList = '/inq/accounts/accountId/rightOffList';
  // //ktra giao dịch mua chứng khoán sự kiện quyền
  // static const String checkRightOffRegister = '/tran/accounts/accountId/checkRightOffRegister';
  // //giao dịch mua chứng khoán sự kiện quyền
  // static const String rightOffRegister = '/tran/accounts/accountId/rightOffRegiter';
  // //số dư tiểu khoản
  // // static ciInfo(String accountId)=> '/inq/accounts/$accountId/ciinfo';
  // static const String ciInfoPath = '/inq/accounts/{accountId}/ciinfo';

  //danh sách quyền mua
  static rightOffList(String accountId) =>
      '/flex/inq/accounts/$accountId/rightOffList';
  //ktra giao dịch mua chứng kho<PERSON> sự kiện quyền
  static checkRightOffRegister(String accountId) =>
      '/flex/tran/accounts/$accountId/checkRightOffRegister';
  //giao dịch mua chứng khoán sự kiện quyền
  static rightOffRegister(String accountId) =>
      '/flex/tran/accounts/$accountId/rightOffRegiter';
  //số dư tiểu khoản
  static ciInfo(String accountId) => '/flex/inq/accounts/$accountId/ciinfo';
}
