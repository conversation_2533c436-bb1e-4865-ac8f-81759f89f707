import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/enum/type_select_plan.dart';
import 'package:vp_wealth/data/model/assets/item_assets_model.dart';
import 'package:vp_wealth/data/utils/app_keyboard_utils.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/data/utils/padding_extends.dart';
import 'package:vp_wealth/generated/assets.gen.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/cubit/money_transfer_page/money_transfer_cubit.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/cubit/money_transfer_page/money_transfer_state.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/widget/app_spinner_widget.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/widget/list_account_widget.dart';
import 'package:vp_wealth/presentation/wealths/plan/custom_category/wealth_text_field.dart';
import 'package:vp_wealth/presentation/wealths/widgets/utils/right_suffix_money.dart';

class WealthMoneyTransferPage extends StatefulWidget {
  const WealthMoneyTransferPage({super.key, required this.model});

  final ItemAssetsModel model;

  @override
  State<WealthMoneyTransferPage> createState() =>
      _WealthMoneyTransferPageState();
}

class _WealthMoneyTransferPageState extends State<WealthMoneyTransferPage> {
  late WealthMoneyTransferCubit _cubit;
  final FocusNode _focus = FocusNode();
  bool showSuggest = false;

  @override
  void initState() {
    super.initState();
    _cubit = WealthMoneyTransferCubit()..init(itemAssetsModel: widget.model);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => _cubit,
      child: GestureDetector(
        onTap: () {
          AppKeyboardUtils.dismissKeyboard();
        },
        child: VPScaffold(
          appBar: VPAppBar.layer(title: 'Chuyển tiền'),
          // resizeToAvoidBottomInset: true,
          body: Container(
            margin: const EdgeInsets.only(top: 8),
            color: vpColor.backgroundElevation0,
            child: SafeArea(
              child: Container(
                color: vpColor.backgroundElevation0,
                child: Column(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: SizeUtils.kSize16,
                          vertical: SizeUtils.kSize16,
                        ),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  BlocBuilder<
                                    WealthMoneyTransferCubit,
                                    WealthMoneyTransferState
                                  >(
                                    buildWhen:
                                        (previous, current) =>
                                            previous.listAccountModel !=
                                                current.listAccountModel ||
                                            previous.selectedSourceAccount !=
                                                current.selectedSourceAccount ||
                                            previous.hideSubAccount !=
                                                current.hideSubAccount ||
                                            previous.isValidSelectedSourceAccount !=
                                                current
                                                    .isValidSelectedSourceAccount ||
                                            previous.selectedDestinationAccount !=
                                                current
                                                    .selectedDestinationAccount,
                                    builder: (context, state) {
                                      return AppSpinnerWidget(
                                        height: 40,
                                        title: 'Tiểu khoản/Kế hoạch chuyển',
                                        textStyle: vpTextStyle.body14?.copyWith(
                                          color:
                                              (state.selectedSourceAccount !=
                                                          null &&
                                                      (state.isValidSelectedSourceAccount ??
                                                          false))
                                                  ? vpColor.textPrimary
                                                  : vpColor.textTertiary,
                                        ),
                                        isHaveSelect: false,
                                        content:
                                            (state.selectedSourceAccount !=
                                                        null &&
                                                    (state.isValidSelectedSourceAccount ??
                                                        false))
                                                ? state
                                                    .selectedSourceAccount!
                                                    .copierInfo
                                                    .name
                                                    .trim()
                                                : 'Chọn tiểu khoản',
                                        tap: () async {
                                          await showModalBottomSheet(
                                            barrierColor:
                                                themeData.overlayBottomSheet,
                                            context: context,
                                            isScrollControlled: true,
                                            backgroundColor: Colors.transparent,
                                            builder: (context) {
                                              return ListAccountWidget(
                                                listAccountModel:
                                                    state.listAccountModel ??
                                                    [],
                                                typeSelectEnum:
                                                    TypeSelectEnum.transfer,
                                                selected:
                                                    state.selectedSourceAccount,
                                                onTap: (value) {
                                                  var data = value;
                                                  _cubit.updateTypeAccount(
                                                    accountModel: data,
                                                    typeSelectEnum:
                                                        TypeSelectEnum.transfer,
                                                  );
                                                },
                                              );
                                            },
                                          );
                                        },
                                      );
                                    },
                                  ),
                                  BlocBuilder<
                                    WealthMoneyTransferCubit,
                                    WealthMoneyTransferState
                                  >(
                                    buildWhen:
                                        (previous, current) =>
                                            previous
                                                .isValidSelectedSourceAccount !=
                                            current
                                                .isValidSelectedSourceAccount,
                                    builder: (context, state) {
                                      return (!(state
                                                  .isValidSelectedSourceAccount ??
                                              true))
                                          ? Text(
                                            'Thông tin không được để trống',
                                            style: vpTextStyle.captionRegular
                                                ?.copyWith(
                                                  color: themeData.red,
                                                ),
                                          )
                                          : const SizedBox.shrink();
                                    },
                                  ),
                                ],
                              ),
                              kSpacingHeight16,
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  BlocBuilder<
                                    WealthMoneyTransferCubit,
                                    WealthMoneyTransferState
                                  >(
                                    buildWhen:
                                        (previous, current) =>
                                            previous.listAccountModel !=
                                                current.listAccountModel ||
                                            previous.selectedDestinationAccount !=
                                                current
                                                    .selectedDestinationAccount ||
                                            previous.hideSubAccount !=
                                                current.hideSubAccount ||
                                            previous.isValidSelectedDestinationAccount !=
                                                current
                                                    .isValidSelectedDestinationAccount ||
                                            previous.selectedSourceAccount !=
                                                current.selectedSourceAccount,
                                    builder: (context, state) {
                                      return AppSpinnerWidget(
                                        height: 40,
                                        title: 'Tiểu khoản/Kế hoạch nhận',
                                        textStyle: vpTextStyle.body14?.copyWith(
                                          color:
                                              (state.selectedDestinationAccount !=
                                                          null &&
                                                      (state.isValidSelectedDestinationAccount ??
                                                          false))
                                                  ? vpColor.textPrimary
                                                  : vpColor.textTertiary,
                                        ),
                                        isHaveSelect: false,
                                        content:
                                            (state.selectedDestinationAccount !=
                                                        null &&
                                                    (state.isValidSelectedDestinationAccount ??
                                                        false))
                                                ? state
                                                    .selectedDestinationAccount!
                                                    .copierInfo
                                                    .name
                                                    .trim()
                                                : 'Chọn tiểu khoản',
                                        tap: () async {
                                          showModalBottomSheet(
                                            barrierColor:
                                                themeData.overlayBottomSheet,
                                            context: context,
                                            isScrollControlled: true,
                                            backgroundColor: Colors.transparent,
                                            builder: (context) {
                                              return ListAccountWidget(
                                                listAccountModel: _cubit
                                                    .getListAccountReceiving(
                                                      state.listAccountModel ??
                                                          [],
                                                      false,
                                                    ),
                                                typeSelectEnum:
                                                    TypeSelectEnum.receiving,
                                                selected:
                                                    state
                                                        .selectedDestinationAccount,
                                                onTap: (value) {
                                                  var data = value;
                                                  _cubit.updateTypeAccount(
                                                    accountModel: data,
                                                    typeSelectEnum:
                                                        TypeSelectEnum
                                                            .receiving,
                                                  );
                                                },
                                              );
                                            },
                                          );
                                        },
                                      );
                                    },
                                  ),
                                  // BlocBuilder<WealthMoneyTransferCubit,
                                  //     WealthMoneyTransferState>(
                                  //   buildWhen: (previous, current) =>
                                  //       previous
                                  //           .isValidSelectedDestinationAccount !=
                                  //       current.isValidSelectedDestinationAccount,
                                  //   builder: (context, state) {
                                  //     return (!(state
                                  //                 .isValidSelectedDestinationAccount ??
                                  //             true))
                                  //         ? Text(
                                  //             'Thông tin không được để trống',
                                  //             style: TextStyleUtils.text12Weight400
                                  //                 .copyWith(color: themeData.red),
                                  //           )
                                  //         : const SizedBox.shrink();
                                  //   },
                                  // )
                                ],
                              ),
                              kSpacingHeight16,
                              Text(
                                'Số tiền chuyển',
                                style: vpTextStyle.body14?.copyWith(
                                  color: themeData.black,
                                ),
                              ),
                              kSpacingHeight8,
                              Stack(
                                children: [
                                  BlocBuilder<
                                    WealthMoneyTransferCubit,
                                    WealthMoneyTransferState
                                  >(
                                    bloc: _cubit,
                                    builder: (context, state) {
                                      return Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          VPTextField.medium(
                                            caption: (color) {
                                              return Visibility(
                                                visible: state.isValid ?? false,
                                                child: Row(
                                                  children: [
                                                    Assets.icons.errorTriangle
                                                        .svg()
                                                        .paddingRight8(),
                                                    Text(
                                                      '${num.parse(state.baldefovd ?? '0') < num.parse(_cubit.textController.text.toString().replaceAll(',', '')) ? 'Vượt quá số tiền tối đa: ${num.parse(state.baldefovd ?? '0').toMoney()}' : ((state.baldefovd ?? '').isNotEmpty ? 'Số tiền tối đa: ${num.parse(state.baldefovd ?? '0').toMoney()}' : null)}',
                                                      style: vpTextStyle
                                                          .captionRegular
                                                          ?.copyWith(
                                                            color:
                                                                vpColor
                                                                    .textAccentRed,
                                                          ),
                                                    ),
                                                  ],
                                                ),
                                              );
                                            },
                                            enableInteractiveSelection: false,
                                            inputType:
                                                state.isValid ?? false
                                                    ? InputType.error
                                                    : InputType.rest,
                                            keyboardType: TextInputType.phone,
                                            focusNode: _focus,
                                            autofocus: false,
                                            maxLength: 15,
                                            hintText: '0',
                                            onChanged: (value) {
                                              _cubit.updateTransferAmount(
                                                value,
                                              );
                                            },
                                            controller: _cubit.textController,
                                          ),
                                          Visibility(
                                            visible:
                                                state.isValid == null
                                                    ? true
                                                    : !state.isValid!,
                                            child:
                                                Text(
                                                  'Số tiền tối đa: ${num.parse(state.baldefovd ?? '0').toMoney()}',
                                                  style: vpTextStyle
                                                      .captionRegular
                                                      ?.copyWith(
                                                        color:
                                                            vpColor.textPrimary,
                                                      ),
                                                ).paddingTop8(),
                                          ),
                                        ],
                                      );
                                    },
                                  ),
                                  const RightSuffixMoney(),
                                ],
                              ),
                              kSpacingHeight16,
                              Text(
                                'Nội dung chuyển (không bắt buộc)',
                                style: vpTextStyle.body14?.copyWith(
                                  color: themeData.black,
                                ),
                              ),
                              kSpacingHeight8,
                              BlocBuilder<
                                WealthMoneyTransferCubit,
                                WealthMoneyTransferState
                              >(
                                builder: (context, state) {
                                  return AppTextField(
                                    typeInput: TextInputType.text,
                                    hintText: 'Nhập nội dung chuyển khoản',
                                    maxLength: 200,
                                    enable:
                                        state
                                            .isValidSelectedDestinationAccount ??
                                        true,
                                    paddingBottom: SizeUtils.kSize16,
                                    autofocus: false,
                                    textController:
                                        _cubit.textContentController,
                                    onChanged: (value) {
                                      _cubit.updateTransferContent(value);
                                    },
                                  );
                                },
                              ),
                              kSpacingHeight16,
                            ],
                          ),
                        ),
                      ),
                    ),
                    BlocBuilder<
                      WealthMoneyTransferCubit,
                      WealthMoneyTransferState
                    >(
                      buildWhen:
                          (previous, current) =>
                              previous.enableButton != current.enableButton,
                      builder: (context, state) {
                        return Column(
                          children: [
                            const DividerWidget(),
                            Padding(
                              padding: const EdgeInsets.all(SizeUtils.kSize16),
                              child: VpsButton.primarySmall(
                                width: double.infinity,
                                disabled: !(state.enableButton ?? false),
                                textAlign: TextAlign.center,
                                title: 'Chuyển tiền',
                                onPressed: () => _cubit.sendMoney(widget.model),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
