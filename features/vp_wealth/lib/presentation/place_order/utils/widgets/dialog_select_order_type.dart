import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/utils/wealth_constants.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/assets/stock_key_assets.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/exchange.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/order.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/order_type.dart';
import 'package:vp_wealth/presentation/place_order/domain/model/model_order_type.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/widget/container_hepler.dart';

import 'item_bottom_sheet_order_type.dart';

class DialogSelectOrderType extends StatelessWidget {
  final Exchange exchangeType;
  final bool recommendDetailsOrder;
  final Order? order;

  DialogSelectOrderType({
    Key? key,
    required this.exchangeType,
    required this.recommendDetailsOrder,
    this.order,
  }) : super(key: key);
  final lo = ModelOrderType(
    orderType: OrderType.lo,
    title: WealthStock.current.orderTypeLo,
    description: WealthStock.current.orderDescriptionLo,
    icon: StockKeyAssets.orderLO,
  );
  final gtc = ModelOrderType(
    orderType: OrderType.gtc,
    title: WealthStock.current.orderTypeGtc,
    description: WealthStock.current.orderDescriptionGtc,
    icon: StockKeyAssets.orderGTC,
  );
  final waiting = ModelOrderType(
    orderType: OrderType.waiting,
    title: WealthStock.current.waitingCommand,
    description: WealthStock.current.pendingOrderCommandDescription,
    icon: StockKeyAssets.orderWaiting,
  );
  final stopLoss = ModelOrderType(
    orderType: OrderType.stopLoss,
    title: WealthStock.current.stopLoss,
    description: WealthStock.current.stopLossCommandDescription,
    icon: StockKeyAssets.orderSL,
  );
  final takeProfit = ModelOrderType(
    orderType: OrderType.takeProfit,
    title: WealthStock.current.takeProfit,
    description: WealthStock.current.takeProfitCommandDescription,
    icon: StockKeyAssets.orderTP,
  );

  List<Widget> _featuresForConditionalOrder(BuildContext context) {
    final List<ModelOrderType> orderTypeList = [];
    final List<Widget> listWidget = [];
    if (order == Order.buy) {
      orderTypeList.add(waiting);
    } else if (order == Order.sell) {
      orderTypeList
        ..add(waiting)
        ..add(takeProfit)
        ..add(stopLoss);
    }
    return listWidget..addAll(
      orderTypeList
          .map(
            (modelOrderType) => ItemBottomSheetOrderType(
              modelOrderType: modelOrderType,
              onTap: (value) => Navigator.of(context).pop(value),
            ),
          )
          .toList(),
    );
  }

  List<Widget> _listNormalOrderWidget(BuildContext context) {
    return [
      _Title(title: WealthStock.current.orderTypeLo),
      ItemBottomSheetOrderType(
        modelOrderType: lo,
        onTap: (value) => Navigator.of(context).pop(value),
      ),
    ];
  }

  List<Widget> _listConditionOrderWidget(BuildContext context) {
    final List<Widget> listWidget = [];

    return listWidget
      ..add(_Title(title: WealthStock.current.orderTypeCondition))
      ..add(
        ItemBottomSheetOrderType(
          modelOrderType: gtc,
          onTap: (value) => Navigator.of(context).pop(value),
        ),
      )
      ..addAll(_featuresForConditionalOrder(context));
  }

  @override
  Widget build(BuildContext context) {
    final paddingTop = MediaQuery.of(context).padding.top + 125.0;
    return Stack(
      children: [
        Container(
          decoration: ContainerHelper.decorationBottom(),
          padding: const EdgeInsets.symmetric(horizontal: SizeUtils.kSize24),
          margin: EdgeInsets.only(top: paddingTop),
          child: SafeArea(
            child: Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: SizeUtils.kSize40),
                  child: SingleChildScrollView(
                    child: Wrap(
                      children: [
                        ..._listNormalOrderWidget(context),
                        if (!recommendDetailsOrder)
                          ..._listConditionOrderWidget(context),
                      ],
                    ),
                  ),
                ),
                Container(
                  height: SizeUtils.kSize40,
                  alignment: Alignment.center,
                  child: SvgPicture.asset(
                    package: WealthConstants.packageName,
                    StockKeyAssets.handle,
                    colorFilter: ColorFilter.mode(
                      themeData.buttonTopBottomSheet,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        GestureDetector(
          child: Container(height: paddingTop, color: Colors.transparent),
          onTap: () => Navigator.of(context).pop(),
        ),
      ],
    );
  }
}

class _Title extends StatelessWidget {
  final String title;

  const _Title({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: SizeUtils.kSize8),
      child: Text(title, style: vpTextStyle.body16),
    );
  }
}
