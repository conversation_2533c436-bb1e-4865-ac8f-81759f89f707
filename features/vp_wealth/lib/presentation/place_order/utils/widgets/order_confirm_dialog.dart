import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_design_system/widget/button/vps_button.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/wealth_constants.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';
import 'package:vp_wealth/presentation/place_order/assets/stock_key_assets.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/gen/asset_util.dart';
import 'package:vp_wealth/presentation/place_order/utils/widgets/confirm_toggle_button.dart';

import 'order_confirm_row_title.dart';

class OrderConfirmDialog extends StatelessWidget {
  final String symbol;
  final String price;
  final String volume;
  final String value;
  final String gtcTime;
  final Function(bool showConfirmOrder) onConfirm;
  final String orderType;
  final Order order;
  final bool gtcOrder;
  final bool isNewOrder;

  const OrderConfirmDialog({
    Key? key,
    required this.price,
    required this.volume,
    required this.value,
    required this.gtcTime,
    required this.onConfirm,
    required this.orderType,
    required this.gtcOrder,
    required this.symbol,
    required this.order,
    required this.isNewOrder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    bool isBuyOrder = order == Order.buy;

    String orderIcon =
        isBuyOrder
            ? StockAssetsUtil.svgs.icOrderBuy
            : StockAssetsUtil.svgs.icOrderSell;

    Color orderColor = isBuyOrder ? themeData.primary : themeData.red;

    String orderLabel = getStockLang(
      isBuyOrder ? StockKeyLang.buy : StockKeyLang.sell,
    );

    String title = '$orderLabel $symbol';

    String buttonLabel =
        '${getStockLang(StockKeyLang.confirm)} ${orderLabel.toLowerCase()}';

    bool showConfirmOrder = true;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        isNewOrder
            ? SvgPicture.asset(orderIcon, package: WealthConstants.packageName,)
            : SvgPicture.asset(StockKeyAssets.icOrder, color: orderColor, package: WealthConstants.packageName,),
        isNewOrder
            ? kSpacingHeight16
            : const SizedBox(height: SizeUtils.kSize32),
        Text(
          title,
          style: vpTextStyle.headine6?.copyWith(
            color: Theme.of(context).textTheme.displayLarge!.color,
          ),
        ),
        const SizedBox(height: SizeUtils.kSize16),
        OrderConfirmRowTitle(
          title: WealthStock.current.typeCommand,
          content: orderType,
        ),
        const SizedBox(height: SizeUtils.kSize8),
        OrderConfirmRowTitle(
          title: WealthStock.current.price,
          content: price,
        ),
        const SizedBox(height: SizeUtils.kSize8),
        OrderConfirmRowTitle(
          title: WealthStock.current.volume,
          content: volume,
        ),
        Visibility(
          visible: gtcOrder,
          child: Padding(
            padding: const EdgeInsets.only(top: SizeUtils.kSize8),
            child: OrderConfirmRowTitle(
              title: WealthStock.current.effectiveDate,
              content: gtcTime,
            ),
          ),
        ),
        const SizedBox(height: SizeUtils.kSize8),
        OrderConfirmRowTitle(
          title: WealthStock.current.value,
          content: value,
        ),
        const SizedBox(height: SizeUtils.kSize20),
        ConfirmToggleButton(
          onChange: (showConfirm) => showConfirmOrder = showConfirm,
          showConfirm: showConfirmOrder,
        ),
        const SizedBox(height: SizeUtils.kSize20),
        Row(
          children: [
            Expanded(
              child: VpsButton.primarySmall(
                title: WealthStock.current.commandClose,
                onPressed: () => Navigator.pop(context),
              ),
            ),
            const SizedBox(width: SizeUtils.kSize8),
            Expanded(
              child: VpsButton.primarySmall(
                title: buttonLabel,
                onPressed: () {
                  Navigator.pop(context);
                  onConfirm(showConfirmOrder);
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}
