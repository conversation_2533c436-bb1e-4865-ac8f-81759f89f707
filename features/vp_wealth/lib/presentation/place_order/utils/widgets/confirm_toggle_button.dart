import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_wealth/generated/l10n.dart';

class ConfirmToggleButton extends StatefulWidget {
  final Function(bool showConfirm) onChange;
  final bool showConfirm;

  const ConfirmToggleButton({
    Key? key,
    required this.onChange,
    required this.showConfirm,
  }) : super(key: key);

  @override
  _ConfirmToggleButtonState createState() => _ConfirmToggleButtonState();
}

class _ConfirmToggleButtonState extends State<ConfirmToggleButton> {
  late bool showConfirm;

  @override
  void initState() {
    showConfirm = widget.showConfirm;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        setState(() {
          showConfirm = !showConfirm;
        });
        widget.onChange(showConfirm);
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
              child: Text(WealthStock.current.showMessageNextTime,
                  style: vpTextStyle.body14?.copyWith(
                      color: Theme.of(context).textTheme.displayLarge!.color))),
          CupertinoSwitch(
            value: showConfirm,
            activeTrackColor: themeData.primary,
            inactiveTrackColor: themeData.borderBg,
            onChanged: (_) {
              setState(() {
                showConfirm = !showConfirm;
              });
              widget.onChange(showConfirm);
            },
          ),
        ],
      ),
    );
  }
}
