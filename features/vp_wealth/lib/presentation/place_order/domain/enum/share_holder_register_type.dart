import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';

enum ShareHolderRegisterType {
  registered(
    '1',
    '<p>Quý khách <b>đã hoàn tất nghĩa vụ CBTT</b> đầy đủ và đúng hạn theo quy định của pháp luật để thực hiện giao dịch.</p>',
    "Quý khách có nghĩa vụ thực hiện CBTT và xác nhận đã hoàn tất nghĩa vụ CBTT đầy đủ và đúng hạn theo quy định của pháp luật",
  ),
  noRegister(
    '2',
    '<p><PERSON><PERSON><PERSON> khách <b>không có nghĩa vụ thực hiện nghĩa vụ CBTT</b> theo quy định của pháp luật.</p>',
    "Quý khách đã xác nhận không có nghĩa vụ thực hiện ngh<PERSON>a vụ CBTT theo quy định của pháp luật",
  ),
  notRegister(
    '3',
    '<p><PERSON><PERSON><PERSON> kh<PERSON> <b>có nghĩa vụ thực hiện CBTT nhưng chưa hoàn tất</b> nghĩa vụ CBTT đầy đủ và đúng hạn theo quy định của pháp luật.</p>',
    "Quý khách vui lòng hoàn tất nghĩa vụ CBTT đầy đủ và đúng hạn theo quy định của pháp luật để thực hiện giao dịch",
  );

  final String _type;
  final String _keyLang;
  final String _descriptionKeyLang;

  const ShareHolderRegisterType(
    this._type,
    this._keyLang,
    this._descriptionKeyLang,
  );

  factory ShareHolderRegisterType.parse(String type) =>
      values.firstWhereOrNull((e) => e._type == type) ?? registered;

  bool get canOrder => this == registered || this == noRegister;

  bool get canNotOrder => this == notRegister;

  String get title => _keyLang.tr();

  String get description => _descriptionKeyLang.tr();
}
