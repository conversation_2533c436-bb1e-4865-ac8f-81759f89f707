import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/stock_info/stock_info_cubit.dart';

import 'feature/place_order/bloc/bloc.dart';
import 'feature/place_order/old/place_order_view.dart';

class PlaceOrderPage extends StatefulWidget {
  const PlaceOrderPage({super.key});

  @override
  State<PlaceOrderPage> createState() => _PlaceOrderPageState();
}

class _PlaceOrderPageState extends State<PlaceOrderPage> {
  late final StockInfoCubit stockInfoCubit;
  late final PlaceOrderBloc placeOrderBloc;

  @override
  void initState() {
    super.initState();
    _showDialog();
    stockInfoCubit = context.read<StockInfoCubit>();
    placeOrderBloc = context.read<PlaceOrderBloc>();
  }

  void _showDialog() async {
    double version = await AppHelper().getVersion().then((version) =>
    double.tryParse(version?.trim().replaceAll(".", "") ?? '') ?? 0);
    // if (version == 300) {
    //   final newOrderConfigBloc =
    //   context.read<SetupConfigurationTransactionBloc>();
    //   final show = await newOrderConfigBloc.getShowNewOrderIntro();
    //   if (!show) return;
    //   Tutorial().showDialog(
    //     reShow: true,
    //     forInitState: false,
    //     TutorialType.newOrderIntro,
    //     content: () => const NewOrderIntroContent(),
    //     action: () =>
    //         NewOrderIntroAction(
    //           onChange: (status) {
    //             placeOrderBloc.add(ChangeNewOrderEvent(status));
    //             newOrderConfigBloc.offNewOrderIntro();
    //             newOrderConfigBloc.init().then(
    //                     (_) =>
    //                     newOrderConfigBloc.changeOrderInterfaceStatus(status));
    //           },
    //         ),
    //   );
    // }
  }

  @override
  Widget build(BuildContext context) =>
      Scaffold(
        body: SafeArea(
          child: NewOrderSelector(
                (newOrder) {
              stockInfoCubit.updatePropertySelector(
                newOrder ? newOrderPropertySelector : propertySelector,
              );
              return const PlaceOrderView();
            },
          ),
        ),
      );
}
