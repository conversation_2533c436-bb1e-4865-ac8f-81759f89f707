import 'package:flutter/services.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/focus_keyboard.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/order.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/order_type.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/valid_volume_status.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/condition/condition_command_util.dart';
import 'package:vp_wealth/presentation/place_order/utils/place_order_utils.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/data/stock_helper.dart';

import 'base_state.dart';

class VolumeInputState extends InputFieldState {
  final num? volume;
  final num maxVolume;
  final num trade;
  final OrderType orderType;
  final Order order;
  final FocusKeyboard focusKeyboard;

  const VolumeInputState({
    this.volume,
    required this.maxVolume,
    required this.trade,
    required this.orderType,
    required this.order,
    required bool highlight,
    required this.focusKeyboard,
  }) : super(highlight: highlight);

  VolumeInputState copyWith({
    num? volume,
    num? maxVolume,
    num? trade,
    OrderType? orderType,
    FocusKeyboard? focusKeyboard,
    Order? order,
    required bool clearVolume,
    required bool highlight,
  }) => VolumeInputState(
    volume: clearVolume ? null : volume ?? this.volume,
    maxVolume: maxVolume ?? this.maxVolume,
    trade: trade ?? this.trade,
    orderType: orderType ?? this.orderType,
    focusKeyboard: focusKeyboard ?? this.focusKeyboard,
    order: order ?? this.order,
    highlight: highlight,
  );

  bool get _isLoOrder => orderType == OrderType.lo;

  bool get _isGtcOrder => orderType == OrderType.gtc;

  String get maxVolumeText => maxVolume.toMoney(showSymbol: false);

  String get tradeText => trade.toMoney(showSymbol: false);

  num get volumeNotNull => volume ?? 0;

  ValidVolumeStatus get _volumeStatus {
    if (volume == null) return ValidVolumeStatus.init;
    if (volume == 0) return ValidVolumeStatus.invalid;
    if (maxVolume == 0 && order == Order.buy) {
      return ValidVolumeStatus.invalidBuy;
    }
    if (maxVolume == 0 && order == Order.sell) {
      return ValidVolumeStatus.invalidSell;
    }
    if (volumeNotNull < 100 && !_isLoOrder) {
      return ValidVolumeStatus.invalidOddLot;
    }
    if (volumeNotNull > 99 && volumeNotNull % 100.0 != 0.0) {
      return ValidVolumeStatus.invalid;
    }
    if (volumeNotNull > maxVolume && !_isGtcOrder) {
      return ValidVolumeStatus.invalidMax;
    }
    return ValidVolumeStatus.done;
  }

  num decreaseVolume() {
    final step =
        _isLoOrder && volumeNotNull > 0 && volumeNotNull <= 100 ? 1.0 : 100.0;
    return ConditionCommandUtil.updateValue(
      false,
      volumeNotNull,
      step,
    ).toDouble();
  }

  num increaseVolume() {
    if (_isLoOrder && volumeNotNull == 0) {
      if (maxVolume > 0 && maxVolume < 100) {
        return maxVolume;
      }
      return volumeNotNull + 100;
    }
    final step = _isLoOrder && NumExts(volumeNotNull).isOddLot ? 1 : 100;
    return ConditionCommandUtil.updateValue(
      true,
      volumeNotNull,
      step,
    ).toDouble();
  }

  List<String> get listSuggest => {
    _roundVolume(1, maxVolume * 0.2): '(20%)',
    _roundVolume(2, maxVolume * 0.5): '(50%)',
    _roundVolume(3, maxVolume * 1.0): '(100%)',
  }.toList((entry) {
    if (entry.key <= 0 || (!_isLoOrder && entry.key < 100)) {
      return '-';
    }
    return '${StockHelper.formatSuggestVol(entry.key)} ${entry.value}';
  });

  int _roundVolume(int index, double volume) {
    if (volume < 100.0) {
      return volume.round().toInt();
    }
    final evenDownRound = (volume / 100.0).floor() * 100.0;
    return evenDownRound.toInt();
  }

  @override
  List<TextInputFormatter> get inputFormatter => [
    FilteringTextInputFormatter.deny(RegExp(r'\D$')),
    TextInputFormatter.withFunction((oldValue, newValue) {
      final volumeFormat = newValue.text.volume.toMoney(showSymbol: false);
      return volumeFormat.length > maxLength ? oldValue : newValue;
    }),
  ];

  @override
  int get maxLength => 12;

  @override
  String get text =>
      volume == null ? '' : volumeNotNull.toMoney(showSymbol: false);

  @override
  Map<String, bool> get error {
    switch (_volumeStatus) {
      case ValidVolumeStatus.invalid:
        return {StockKeyLang.validateVolume: true};
      case ValidVolumeStatus.invalidMax:
        return {
          WealthStock.current.invalidMaxVolume: true,
          maxVolumeText: false,
        };
      case ValidVolumeStatus.invalidBuy:
        return {WealthStock.current.invalidMaxBuy: true};
      case ValidVolumeStatus.invalidSell:
        return {WealthStock.current.invalidMaxSell: true};
      case ValidVolumeStatus.invalidOddLot:
        return {WealthStock.current.invalidOddLot: true};
      default:
        return {'': false};
    }
  }

  @override
  String get hintLang => WealthStock.current.volumeKL;

  @override
  bool get validator {
    switch (_volumeStatus) {
      case ValidVolumeStatus.invalid:
      case ValidVolumeStatus.invalidMax:
      case ValidVolumeStatus.invalidBuy:
      case ValidVolumeStatus.invalidSell:
      case ValidVolumeStatus.invalidOddLot:
        return false;
      default:
        return true;
    }
  }

  @override
  bool get isDone => _volumeStatus == ValidVolumeStatus.done;

  @override
  bool get focus => focusKeyboard == FocusKeyboard.volume;

  @override
  String toString() {
    return '${runtimeType.toString()}(text: $text, '
        'error: $error, '
        'validator: $validator, '
        'isDone: $isDone, '
        'focus: $focus, '
        'enable: $enable, '
        'listSuggest: $listSuggest, '
        'maxVolume: $maxVolume, '
        'highlight: $highlight)';
  }
}
