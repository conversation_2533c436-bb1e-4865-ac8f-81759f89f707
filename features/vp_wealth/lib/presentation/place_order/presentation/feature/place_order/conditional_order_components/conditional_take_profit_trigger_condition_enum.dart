import 'package:vp_wealth/generated/l10n.dart';

import '../../../../domain/enum/order_type.dart';

enum ConditionalTakeProfitTriggerConditionEnum { rateProfit, slippageProfit }

extension ConditionalTakeProfitTriggerConditionEnumExtension
    on ConditionalTakeProfitTriggerConditionEnum {
  String _takeProfitToString() {
    switch (this) {
      case ConditionalTakeProfitTriggerConditionEnum.rateProfit:
        return WealthStock.current.takeProfitRatePercent;
      case ConditionalTakeProfitTriggerConditionEnum.slippageProfit:
        return WealthStock.current.profitMargin;
      default:
        return WealthStock.current.autoAdjustTriggerPriceAndSetPrice;
    }
  }

  String _stopLossToString() {
    switch (this) {
      case ConditionalTakeProfitTriggerConditionEnum.rateProfit:
        return WealthStock.current.stopLossRatePercent;
      case ConditionalTakeProfitTriggerConditionEnum.slippageProfit:
        return WealthStock.current.stopLossMargin;
      default:
        return WealthStock.current.autoAdjustTriggerPriceAndSetPrice;
    }
  }

  String toStringShowDialog({OrderType? orderType = OrderType.takeProfit}) {
    if (orderType == OrderType.takeProfit) {
      return _takeProfitToString();
    } else if (orderType == OrderType.stopLoss) {
      return _stopLossToString();
    } else {
      return '';
    }
  }
}
