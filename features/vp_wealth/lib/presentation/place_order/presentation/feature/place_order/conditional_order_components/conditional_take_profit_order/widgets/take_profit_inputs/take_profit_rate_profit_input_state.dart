part of 'take_profit_rate_profit_input_cubit.dart';

class TakeProfitRateProfitInputState extends PriceInputState {
  TakeProfitRateProfitInputState({
    required bool formatSlippage,
    required String priceNotFormat,
    required OrderType orderType,
    required Exchange exchange,
    required bool highlight,
    required FocusKeyboard focusKeyboard,
    double? slippage,
    double? ceilingPrice,
    double? floorPrice,
    required this.takeProfitFocusKeyboard,
    this.triggerCondition =
        ConditionalTakeProfitTriggerConditionEnum.rateProfit,
    required this.orderPrice,
  }) : super(
          formatPrice: formatSlippage,
          priceNotFormat: priceNotFormat,
          orderType: orderType,
          exchange: exchange,
          highlight: highlight,
          focusKeyboard: focusKeyboard,
          price: slippage,
          ceilingPrice: ceilingPrice,
          floorPrice: floorPrice,
        );

  final ConditionalTakeProfitFocusKeyboard takeProfitFocusKeyboard;
  final ConditionalTakeProfitTriggerConditionEnum triggerCondition;
  final num orderPrice;

  @override
  TakeProfitRateProfitInputState copyWith({
    double? price,
    double? ceilingPrice,
    double? floorPrice,
    bool? formatPrice,
    String? priceNotFormat,
    OrderType? orderType,
    Exchange? exchange,
    bool clearPrice = false,
    FocusKeyboard? focusKeyboard,
    bool highlight = false,
    ConditionalTakeProfitFocusKeyboard? takeProfitFocusKeyboard,
    ConditionalTakeProfitTriggerConditionEnum? triggerCondition,
    num? orderPrice,
  }) =>
      TakeProfitRateProfitInputState(
        slippage: clearPrice ? null : price ?? this.price,
        ceilingPrice: ceilingPrice ?? this.ceilingPrice,
        floorPrice: floorPrice ?? this.floorPrice,
        formatSlippage: formatPrice ?? this.formatPrice,
        priceNotFormat: priceNotFormat ?? this.priceNotFormat,
        orderType: orderType ?? this.orderType,
        exchange: exchange ?? this.exchange,
        highlight: highlight,
        focusKeyboard: focusKeyboard ?? this.focusKeyboard,
        takeProfitFocusKeyboard:
            takeProfitFocusKeyboard ?? this.takeProfitFocusKeyboard,
        triggerCondition: triggerCondition ?? this.triggerCondition,
        orderPrice: orderPrice ?? this.orderPrice,
      );

  factory TakeProfitRateProfitInputState.init() {
    return TakeProfitRateProfitInputState(
      formatSlippage: true,
      priceNotFormat: '',
      orderType: OrderType.takeProfit,
      exchange: Exchange.hsx,
      focusKeyboard: FocusKeyboard.none,
      highlight: false,
      takeProfitFocusKeyboard: ConditionalTakeProfitFocusKeyboard.none,
      orderPrice: 0,
    );
  }

  double _getStepPrice() {
    if (triggerCondition ==
        ConditionalTakeProfitTriggerConditionEnum.rateProfit) {
      return 1000;
    } else {
      return 100;
    }
  }

  double get surPlus => priceNotNull % _getStepPrice();

  double _rateDecreasePrice() {
    // if (priceNotNull == 0.0 || priceNotNull < 0) return 0.0;
    final value = priceNotNull - _getStepPrice();

    return value;
  }

  double _rateIncreasePrice() {
    // if (priceNotNull == 0.0 || priceNotNull < 0) return 0.0;

    return priceNotNull + _getStepPrice();
  }

  double _slippageDecreasePrice() {
    if (priceNotNull <= _getStepPrice()) return 0;

    return priceNotNull - _getStepPrice();
  }

  double _slippageIncreasePrice() {
    // if (priceNotNull == 0.0 || priceNotNull < 0) return 0.0;

    return priceNotNull + _getStepPrice();
  }

  bool get isRateProfit =>
      triggerCondition == ConditionalTakeProfitTriggerConditionEnum.rateProfit;

  @override
  double decreasePrice() {
    if (isRateProfit) {
      return _rateDecreasePrice();
    } else {
      return _slippageDecreasePrice();
    }
  }

  @override
  double increasePrice() {
    print('TriggerCondition: $triggerCondition');
    print('orderPrice: $orderPrice');
    if (isRateProfit) {
      return _rateIncreasePrice();
    } else {
      return _slippageIncreasePrice();
    }
  }

  @override
  bool get validator {
    switch (_priceStatus) {
      case ValidPriceStatus.outOfRange:
        return false;
      default:
        return true;
    }
  }

  @override
  String get hintLang => '';

  @override
  bool get focus =>
      takeProfitFocusKeyboard ==
      ConditionalTakeProfitFocusKeyboard.slippageProfit;

  @override
  bool get isDone => _priceStatus == ValidPriceStatus.done;

  double get rateProfit => priceNotNull / 1000;

  @override
  Map<String, bool> get error =>
      isInit ? {'': false} : {_getErrorPriceKeyLang: true};

  String get _getErrorPriceKeyLang {
    return WealthStock.current.invalidRateProfit;
  }

  ValidPriceStatus get _priceStatus {
    if (price == null) return ValidPriceStatus.init;
    if (orderPrice <= 0) {
      return ValidPriceStatus.outOfRange;
    }
    if (isRateProfit) {
      if (rateProfit <= 0.0 || rateProfit > 300) {
        return ValidPriceStatus.outOfRange;
      } else {
        return ValidPriceStatus.done;
      }
    } else {
      if (rateProfit <= 0) {
        return ValidPriceStatus.outOfRange;
      }

      return ValidPriceStatus.done;
    }
  }

  @override
  List<Object?> get props => [
        text,
        error.toString(),
        validator,
        isDone,
        focus,
        enable,
        highlight,
        triggerCondition,
        orderPrice,
      ];

  String get priceWhenFormatted => priceNotNull.getPriceFormatted(
        convertToThousand: true,
      );

  String _getPrice() {
    if (price == null) {
      return '';
    } else {
      if (formatPrice) {
        return priceWhenFormatted;
      } else {
        return priceNotFormat;
      }
    }
  }

  @override
  String get text => _getPrice();
}
