import 'package:flutter/services.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/state/base_state.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_focus_keyboard.dart';
import 'package:vp_wealth/presentation/place_order/utils/place_order_utils.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/data/stock_helper.dart';

class RateProfitState extends InputFieldState {
  final num? rateProfit;
  final num maxVolume;
  final OrderType orderType;
  final Order order;
  final ConditionalTakeProfitFocusKeyboard takeProfitFocusKeyboard;

  const RateProfitState({
    this.rateProfit,
    required this.maxVolume,
    required this.orderType,
    required this.order,
    required bool highlight,
    required this.takeProfitFocusKeyboard,
  }) : super(highlight: highlight);

  RateProfitState copyWith({
    num? rateProfit,
    num? maxVolume,
    OrderType? orderType,
    ConditionalTakeProfitFocusKeyboard? takeProfitFocusKeyboard,
    Order? order,
    required bool clearVolume,
    required bool highlight,
  }) =>
      RateProfitState(
        rateProfit: clearVolume ? null : rateProfit ?? this.rateProfit,
        maxVolume: maxVolume ?? this.maxVolume,
        orderType: orderType ?? this.orderType,
        takeProfitFocusKeyboard:
            takeProfitFocusKeyboard ?? this.takeProfitFocusKeyboard,
        order: order ?? this.order,
        highlight: highlight,
      );

  bool get _isLoOrder => orderType == OrderType.lo;

  bool get _isGtcOrder => orderType == OrderType.gtc;

  String get maxVolumeText => maxVolume.toMoney(showSymbol: false);

  num get volumeNotNull => rateProfit ?? 0;

  ValidVolumeStatus get _volumeStatus {
    if (rateProfit == null) return ValidVolumeStatus.init;
    if (rateProfit == 0) return ValidVolumeStatus.invalid;
    // if (maxVolume == 0 && order == Order.buy) {
    //   return ValidVolumeStatus.invalidBuy;
    // }
    // if (maxVolume == 0 && order == Order.sell) {
    //   return ValidVolumeStatus.invalidSell;
    // }
    // if (volumeNotNull < 100 && !_isLoOrder) {
    //   return ValidVolumeStatus.invalidOddLot;
    // }
    // if (volumeNotNull > 99 && volumeNotNull % 100.0 != 0.0) {
    //   return ValidVolumeStatus.invalid;
    // }
    // if (volumeNotNull > maxVolume && !_isGtcOrder) {
    //   return ValidVolumeStatus.invalidMax;
    // }
    return ValidVolumeStatus.done;
  }

  num decreaseVolume() {
    if (volumeNotNull == 0) {
      return 0;
    }

    return volumeNotNull - 10;
  }

  num increaseVolume() {
    return volumeNotNull + 10;
  }

  List<String> get listSuggest => {
        _roundVolume(1, maxVolume * 0.2): '(20%)',
        _roundVolume(2, maxVolume * 0.5): '(50%)',
        _roundVolume(3, maxVolume * 1.0): '(100%)',
      }.toList(
        (entry) {
          if (!_isLoOrder && entry.key < 100) {
            return '-';
          }
          return '${StockHelper.formatVol(entry.key)} ${entry.value}';
        },
      );

  int _roundVolume(int index, double volume) {
    if (volume < 100.0) {
      return volume.round().toInt();
    }
    final evenDownRound = (volume / 100.0).floor() * 100.0;
    return evenDownRound.toInt();
  }

  @override
  List<TextInputFormatter> get inputFormatter => [
        FilteringTextInputFormatter.deny(RegExp(r'\D$')),
        TextInputFormatter.withFunction(
          (oldValue, newValue) {
            final volumeFormat =
                newValue.text.volume.toMoney(showSymbol: false);
            return volumeFormat.length > maxLength ? oldValue : newValue;
          },
        )
      ];

  @override
  int get maxLength => 12;

  @override
  String get text =>
      rateProfit == null ? '' : volumeNotNull.toMoney(showSymbol: false);

  @override
  Map<String, bool> get error {
    switch (_volumeStatus) {
      case ValidVolumeStatus.invalid:
        return {getStockLang(StockKeyLang.validateVolume): true};
      case ValidVolumeStatus.invalidMax:
        return {
          WealthStock.current.invalidMaxVolume: true,
          maxVolumeText: false
        };
      case ValidVolumeStatus.invalidBuy:
        return {WealthStock.current.invalidMaxBuy: true};
      case ValidVolumeStatus.invalidSell:
        return {WealthStock.current.invalidMaxSell: true};
      case ValidVolumeStatus.invalidOddLot:
        return {WealthStock.current.invalidOddLot: true};
      default:
        return {'': false};
    }
  }

  @override
  String get hintLang => '';

  @override
  bool get validator {
    switch (_volumeStatus) {
      case ValidVolumeStatus.invalid:
      case ValidVolumeStatus.invalidMax:
      case ValidVolumeStatus.invalidBuy:
      case ValidVolumeStatus.invalidSell:
      case ValidVolumeStatus.invalidOddLot:
        return false;
      default:
        return true;
    }
  }

  @override
  bool get isDone => _volumeStatus == ValidVolumeStatus.done;

  @override
  bool get focus =>
      takeProfitFocusKeyboard == ConditionalTakeProfitFocusKeyboard.rateProfit;

  @override
  List<Object?> get props => [
        text,
        error.toString(),
        validator,
        isDone,
        focus,
        enable,
        listSuggest,
        maxVolume,
        highlight,
      ];
}
