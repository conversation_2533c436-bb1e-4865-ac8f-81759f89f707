import 'package:flutter/services.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/exchange.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/condition/condition_command_util.dart';
import 'package:vp_wealth/presentation/place_order/utils/place_order_utils.dart';

import '../place_order_state.dart';
import 'base_state.dart';

class PriceInputState extends InputFieldState {
  final double? price;
  final double? ceilingPrice;
  final double? floorPrice;

  /// formatPrice: default is true for show text label is price formatted variable,
  /// and false for show text label is priceNotFormat variable
  final bool formatPrice;
  final String priceNotFormat;
  final OrderType orderType;
  final Exchange exchange;
  final FocusKeyboard focusKeyboard;
  String? stockType;

  PriceInputState({
    this.price,
    this.ceilingPrice,
    this.floorPrice,
    required this.formatPrice,
    required this.priceNotFormat,
    required this.orderType,
    required this.exchange,
    required bool highlight,
    required this.focusKeyboard,
    this.stockType,
  }) : super(highlight: highlight);

  PriceInputState copyWith({
    double? price,
    double? ceilingPrice,
    double? floorPrice,
    bool? formatPrice,
    String? priceNotFormat,
    OrderType? orderType,
    Exchange? exchange,
    required bool clearPrice,
    FocusKeyboard? focusKeyboard,
    required bool highlight,
  }) => PriceInputState(
    price: clearPrice ? null : price ?? this.price,
    ceilingPrice: ceilingPrice ?? this.ceilingPrice,
    floorPrice: floorPrice ?? this.floorPrice,
    formatPrice: formatPrice ?? this.formatPrice,
    priceNotFormat: priceNotFormat ?? this.priceNotFormat,
    orderType: orderType ?? this.orderType,
    exchange: exchange ?? this.exchange,
    highlight: highlight,
    focusKeyboard: focusKeyboard ?? this.focusKeyboard,
    stockType: stockType,
  );

  bool get _isLoOrder => orderType == OrderType.lo;

  bool get _isLoOrGtcOrder =>
      _isLoOrder || orderType == OrderType.gtc || _isConditionalOrder;

  bool get _isConditionalOrder => conditionalOrderTypes.contains(orderType);

  double get priceNotNull => price ?? 0.0;

  String get _getErrorPriceKeyLang {
    switch (_priceStatus) {
      case ValidPriceStatus.step10Invalid:
        return WealthStock.current.invalidStep10;
      case ValidPriceStatus.step50Invalid:
        return WealthStock.current.invalidStep50;
      case ValidPriceStatus.step100Invalid:
        return WealthStock.current.invalidStep100;
      default:
        return WealthStock.current.invalidOutOfRange;
    }
  }

  ValidPriceStatus get _priceStatus {
    if (!_isLoOrGtcOrder) return ValidPriceStatus.done;
    if (price == null) return ValidPriceStatus.init;
    if (price == 0.0) return ValidPriceStatus.outOfRange;
    if (_isLoOrder &&
        (price! > (ceilingPrice ?? 0.0) || price! < (floorPrice ?? 0.0))) {
      return ValidPriceStatus.outOfRange;
    }
    // valid price step
    if (stockType.isCW) {
      return priceNotNull % 10.0 != 0.0
          ? ValidPriceStatus.step10Invalid
          : ValidPriceStatus.done;
    }

    if (stockType.isETF) {
      return priceNotNull % (exchange.isHose ? 10.0 : 1.0) != 0.0
          ? ValidPriceStatus.step10Invalid
          : ValidPriceStatus.done;
    }
    if (exchange.isHose) {
      if (priceNotNull < 10000.0 && priceNotNull % 10.0 != 0.0) {
        return ValidPriceStatus.step10Invalid;
      }
      if (priceNotNull >= 10000.0 &&
          priceNotNull < 49950.0 &&
          priceNotNull % 50.0 != 0.0) {
        return ValidPriceStatus.step50Invalid;
      }
      if (priceNotNull > 50000.0 && priceNotNull % 100.0 != 0.0) {
        return ValidPriceStatus.step100Invalid;
      }
    } else {
      if (priceNotNull % 100.0 != 0.0) {
        return ValidPriceStatus.step100Invalid;
      }
    }
    return ValidPriceStatus.done;
  }

  double decreasePrice() {
    if (priceNotNull == 0.0) return 0.0;
    if (!exchange.isHose) {
      return ConditionCommandUtil.updateValue(
        false,
        priceNotNull,
        100.0,
      ).toDouble();
    }
    if (priceNotNull <= 10000.0) {
      return ConditionCommandUtil.updateValue(
        false,
        priceNotNull,
        10.0,
      ).toDouble();
    } else if (priceNotNull > 10000.0 && priceNotNull <= 50000.0) {
      return ConditionCommandUtil.updateValue(
        false,
        priceNotNull,
        50.0,
      ).toDouble();
    }
    return ConditionCommandUtil.updateValue(
      false,
      priceNotNull,
      100.0,
    ).toDouble();
  }

  double increasePrice() {
    if (!exchange.isHose) {
      return ConditionCommandUtil.updateValue(
        true,
        priceNotNull,
        100.0,
      ).toDouble();
    }
    if (priceNotNull < 10000.0) {
      return ConditionCommandUtil.updateValue(
        true,
        priceNotNull,
        10.0,
      ).toDouble();
    } else if (priceNotNull >= 10000.0 && priceNotNull < 50000.0) {
      return ConditionCommandUtil.updateValue(
        true,
        priceNotNull,
        50.0,
      ).toDouble();
    }
    return ConditionCommandUtil.updateValue(
      true,
      priceNotNull,
      100.0,
    ).toDouble();
  }

  @override
  int get maxLength => 8;

  @override
  String get text =>
      _isLoOrGtcOrder
          ? price == null
              ? ''
              : formatPrice
              ? priceNotNull.getPriceFormatted(convertToThousand: true)
              : priceNotFormat
          : orderType.name.toUpperCase();

  @override
  String get hintLang => StockKeyLang.price;

  @override
  Map<String, bool> get error =>
      isInit ? {'': false} : {_getErrorPriceKeyLang: true};

  @override
  bool get enable => _isLoOrGtcOrder;

  @override
  bool get validator {
    switch (_priceStatus) {
      case ValidPriceStatus.outOfRange:
      case ValidPriceStatus.step10Invalid:
      case ValidPriceStatus.step50Invalid:
      case ValidPriceStatus.step100Invalid:
        return false;
      default:
        return true;
    }
  }

  @override
  bool get isDone => _priceStatus == ValidPriceStatus.done;

  @override
  bool get focus => focusKeyboard == FocusKeyboard.price;

  bool get isInit => _priceStatus == ValidPriceStatus.init;

  @override
  String toString() {
    return '${runtimeType.toString()}(text: $text, '
        'error: $error, '
        'validator: $validator, '
        'isDone: $isDone, '
        'focus: $focus, '
        'enable: $enable, '
        'highlight: $highlight)';
  }

  @override
  List<TextInputFormatter> get inputFormatter => [
    FilteringTextInputFormatter.deny(RegExp(r'^([.,])')),
    FilteringTextInputFormatter.allow(RegExp(r'^[\d,]+([.,])?\d{0,2}')),
    FilteringTextInputFormatter.deny(RegExp(r',$'), replacementString: '.'),
    FilteringTextInputFormatter.deny(
      RegExp(r',(?=\d+)'),
      replacementString: '',
    ),
  ];
}
