part of 'take_profit_slippage_input_cubit.dart';

// @immutable
// abstract class TakeProfitSlippageInputState {}
//
// class TakeProfitSlippageInputInitial extends TakeProfitSlippageInputState {}
class TakeProfitSlippageInputState extends PriceInputState {
  TakeProfitSlippageInputState({
    required bool formatSlippage,
    required String priceNotFormat,
    required OrderType orderType,
    required Exchange exchange,
    required bool highlight,
    required FocusKeyboard focusKeyboard,
    double? slippage,
    double? ceilingPrice,
    double? floorPrice,
    required this.takeProfitFocusKeyboard,
    required this.orderPrice,
  }) : super(
          formatPrice: formatSlippage,
          priceNotFormat: priceNotFormat,
          orderType: orderType,
          exchange: exchange,
          highlight: highlight,
          focusKeyboard: focusKeyboard,
          price: slippage,
          ceilingPrice: ceilingPrice,
          floorPrice: floorPrice,
        );

  final ConditionalTakeProfitFocusKeyboard takeProfitFocusKeyboard;
  final num orderPrice;

  @override
  TakeProfitSlippageInputState copyWith({
    double? price,
    double? ceilingPrice,
    double? floorPrice,
    bool? formatPrice,
    String? priceNotFormat,
    OrderType? orderType,
    Exchange? exchange,
    bool clearPrice = false,
    FocusKeyboard? focusKeyboard,
    bool highlight = false,
    ConditionalTakeProfitFocusKeyboard? takeProfitFocusKeyboard,
    num? orderPrice,
  }) =>
      TakeProfitSlippageInputState(
        slippage: clearPrice ? null : price ?? this.price,
        ceilingPrice: ceilingPrice ?? this.ceilingPrice,
        floorPrice: floorPrice ?? this.floorPrice,
        formatSlippage: formatPrice ?? this.formatPrice,
        priceNotFormat: priceNotFormat ?? this.priceNotFormat,
        orderType: orderType ?? this.orderType,
        exchange: exchange ?? this.exchange,
        highlight: highlight,
        focusKeyboard: focusKeyboard ?? this.focusKeyboard,
        takeProfitFocusKeyboard:
            takeProfitFocusKeyboard ?? this.takeProfitFocusKeyboard,
        orderPrice: orderPrice ?? this.orderPrice,
      );

  factory TakeProfitSlippageInputState.init() {
    return TakeProfitSlippageInputState(
      formatSlippage: true,
      priceNotFormat: '',
      orderType: OrderType.takeProfit,
      exchange: Exchange.hsx,
      focusKeyboard: FocusKeyboard.none,
      highlight: false,
      takeProfitFocusKeyboard: ConditionalTakeProfitFocusKeyboard.none,
      orderPrice: 0,
    );
  }

  double get priceStep => 100;

  double get surPlus => priceNotNull % priceStep;

  @override
  double decreasePrice() {
    // if (priceNotNull == 0.0) return 0.0;

    return priceNotNull - 100;
  }

  @override
  double increasePrice() {
    return priceNotNull + 100;
  }

  ValidPriceStatus get _priceStatus {
    if (price == null) return ValidPriceStatus.init;
    if ((price ?? 0) < 0.0) return ValidPriceStatus.outOfRange;
    if (orderPrice <= 0) {
      return ValidPriceStatus.outOfRange;
    }
    return ValidPriceStatus.done;
  }

  String get _getErrorPriceKeyLang {
    switch (_priceStatus) {
      case ValidPriceStatus.outOfRange:
        return WealthStock.current.priceSlippageInvalid;
      default:
        return WealthStock.current.priceSlippageInvalid;
    }
  }

  @override
  Map<String, bool> get error =>
      isInit ? {'': false} : {_getErrorPriceKeyLang: true};

  @override
  bool get validator {
    switch (_priceStatus) {
      case ValidPriceStatus.outOfRange:
        return false;
      default:
        return true;
    }
  }

  @override
  String get hintLang => '';

  @override
  bool get focus =>
      takeProfitFocusKeyboard ==
      ConditionalTakeProfitFocusKeyboard.slippageProfit;

  @override
  bool get isDone => priceNotNull != 0;

  @override
  List<TextInputFormatter> get inputFormatter => [
        FilteringTextInputFormatter.deny(RegExp(r'^(\.|\,)')),
        FilteringTextInputFormatter.allow(RegExp(r'^\d+(\.|\,)?\d{0,2}')),
        FilteringTextInputFormatter.deny(',', replacementString: '.'),
      ];
}
