import 'package:easy_localization/easy_localization.dart';
import 'package:intl/intl.dart';
import 'package:vp_common/utils/app_number_format_utils.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/condition_command_const.dart';
import 'package:vp_wealth/presentation/wealths/order/command_history/condition_command_enum.dart';

class ConditionCommandUtil {
  static String mapConditionTypeToText(String? type) {
    if (type == 'SEO') {
      return StockKeyLang.conditionTypeSeo;
    }
    if (type == 'TPO') {
      return StockKeyLang.conditionTypeTpo;
    }
    return StockKeyLang.conditionTypeSlo;
  }

  static String mapOrderStatusToText(String? type) {
    switch (type) {
      case ConditionCommandConst.orderStatusInActivated:
        return StockKeyLang.orderStatusInActivated;
      case ConditionCommandConst.orderStatusActivated:
        return StockKeyLang.orderStatusActivated;
      case ConditionCommandConst.orderStatusCanceled:
        return StockKeyLang.orderStatusCanceled;
      default:
        return StockKeyLang.orderStatusExpired;
    }
  }

  static String mapConditionTypeEnumToText(ConditionCommandTypeEnum type) {
    if (type == ConditionCommandTypeEnum.seo) {
      return StockKeyLang.conditionTypeSeo;
    }
    if (type == ConditionCommandTypeEnum.tpo) {
      return StockKeyLang.conditionTypeTpo;
    }
    return StockKeyLang.conditionTypeSlo;
  }

  static String mapConditionTypeAccountEnumToText(
    ConditionCommandSubAccountEnum type,
  ) {
    if (type == ConditionCommandSubAccountEnum.normal) {
      return WealthStock.current.ordinaryFull;
    }
    if (type == ConditionCommandSubAccountEnum.margin) {
      return WealthStock.current.depositFull;
    }
    return getStockLang(StockKeyLang.all);
  }

  static String mapTransactionTypeEnumToText(
    ConditionCommandTransactionTypeEnum? type,
  ) {
    if (type == ConditionCommandTransactionTypeEnum.buy) {
      return StockKeyLang.setCommandBuy;
    }
    if (type == ConditionCommandTransactionTypeEnum.sell) {
      return StockKeyLang.setCommandSell;
    }
    return StockKeyLang.all;
  }

  static String? parteDateTimeToStringCallApi(DateTime? dateTime) {
    try {
      if (dateTime == null) {
        return null;
      }
      return DateFormat('yyyy-MM-dd').format(dateTime);
    } catch (e) {
      return null;
    }
  }

  static String formatNumber(num? input) {
    if (input == null) {
      return '';
    }
    try {
      if (input < 1000) {
        if (input - input.toInt() == 0) {
          return '${input.toInt()}';
        }
        return input.toStringAsFixed(2);
      }
      return AppNumberFormatUtils.formatNum(input, haveSymbol: false);
    } catch (e) {
      return '';
    }
  }

  static num updateValue(bool increase, num value, num step) {
    if (increase) {
      if (value % step == 0) {
        return value + step;
      } else {
        return (value ~/ step + 1) * step;
      }
    } else {
      if (value % step == 0) {
        return value > step ? value - step : 0;
      } else {
        return (value ~/ step) * step;
      }
    }
  }
}
