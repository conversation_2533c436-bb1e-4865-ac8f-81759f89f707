import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/exchange.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/place_order_bloc.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/place_order_state.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/state/place_order_message.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/choice_dilution_action_widget.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_take_profit_choice_trigger_condition.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_take_profit_effect_time.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_take_profit_order/bloc/conditional_take_profit_order_bloc.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_take_profit_trigger_condition_enum.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/take_profit_rate_profit_input.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/take_profit_slippage_input.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/utils/message_listener.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/widgets/inputs/conditional_volume_input/conditional_volume_cubit.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/widgets/inputs/conditional_volume_input/conditional_volume_input.dart';

class ConditionalTakeProfitOrderComponent extends StatefulWidget {
  const ConditionalTakeProfitOrderComponent({
    Key? key,
    required this.orderType,
    required this.subAccountType,
  }) : super(key: key);
  final OrderType orderType;
  final SubAccountType subAccountType;

  @override
  State<ConditionalTakeProfitOrderComponent> createState() =>
      _ConditionalTakeProfitOrderComponentState();
}

class _ConditionalTakeProfitOrderComponentState
    extends State<ConditionalTakeProfitOrderComponent> {
  late final ConditionalTakeProfitOrderBloc _takeProfitBloc;
  late final PlaceOrderBloc _placeOrderBloc;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    print('Conditional Take Profit Init');
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _initialBloc();
  }

  void _initialBloc() {
    _placeOrderBloc = context.read<PlaceOrderBloc>();
    _takeProfitBloc = context.read<ConditionalTakeProfitOrderBloc>();
    _takeProfitBloc.add(TakeProfitClearStateEvent());

    _initialDataAndUpdateOrderType();
  }

  void _initialDataAndUpdateOrderType() {
    _takeProfitBloc.onChangePlaceOrderBloc(_placeOrderBloc);
    _takeProfitBloc.add(TakeProfitInitialDataEvent());
    _takeProfitBloc.add(
      TakeProfitUpdateOrderTypeEvent(_placeOrderBloc.state.orderType),
    );
    _takeProfitBloc.add(
      TakeProfitUpdateSubAccountEvent(_placeOrderBloc.state.subAccount),
    );
    _takeProfitBloc.add(
      TakeProfitUpdateExchangeEvent(
        _placeOrderBloc.state.exchange ?? Exchange.hsx,
      ),
    );
  }

  @override
  void dispose() {
    _takeProfitBloc.add(TakeProfitClearStateEvent());
    _placeOrderBloc.add(const ChangeVolumeEvent(''));
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        kSpacingHeight16,
        MultiBlocListener(
          listeners: [
            BlocListener<PlaceOrderBloc, PlaceOrderState>(
              listenWhen:
                  (current, previous) =>
                      current.orderType != previous.orderType,
              listener: (context, state) {
                _takeProfitBloc.add(
                  TakeProfitUpdateOrderTypeEvent(state.orderType),
                );
                _takeProfitBloc.add(TakeProfitClearStateEvent());
              },
            ),
            BlocListener<PlaceOrderBloc, PlaceOrderState>(
              listenWhen:
                  (current, previous) => current.symbol != previous.symbol,
              listener: (context, state) {
                _takeProfitBloc.add(TakeProfitOnUpdateSymbol(state.symbolText));
                _takeProfitBloc.add(
                  TakeProfitUpdateExchangeEvent(state.exchange ?? Exchange.hsx),
                );
                _takeProfitBloc.add(TakeProfitClearStateEvent());
              },
            ),
            BlocListener<PlaceOrderBloc, PlaceOrderState>(
              listenWhen:
                  (current, previous) => current.exchange != previous.exchange,
              listener: (context, state) {
                _takeProfitBloc.add(
                  TakeProfitUpdateExchangeEvent(state.exchange ?? Exchange.hsx),
                );
              },
            ),
            BlocListener<PlaceOrderBloc, PlaceOrderState>(
              listenWhen:
                  (current, previous) =>
                      current.subAccount != previous.subAccount,
              listener: (context, state) {
                _takeProfitBloc.add(
                  TakeProfitUpdateSubAccountEvent(state.subAccount),
                );
                _takeProfitBloc.add(TakeProfitClearStateEvent());
              },
            ),
            BlocListener<PlaceOrderBloc, PlaceOrderState>(
              listenWhen:
                  (previous, current) => previous.order != current.order,
              listener: (context, state) {
                context.read<ConditionalTakeProfitOrderBloc>().add(
                  TakeProfitUpdateOrderEvent(order: state.order),
                );
              },
            ),
            BlocListener<PlaceOrderBloc, PlaceOrderState>(
              listenWhen:
                  (previous, current) =>
                      previous.maxVolume != current.maxVolume,
              listener: (context, state) {
                context.read<ConditionalTakeProfitOrderBloc>().add(
                  TakeProfitOnUpdateMaxVolume(state.maxVolume),
                );
              },
            ),
            MessageListener<PlaceOrderBloc, PlaceOrderState>(
              message: {
                PlaceOrderMessageType.successOrder: (_) {
                  _takeProfitBloc.add(TakeProfitClearStateEvent());
                },
              },
            ),
          ],
          child: BlocBuilder<
            ConditionalTakeProfitOrderBloc,
            ConditionalTakeProfitOrderState
          >(
            buildWhen:
                (previous, current) =>
                    previous.costPrice != current.costPrice ||
                    previous.tradeDisplay != current.tradeDisplay,
            builder: (context, state) {
              return Row(
                children: [
                  _RowContent(
                    title:
                        WealthStock.current.averageCostPriceConditional,
                    content: state.costPriceDisplay,
                  ),
                  _RowContent(
                    title: WealthStock.current.holdVolume,
                    content: state.tradeDisplay,
                  ),
                ],
              );
            },
          ),
        ),
        kSpacingHeight16,
        Text(
          WealthStock.current.triggerCondition,
          style: vpTextStyle.captionRegular?.copyWith(color: themeData.gray500),
        ),
        kSpacingHeight4,

        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: BlocBuilder<PlaceOrderBloc, PlaceOrderState>(
                buildWhen:
                    (previous, current) =>
                        previous.orderType != current.orderType,
                builder: (context, placeOrderState) {
                  return BlocSelector<
                    ConditionalTakeProfitOrderBloc,
                    ConditionalTakeProfitOrderState,
                    ConditionalTakeProfitTriggerConditionEnum
                  >(
                    selector: (state) {
                      return state.triggerCondition;
                    },
                    builder: (context, state) {
                      return ConditionalTakeProfitChoiceTriggerCondition(
                        orderType: placeOrderState.orderType,
                        triggerCondition: state,
                        onChoiceTriggerCondition: (value) {
                          _takeProfitBloc.add(
                            UpdateTriggerConditionEvent(value),
                          );
                        },
                      );
                    },
                  );
                },
              ),
            ),
            kSpacingWidth4,
            Expanded(
              child: BlocBuilder<PlaceOrderBloc, PlaceOrderState>(
                builder: (context, state) {
                  return TakeProfitRateProfitInputCommon<
                    ConditionalTakeProfitOrderBloc
                  >(
                    orderType: state.orderType,
                    order: state.order,
                    exchange: state.exchange ?? Exchange.hsx,
                    onTap: () {
                      context.read<ConditionalTakeProfitOrderBloc>().add(
                        OnTapFocusRateProfitInput(),
                      );
                    },
                    onUpdateRateProfit: (rateProfitInput) {
                      context.read<ConditionalTakeProfitOrderBloc>().add(
                        UpdateRateProfitInputStateEvent(rateProfitInput),
                      );
                    },
                  );
                },
              ),
            ),

            // const Expanded(
            //     child: _InputFormDecoder(
            //         child: ConditionalTakeProfitRateProfitInput())),
          ],
        ),
        kSpacingHeight16,
        Text(
          WealthStock.current.slippageMargin,
          style: vpTextStyle.captionRegular?.copyWith(color: themeData.gray500),
        ),
        kSpacingHeight4,
        BlocBuilder<PlaceOrderBloc, PlaceOrderState>(
          builder: (context, state) {
            return TakeProfitSlippageInputCommon<
              ConditionalTakeProfitOrderBloc
            >(
              orderType: state.orderType,
              order: state.order,
              exchange: state.exchange ?? Exchange.hsx,
              onTap: () {
                context.read<ConditionalTakeProfitOrderBloc>().add(
                  OnTapFocusSlippageInput(),
                );
              },
              onUpdateRateProfit: (slippageInput) {
                context.read<ConditionalTakeProfitOrderBloc>().add(
                  UpdateSlippageInputStateEvent(slippageInput),
                );
              },
            );
          },
        ),
        // const _InputFormDecoder(
        //     child: ConditionalTakeProfitSlippageProfitMarginInput()),
        kSpacingHeight16,
        BlocBuilder<
          ConditionalTakeProfitOrderBloc,
          ConditionalTakeProfitOrderState
        >(
          buildWhen:
              (previous, current) =>
                  previous.orderPriceDisplay != current.orderPriceDisplay,
          builder: (context, state) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  getStockLang(StockKeyLang.orderPrice),
                  style: vpTextStyle.body14?.copyWith(color: themeData.gray700),
                ),
                Text(
                  state.orderPriceDisplay,
                  style: vpTextStyle.body14?.copyWith(color: themeData.black),
                ),
              ],
            );
          },
        ),
        kSpacingHeight16,
        Text(
          getStockLang(StockKeyLang.volume),
          style: vpTextStyle.captionRegular?.copyWith(color: themeData.gray500),
        ),
        kSpacingHeight4,
        BlocBuilder<PlaceOrderBloc, PlaceOrderState>(
          builder: (context, state) {
            return BlocBuilder<
              ConditionalTakeProfitOrderBloc,
              ConditionalTakeProfitOrderState
            >(
              builder: (context, takeProfitState) {
                return ConditionalVolumeInputCommon<
                  ConditionalTakeProfitOrderBloc
                >(
                  orderType: state.orderType,
                  order: state.order,
                  exchange: state.exchange ?? Exchange.hsx,
                  onTap: () {
                    context.read<ConditionalTakeProfitOrderBloc>().add(
                      OnTapFocusVolumeInput(),
                    );
                  },
                  onUpdateVolume: (state) {
                    context.read<ConditionalTakeProfitOrderBloc>().add(
                      UpdateTakeProfitVolumeEvent(state as ConditionalVolumeInputState),
                    );
                  },
                );
              },
            );
          },
        ),
        kSpacingHeight16,
        Text(
          WealthStock.current.effectiveTime,
          style: vpTextStyle.captionRegular?.copyWith(
            color: themeData.gray500,
          ),
        ),
        kSpacingHeight4,
        const _InputFormDecoder(child: ConditionalTakeProfitEffectTime()),
        kSpacingHeight16,
        const ChoiceDilutionActionWidget(),
      ],
    );
  }
}

class _RowContent extends StatelessWidget {
  const _RowContent({Key? key, required this.title, required this.content})
    : super(key: key);
  final String title;
  final String content;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: vpTextStyle.captionRegular?.copyWith(
              color: themeData.gray500,
            ),
          ),
          Text(
            content,
            style: vpTextStyle.body14?.copyWith(
              color: themeData.black,
            ),
          ),
        ],
      ),
    );
  }
}

class _InputFormDecoder extends StatelessWidget {
  const _InputFormDecoder({Key? key, required this.child}) : super(key: key);
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(SizeUtils.kRadius4),
      child: ColoredBox(color: themeData.highlightBg, child: child),
    );
  }
}
