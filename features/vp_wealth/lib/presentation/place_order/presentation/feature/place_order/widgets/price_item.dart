import 'package:auto_size_text/auto_size_text.dart' as autoSized;
import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_wealth/data/utils/common_color_utils.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/price_type.dart';

class PriceItem extends StatelessWidget {
  final PriceType priceType;
  final num? price;
  final double? referencePrice;
  final double? ceilingPrice;
  final double? floorPrice;
  final Function(String)? onTap;
  final CrossAxisAlignment crossAxisAlignment;

  const PriceItem({
    Key? key,
    required this.price,
    required this.priceType,
    this.referencePrice,
    this.ceilingPrice,
    this.floorPrice,
    this.onTap,
    this.crossAxisAlignment = CrossAxisAlignment.center,
  }) : super(key: key);

  final pTC = 'TC'; // Tham Chieu
  final pTB = 'TB'; // Trung Binh

  String priceLabel(BuildContext context) {
    switch (priceType) {
      case PriceType.ceiling:
        return WealthStock.current.priceCeiling;
      case PriceType.floor:
        return WealthStock.current.priceFloor;
      case PriceType.reference:
        return pTC;
      case PriceType.lowPrice:
        return WealthStock.current.priceLow;
      case PriceType.averagePrice:
        return pTB;
      case PriceType.highPrice:
        return WealthStock.current.priceHigh;
      default:
        return WealthStock.current.pricePrice;
    }
  }

  Color priceColor() {
    switch (priceType) {
      case PriceType.ceiling:
        return vpColor.chartPurple;
      case PriceType.floor:
        return themeData.floorColor;
      case PriceType.reference:
        return themeData.yellow;
      case PriceType.averagePrice:
      case PriceType.highPrice:
      case PriceType.lowPrice:
        return emptyPrice
            ? themeData.gray900
            : CommonthemeData.colorByPrice(
              referencePrice: referencePrice ?? 0,
              currentPrice: price ?? 0,
              floorPrice: floorPrice ?? 0,
              ceilingPrice: ceilingPrice ?? 0,
            );

      default:
        if (price == null ||
            price == 0.0 ||
            referencePrice == null ||
            ceilingPrice == null ||
            floorPrice == null) {
          return themeData.yellow;
        }
        return CommonthemeData.colorByPrice(
          currentPrice: price!,
          referencePrice: referencePrice!,
          ceilingPrice: ceilingPrice!,
          floorPrice: floorPrice!,
        );
    }
  }

  bool get emptyPrice => price == null || (price ?? 0).isZero();

  @override
  Widget build(BuildContext context) {
    final priceString = emptyPrice ? '-' : (price! / 1000.0).toStringAsFixed(2);
    return GestureDetector(
      onTap:
          onTap != null
              ? priceString == '-'
                  ? null
                  : () => onTap!.call(priceString)
              : null,
      behavior: HitTestBehavior.opaque,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: crossAxisAlignment,
        children: [
          Text(
            priceLabel(context),
            style: vpTextStyle.captionRegular?.copyWith(
              color: themeData.gray500,
            ),
          ),
          kSpacingHeight4,
          AnimatedDefaultTextStyle(
            style: vpTextStyle.captionRegular!.copyWith(color: priceColor()),
            duration: const Duration(milliseconds: 300),
            child: autoSized.AutoSizeText(priceString, maxLines: 1),
          ),
        ],
      ),
    );
  }
}
