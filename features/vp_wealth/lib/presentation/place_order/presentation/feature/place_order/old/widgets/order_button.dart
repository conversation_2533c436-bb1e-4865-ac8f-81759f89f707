import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_design_system/widget/button/vps_button.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/widgets/place_order_confirm_dialog.dart';
import 'package:vp_wealth/presentation/wealths/plan/support_person/sign_up_button.dart';

import '../../../../../utils/place_order_utils.dart';
import '../../bloc/bloc.dart';

class OrderButton extends StatefulWidget {
  const OrderButton({Key? key}) : super(key: key);

  @override
  State<OrderButton> createState() => _OrderButtonState();
}

class _OrderButtonState extends State<OrderButton>
    with SingleTickerProviderStateMixin {
  late final AnimationController _animationController = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 200),
  );
  late final bloc = context.read<PlaceOrderBloc>();

  @override
  void dispose() {
    super.dispose();
    _animationController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<PlaceOrderBloc, PlaceOrderState>(
      listenWhen:
          (previous, current) =>
              previous.orderNavigateStatus != OrderNavigateStatus.enable &&
              current.orderNavigateStatus == OrderNavigateStatus.enable,
      listener: (context, state) => _animationController.forward(from: 0),
      child: OrderNavigateStatusSelector((status) {
        if (status == OrderNavigateStatus.next) {
          return NextButton(onTap: () => bloc.add(TapNextEvent()));
        }
        return ScaleTransition(
          scale: Tween<double>(
            begin: status == OrderNavigateStatus.enable ? 0.8 : 1,
            end: 1,
          ).animate(
            CurvedAnimation(
              parent: _animationController,
              curve: Curves.easeOutBack,
            ),
          ),
          child: OrderSelector(
            (orderState) => VpsButton.primarySmall(
              disabled: status != OrderNavigateStatus.enable,
              padding: EdgeInsets.only(left: 16, right: 16, bottom: 4, top: 4),
              title: orderState.buttonKeyLang,
              onPressed:
                  () => NoDuplicate(() async {
                    bloc.add(const FocusKeyboardEvent(FocusKeyboard.none));
                    if (bloc.state.showConfirmOrder) {
                      await showDialog(
                        context: context,
                        builder:
                            (_) => PlaceOrderConfirmDialog(
                              onConfirm:
                                  (showConfirmOrder) => context
                                      .read<PlaceOrderBloc>()
                                      .add(OrderSubmitEvent(showConfirmOrder)),
                              state: bloc.state,
                            ),
                      );
                    } else {
                      bloc.add(const OrderSubmitEvent(null));
                    }
                  }),
            ),
          ),
        );
      }),
    );
  }
}
