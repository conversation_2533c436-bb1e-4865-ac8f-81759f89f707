import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/generated/l10n.dart';

class NewOrderIntroContent extends StatelessWidget {
  const NewOrderIntroContent({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          WealthStock.current.newOrderInterfaceIntro1,
          style: vpTextStyle.headine6?.copyWith(
            fontWeight: FontWeight.bold,
            color: themeData.gray700,
          ),
        ),
        kSpacingHeight8,
        Text(
          WealthStock.current.newOrderInterfaceIntro2,
          style: vpTextStyle.body14?.copyWith(color: themeData.gray700),
        ),
        Text(
          WealthStock.current.newOrderInterfaceIntro3,
          style: vpTextStyle.body14?.copyWith(
            fontWeight: FontWeight.bold,
            color: themeData.gray700,
          ),
        ),
      ],
    );
  }
}
