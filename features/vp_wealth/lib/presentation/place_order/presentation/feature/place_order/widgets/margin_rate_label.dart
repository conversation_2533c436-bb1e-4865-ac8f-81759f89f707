import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/generated/l10n.dart';

import '../bloc/bloc.dart';

class MarginRateLabel extends StatelessWidget {
  const MarginRateLabel({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => MarginRateSelector(
        (marginRateState) => marginRateState.visible
            ? Column(
                children: [
                  kSpacingHeight6,
                  Row(
                    children: [
                      Text(
                        '${WealthStock.current.marginRate}: ',
                        style: vpTextStyle.captionRegular
                            ?.copyWith(color: themeData.gray500),
                      ),
                      Text(
                        marginRateState.label,
                        style: vpTextStyle.captionRegular
                            ?.copyWith(color: themeData.gray500),
                      ),
                    ],
                  ),
                ],
              )
            : const SizedBox.shrink(),
      );
}
