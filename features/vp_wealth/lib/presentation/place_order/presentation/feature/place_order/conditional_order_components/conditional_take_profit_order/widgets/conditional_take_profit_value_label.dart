import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/place_order_bloc.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/place_order_selector.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/place_order_state.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_take_profit_order/bloc/conditional_take_profit_order_bloc.dart';
import 'package:vp_wealth/presentation/place_order/utils/ext.dart';

class ConditionalTakeProfitValueLabel extends StatelessWidget {
  final bool center;

  const ConditionalTakeProfitValueLabel({Key? key, this.center = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment:
          center ? CrossAxisAlignment.center : CrossAxisAlignment.start,
      children: [
        NewOrderSelector(
          (newOrder) => Text(
            newOrder
                ? WealthStock.current.orderValue
                : WealthStock.current.value,
            style: vpTextStyle.captionRegular,
          ),
        ),
        Expanded(child: BlocBuilder<PlaceOrderBloc, PlaceOrderState>(
          builder: (context, placeOrderState) {
            return BlocBuilder<ConditionalTakeProfitOrderBloc,
                ConditionalTakeProfitOrderState>(
              buildWhen: (previous, current) =>
                  previous.totalOrderPrice != current.totalOrderPrice,
              builder: (context, state) {
                return ValueSelector(
                  (value) => AutoSizeText(
                    state.totalOrderPrice.valueText,
                    style: vpTextStyle.body14?.copyWith(
                      color: value.color,
                    ),
                    minFontSize: 2,
                  ),
                );
              },
            );
          },
        )),
      ],
    );
  }
}
