import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/generated/l10n.dart';

class NewOrderIntroAction extends StatelessWidget {
  final ValueChanged<bool> onChange;

  const NewOrderIntroAction({super.key, required this.onChange});

  @override
  Widget build(BuildContext context) {
    return ActionDialog(
      textButtonLeft: WealthStock.current.orderConfirmLate,
      textButtonRight: WealthStock.current.tryNow,
      textStyleLeft: vpTextStyle.body14?.copyWith(color: themeData.gray700),
      colorBorderButtonLeft: themeData.gray700,
      onPressedLeft: () {
        onChange(false);
        context.pop();
      },
      onPressedRight: () {
        onChange(true);
        context.pop();
      },
    );
  }
}

class ActionDialog extends StatelessWidget {
  const ActionDialog({
    Key? key,
    this.textButtonLeft,
    this.textStyleLeft,
    this.colorButtonLeft,
    this.colorBorderButtonLeft,
    this.onPressedLeft,
    this.textButtonRight,
    this.textStyleRight,
    this.colorButtonRight,
    this.colorBorderButtonRight,
    this.onPressedRight,
  }) : super(key: key);
  final String? textButtonLeft;
  final TextStyle? textStyleLeft;
  final Color? colorButtonLeft;
  final Color? colorBorderButtonLeft;
  final Function()? onPressedLeft;

  final String? textButtonRight;
  final TextStyle? textStyleRight;
  final Color? colorButtonRight;
  final Color? colorBorderButtonRight;
  final Function()? onPressedRight;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        textButtonLeft != null
            ? Expanded(
              child: VpsButton.secondarySmall(
                title: textButtonLeft ?? '',
                onPressed: onPressedLeft,
              ),
            )
            : Container(),
        Offstage(
          offstage: textButtonLeft == null || textButtonRight == null,
          child: const SizedBox(width: SizeUtils.kSize8),
        ),
        textButtonRight != null
            ? Expanded(
              child: VpsButton.primarySmall(
                title: textButtonRight ?? '',
                onPressed: onPressedRight,
              ),
            )
            : Container(),
      ],
    );
  }
}
