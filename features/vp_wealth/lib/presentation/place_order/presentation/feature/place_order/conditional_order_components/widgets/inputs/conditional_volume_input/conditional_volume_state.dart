part of 'conditional_volume_cubit.dart';

enum ConditionalValidVolumeStatus {
  init,
  invalid,
  invalidMax,
  invalidBuy,
  invalidSell,
  invalidOddLot,
  done,
  invalidNoneVolume,
  notHolding,
  cannotAcceptOddLot,
}

class ConditionalVolumeInputState extends InputFieldState {
  final num? volume;
  final OrderType orderType;
  final Order order;
  final FocusKeyboard focusKeyboard;
  final Exchange exchange;
  final bool? isHolding;
  final num? tradOfSecurities;
  final bool? isMarketCommand;

  const ConditionalVolumeInputState({
    this.volume,
    required this.orderType,
    required this.order,
    required bool highlight,
    required this.focusKeyboard,
    required this.exchange,
    this.isHolding,
    this.tradOfSecurities,
    this.isMarketCommand,
  }) : super(highlight: highlight);

  ConditionalVolumeInputState copyWith({
    num? volume,
    OrderType? orderType,
    FocusKeyboard? focusKeyboard,
    Order? order,
    bool clearVolume = false,
    bool highlight = false,
    Exchange? exchange,
    bool? isHolding,
    num? tradOfSecurities,
    bool? isMarketCommand,
  }) => ConditionalVolumeInputState(
    volume: clearVolume ? null : volume ?? this.volume,
    orderType: orderType ?? this.orderType,
    focusKeyboard: focusKeyboard ?? this.focusKeyboard,
    order: order ?? this.order,
    highlight: highlight,
    exchange: exchange ?? this.exchange,
    isHolding: isHolding ?? this.isHolding,
    tradOfSecurities: tradOfSecurities ?? this.tradOfSecurities,
    isMarketCommand: isMarketCommand ?? this.isMarketCommand,
  );

  bool get _isAcceptOddLot => true;

  bool get _isGtcOrder => orderType == OrderType.gtc;

  String get maxVolumeText => maxVolume.toMoney(showSymbol: false);

  bool get isHose => exchange == Exchange.hsx;

  num _getMaxSell() {
    // if (maxForSell != null) {
    //   return maxForSell!;
    // } else {
    if (isHose) {
      return 500000.0;
    } else {
      return 1000000.0;
    }
    // }
  }

  num get _maxBuy => isHose ? 500000.0 : 1000000.0;

  num get maxVolume => _getMaxSell();

  num get volumeNotNull => volume ?? 0;

  ConditionalValidVolumeStatus get _volumeStatus {
    if (order == Order.sell && tradOfSecurities == -1) {
      return ConditionalValidVolumeStatus.notHolding;
    }
    if (volume == null) return ConditionalValidVolumeStatus.init;
    if (volume == 0) return ConditionalValidVolumeStatus.invalid;
    if (isOddLot && (isMarketCommand ?? false)) {
      return ConditionalValidVolumeStatus.cannotAcceptOddLot;
    }
    // if (maxVolume == 0 && order == Order.buy) {
    //   return ValidVolumeStatus.invalidBuy;
    // }
    if (maxVolume == 0) {
      return ConditionalValidVolumeStatus.invalidSell;
    }
    // if (volumeNotNull < 100) {
    //   return ConditionalValidVolumeStatus.invalidOddLot;
    // }
    if (volumeNotNull > 99 && volumeNotNull % 100.0 != 0.0) {
      return ConditionalValidVolumeStatus.invalid;
    }
    if (volumeNotNull > maxVolume && !_isGtcOrder) {
      return ConditionalValidVolumeStatus.invalidMax;
    }
    return ConditionalValidVolumeStatus.done;
  }

  num decreaseVolume() {
    if (volumeNotNull == 0) {
      return 0;
    }
    if (_isAcceptOddLot && volumeNotNull > 0 && volumeNotNull <= 100) {
      return volumeNotNull - 1;
    }
    if (volumeNotNull < 100.0) {
      return 0;
    }
    if (volumeNotNull % 100 != 0) {
      return (volumeNotNull / 100).floor() * 100;
    }
    return volumeNotNull - 100;
  }

  num increaseVolume() {
    if (_isAcceptOddLot && volumeNotNull == 0) {
      if (maxVolume > 0 && maxVolume < 100) {
        return maxVolume;
      }
      return volumeNotNull + 100;
    }
    if (_isAcceptOddLot && volumeNotNull > 0 && volumeNotNull < 100) {
      return volumeNotNull + 1;
    }
    if (volumeNotNull % 100 != 0) {
      return (volumeNotNull / 100).ceil() * 100;
    }
    return volumeNotNull + 100;
  }

  num get suggestion => _roundVolume(maxVolume.toDouble());

  int _roundVolume(double volume) {
    if (volume < 100.0) {
      return 0;
    }
    final evenDownRound = (volume / 100.0).floor() * 100.0;
    return evenDownRound.toInt();
  }

  @override
  List<TextInputFormatter> get inputFormatter => [
    FilteringTextInputFormatter.deny(RegExp(r'\D$')),
    TextInputFormatter.withFunction((oldValue, newValue) {
      final volumeFormat = newValue.text.volume.volumeString;
      return volumeFormat.length > maxLength ? oldValue : newValue;
    }),
  ];

  @override
  int get maxLength => 12;

  @override
  String get text =>
      volume == null ? '' : volumeNotNull.toMoney(showSymbol: false);

  @override
  Map<String, bool> get error {
    switch (_volumeStatus) {
      case ConditionalValidVolumeStatus.invalid:
        return {WealthStock.current.validateVolume: true};
      case ConditionalValidVolumeStatus.invalidMax:
        return {WealthStock.current.maxVolumeIs: true, maxVolumeText: false};
      case ConditionalValidVolumeStatus.invalidBuy:
        return {WealthStock.current.invalidMaxBuy: true};
      case ConditionalValidVolumeStatus.invalidSell:
        return {WealthStock.current.invalidMaxSell: true};
      case ConditionalValidVolumeStatus.invalidOddLot:
        return {WealthStock.current.invalidOddLot: true};
      case ConditionalValidVolumeStatus.notHolding:
        return {WealthStock.current.youNotHoldingThisStock: true};
      case ConditionalValidVolumeStatus.cannotAcceptOddLot:
        return {WealthStock.current.cannotOrderOddLotWithMarketCommand: true};
      default:
        return {'': false};
    }
  }

  @override
  String get hintLang => WealthStock.current.volumeKL;

  @override
  bool get validator {
    switch (_volumeStatus) {
      case ConditionalValidVolumeStatus.invalid:
      case ConditionalValidVolumeStatus.invalidMax:
      case ConditionalValidVolumeStatus.invalidBuy:
      case ConditionalValidVolumeStatus.invalidSell:
      case ConditionalValidVolumeStatus.invalidOddLot:
      case ConditionalValidVolumeStatus.notHolding:
      case ConditionalValidVolumeStatus.cannotAcceptOddLot:
        return false;
      default:
        return true;
    }
  }

  @override
  bool get isDone => _volumeStatus == ConditionalValidVolumeStatus.done;

  @override
  bool get focus => focusKeyboard == FocusKeyboard.volume;

  bool get isOddLot => volumeNotNull < 100.0;

  @override
  List<Object?> get props => [
    text,
    error.toString(),
    validator,
    isDone,
    focus,
    enable,
    highlight,
    tradOfSecurities,
    isMarketCommand,
  ];
}
