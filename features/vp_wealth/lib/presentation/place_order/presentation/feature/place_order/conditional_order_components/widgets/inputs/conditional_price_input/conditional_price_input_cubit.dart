import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_wealth/data/model/stock_detail_entity.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/conditional_market_command_enum.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/exchange.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/gen/locale_keys.g.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/place_order_state.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/state/base_state.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_focus_keyboard.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/property_item/property_item_cubit.dart';
import 'package:vp_wealth/presentation/place_order/utils/ext.dart';

part 'conditional_price_input_state.dart';

class ConditionalPriceInputCubit extends Cubit<ConditionalPriceInputState> {
  ConditionalPriceInputCubit({
    required this.exchange,
    this.isBuy = false,
    this.ceilingPrice,
    this.floorPrice,
    this.isPrice,
  }) : super(ConditionalPriceInputState(
          formatPrice: true,
          priceNotFormat: '',
          orderType: OrderType.waiting,
          exchange: exchange,
          focusKeyboard: ConditionalFocusKeyBoard.price,
          highlight: false,
          ceilingPrice: ceilingPrice,
          floorPrice: floorPrice,
          isPrice: isPrice ?? false,
        ));
  final Exchange exchange;
  final bool isBuy;
  final double? ceilingPrice;
  final double? floorPrice;
  final bool? isPrice;

  double? getPriceAfterChangeMarketCommand() {
    // if (isBuy) {
    //   return state.floorPriceInvest;
    // } else {
    //   return state.ceilingPriceInvest;
    // }

    if (state.isBuy) {
      return state.ceilingPrice;
    } else {
      return state.floorPrice;
    }
  }

  void onChangeToStringCommand(MarketCommandEnum? marketCommand) {
    if (marketCommand != null && marketCommand != MarketCommandEnum.none) {
      emit(state.copyWith(
          marketCommand: marketCommand,
          price: getPriceAfterChangeMarketCommand()));
    } else {
      emit(state.copyWith(marketCommand: marketCommand));
    }
  }

  void onIncreasePrice() {
    if (state.isInit) {
      final price = state.isBuy ? state.floorPrice : state.ceilingPrice;
      if (price == null) {
        return;
      } else {
        emit(state.copyWith(price: price, formatPrice: true));
      }
    } else {
      if (state.isStringCommand) {
        emit(state.copyWith(
          price: state.closePrice,
          marketCommand: MarketCommandEnum.none,
          formatPrice: true,
        ));
      } else {
        emit(state.copyWith(price: state.increasePrice(), formatPrice: true));
      }
    }
  }

  void onDecreasePrice() {
    if (state.isInit) {
      final price = state.isBuy ? state.floorPrice : state.ceilingPrice;
      if (price == null) {
        return;
      } else {
        emit(state.copyWith(price: price, formatPrice: true));
      }
    } else {
      if (state.isStringCommand) {
        emit(state.copyWith(
            price: state.closePrice, marketCommand: MarketCommandEnum.none));
      } else {
        emit(state.copyWith(price: state.decreasePrice(), formatPrice: true));
      }
    }
  }

  void onChangePrice(String priceText) {
    if (priceText.isEmpty) {
      emit(state.copyWith(clearPrice: true));
    }
    emit(state.copyWith(
      price: priceText.price,
      formatPrice: false,
      priceNotFormat: priceText,
      marketCommand: MarketCommandEnum.none,
    ));
  }

  void onUpdateClosePrice({double? closePrice}) {
    emit(state.copyWith(closePrice: closePrice));
  }

  void onUpdateExchange({Exchange? exchange}) {
    emit(state.copyWith(exchange: exchange));
  }

  void onUpdateOrderStatus(Order order) {
    emit(state.copyWith(order: order));
    if (state.marketCommand != null &&
        state.marketCommand != MarketCommandEnum.none) {
      emit(state.copyWith(price: getPriceAfterChangeMarketCommand()));
    }
  }

  void onUpdatePriceFromInvest(
      PropertyItemState<StockDetailEntity?> stockInfoState) {
    emit(state.copyWith(
      closePrice: stockInfoState.item?.price,
      floorPriceInvest: stockInfoState.item?.floorPrice,
      ceilingPriceInvest: stockInfoState.item?.ceilingPrice,
    ));
  }

  void onUpdateStateByPlaceOrderState(PlaceOrderState placeOrderState) {
    emit(state.copyWith(
      orderType: placeOrderState.orderType,
      exchange: placeOrderState.exchange,
      ceilingPrice: placeOrderState.inputPriceState.ceilingPrice,
      floorPrice: placeOrderState.inputPriceState.floorPrice,
      order: placeOrderState.order,
    )..stockType = placeOrderState.inputPriceState.stockType);
  }
}
