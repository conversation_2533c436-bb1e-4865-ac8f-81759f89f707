import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:vp_common/utils/navigation_service.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/utils/wealth_notification_dialog.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';

Future showCBTTNotifyDialog({required String content}) async {
  final context =
      GetIt.instance<NavigationService>().navigatorKey.currentContext;
  if (context == null) return;
  await showNotifyDialog(
    context: context,
    title: WealthStock.current.notConfirmCBTTClose,
    contentWidget: Html(data: content, style: {}),
    titleStyle: vpTextStyle.headine6?.copyWith(
      color: themeData.gray700,
      fontWeight: FontWeight.bold,
    ),
    // image:
    //     isDark() ? StockKeyAssets.alertBellDark : StockKeyAssets.alertBellLight,
    iconSize: SizeUtils.kSize80,
    imagePadding: const EdgeInsets.only(bottom: SizeUtils.kSize16),
    textButtonLeft: getStockLang(StockKeyLang.close),
    textButtonRight: getStockLang(StockKeyLang.contactCustomerService),
    colorButtonLeft: themeData.bgPopup,
    textStyleLeft: vpTextStyle.body14?.copyWith(
      color: themeData.black,
      fontWeight: FontWeight.bold,
    ),
    onPressedLeft: context.pop,
    onPressedRight: () {
      showModalBottomSheet(
        barrierColor: themeData.overlayBottomSheet,
        context: context,
        elevation: 0,
        backgroundColor: Colors.transparent,
        builder: (_) => Container(),
        // builder:
        // (_) => DialogSelectSupport(
        //   supportContact: SupportContactUsInfo(phone: '1900 636679'),
        // ),
      );
    },
  );
}
