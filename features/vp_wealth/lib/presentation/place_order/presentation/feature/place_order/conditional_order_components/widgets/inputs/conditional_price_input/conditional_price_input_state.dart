part of 'conditional_price_input_cubit.dart';

const tenK = 10000.0;
const fiftyK = 50000.0;

class ConditionalPriceInputState extends InputFieldState {
  final double? price;
  final double? ceilingPrice;
  final double? floorPrice;
  final double? ceilingPriceInvest;
  final double? floorPriceInvest;
  final double? closePrice;
  String? stockType;

  /// formatPrice: default is true for show text label is price formatted variable,
  /// and false for show text label is priceNotFormat variable
  final bool formatPrice;
  final String priceNotFormat;
  final OrderType orderType;
  final Exchange exchange;
  final ConditionalFocusKeyBoard focusKeyboard;
  final MarketCommandEnum? marketCommand;
  final Order? order;
  final bool isPrice;

  ConditionalPriceInputState({
    this.price,
    this.ceilingPrice,
    this.floorPrice,
    this.closePrice,
    required this.formatPrice,
    required this.priceNotFormat,
    required this.orderType,
    required this.exchange,
    required bool highlight,
    required this.focusKeyboard,
    this.marketCommand,
    this.order,
    this.isPrice = true,
    this.ceilingPriceInvest,
    this.floorPriceInvest,
    this.stockType,
  }) : super(highlight: highlight);

  ConditionalPriceInputState copyWith({
    double? price,
    double? ceilingPrice,
    double? floorPrice,
    double? closePrice,
    bool? formatPrice,
    String? priceNotFormat,
    OrderType? orderType,
    Exchange? exchange,
    bool clearPrice = false,
    ConditionalFocusKeyBoard? focusKeyboard,
    bool? highlight,
    MarketCommandEnum? marketCommand,
    Order? order,
    bool? isPrice,
    double? ceilingPriceInvest,
    double? floorPriceInvest,
  }) => ConditionalPriceInputState(
    price: clearPrice ? null : price ?? this.price,
    ceilingPrice: ceilingPrice ?? this.ceilingPrice,
    floorPrice: floorPrice ?? this.floorPrice,
    formatPrice: formatPrice ?? this.formatPrice,
    priceNotFormat: priceNotFormat ?? this.priceNotFormat,
    orderType: orderType ?? this.orderType,
    exchange: exchange ?? this.exchange,
    highlight: highlight ?? this.highlight,
    focusKeyboard: focusKeyboard ?? this.focusKeyboard,
    marketCommand: marketCommand ?? this.marketCommand,
    closePrice: closePrice ?? this.closePrice,
    order: order ?? this.order,
    isPrice: isPrice ?? this.isPrice,
    ceilingPriceInvest: ceilingPriceInvest ?? this.ceilingPriceInvest,
    floorPriceInvest: floorPriceInvest ?? this.floorPriceInvest,
    stockType: stockType,
  );

  bool get isBuy => order == Order.buy;

  bool get _isLoOrder => orderType == OrderType.lo;

  bool get _isLoOrGtcOrder =>
      _isLoOrder || orderType == OrderType.gtc || _isConditionalOrder;

  bool get _isConditionalOrder => true;

  bool get isHose => exchange == Exchange.hsx;

  double get priceNotNull => price ?? 0.0;

  String get _getErrorPriceKeyLang {
    switch (_priceStatus) {
      case ValidPriceStatus.step10Invalid:
        return WealthStock.current.invalidStep10;
      case ValidPriceStatus.step50Invalid:
        return WealthStock.current.invalidStep50;
      case ValidPriceStatus.step100Invalid:
        return WealthStock.current.invalidStep100;
      default:
        if (isPrice) {
          return WealthStock.current.invalidConditionalPrice;
        } else {
          return WealthStock.current.invalidActivationPrice;
        }
    }
  }

  ValidPriceStatus get _priceStatus {
    if (!_isLoOrGtcOrder) return ValidPriceStatus.done;
    if (price == null) return ValidPriceStatus.init;
    if (price == 0.0) return ValidPriceStatus.outOfRange;

    if (stockType.isCW) {
      return priceNotNull % 10.0 != 0.0
          ? ValidPriceStatus.step10Invalid
          : ValidPriceStatus.done;
    }

    if (stockType.isETF) {
      return priceNotNull % (isHose ? 10.0 : 1.0) != 0.0
          ? ValidPriceStatus.step10Invalid
          : ValidPriceStatus.done;
    }
    if (isHose) {
      if (priceNotNull < 10000.0 && priceNotNull % 10.0 != 0.0) {
        return ValidPriceStatus.step10Invalid;
      }
      if (priceNotNull >= 10000.0 &&
          priceNotNull < 49950.0 &&
          priceNotNull % 50.0 != 0.0) {
        return ValidPriceStatus.step50Invalid;
      }
      if (priceNotNull > 50000.0 && priceNotNull % 100.0 != 0.0) {
        return ValidPriceStatus.step100Invalid;
      }
    } else {
      if (priceNotNull % 100.0 != 0.0) {
        return ValidPriceStatus.step100Invalid;
      }
    }
    return ValidPriceStatus.done;
  }

  double _getDecreaseStepPrice() {
    if (stockType.isCW || (stockType.isETF && !exchange.isUpcom)) {
      return 10.0;
    }
    if (!isHose) {
      return 100.0;
    } else if (priceNotNull <= tenK) {
      return 10.0;
    } else if (priceNotNull > tenK && priceNotNull <= fiftyK) {
      return 50.0;
    } else {
      return 100.0;
    }
  }

  double _getIncreaseStepPrice() {
    if (stockType.isCW || (stockType.isETF && !exchange.isUpcom)) {
      return 10.0;
    }
    if (!isHose) {
      return 100.0;
    } else if (priceNotNull < tenK) {
      return 10.0;
    } else if (priceNotNull >= tenK && priceNotNull < fiftyK) {
      return 50.0;
    } else {
      return 100.0;
    }
  }

  double get decreaseStepPrice => _getDecreaseStepPrice();

  double get increaseStepPrice => _getIncreaseStepPrice();

  double get surPlus => priceNotNull % decreaseStepPrice;

  bool get isStringCommand =>
      marketCommand != null && marketCommand != MarketCommandEnum.none;

  double decreasePrice() {
    var value = 0.0;
    if (priceNotNull == 0.0) return 0.0;
    if (surPlus > 0) {
      value = priceNotNull - surPlus;
    } else {
      value = priceNotNull - decreaseStepPrice;
    }
    return value;
    // return priceNotNull - priceStep + surPlus;
  }

  double increasePrice() {
    return priceNotNull + increaseStepPrice - surPlus;
  }

  @override
  int get maxLength => 8;

  String _getTextDisplay() {
    if (marketCommand != null && marketCommand != MarketCommandEnum.none) {
      return marketCommand!.paramsRequest();
    }
    if (price == null) {
      return '';
    } else {
      if (formatPrice) {
        return priceNotNull.getPriceFormatted(convertToThousand: true);
      } else {
        return priceNotFormat;
      }
    }
  }

  @override
  String get text => _getTextDisplay();

  @override
  String get hintLang => WealthStock.current.price;

  @override
  Map<String, bool> get error =>
      isInit ? {'': false} : {_getErrorPriceKeyLang: true};

  @override
  bool get enable => _isLoOrGtcOrder;

  @override
  bool get validator {
    switch (_priceStatus) {
      case ValidPriceStatus.outOfRange:
      case ValidPriceStatus.step10Invalid:
      case ValidPriceStatus.step50Invalid:
      case ValidPriceStatus.step100Invalid:
        return false;
      default:
        return true;
    }
  }

  @override
  bool get isDone => _priceStatus == ValidPriceStatus.done;

  @override
  bool get focus => focusKeyboard == ConditionalFocusKeyBoard.price;

  bool get isInit => _priceStatus == ValidPriceStatus.init;

  @override
  List<Object?> get props => [
    text,
    error.toString(),
    validator,
    isDone,
    focus,
    enable,
    highlight,
    marketCommand,
    order,
    isPrice,
    exchange,
    floorPrice,
    ceilingPrice,
    closePrice,
    ceilingPriceInvest,
    floorPriceInvest,
    price,
  ];

  @override
  List<TextInputFormatter> get inputFormatter => [
    FilteringTextInputFormatter.deny(RegExp(r'^(\.|\,)')),
    FilteringTextInputFormatter.allow(RegExp(r'^\d+(\.|\,)?\d{0,2}')),
    FilteringTextInputFormatter.deny(',', replacementString: '.'),
  ];
}
