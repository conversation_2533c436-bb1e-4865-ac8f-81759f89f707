import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vp_common/utils/navigation_service.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/data/utils/wealth_constants.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';
import 'package:vp_wealth/presentation/place_order/assets/stock_key_assets.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/conditional_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/gen/locale_keys.g.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_take_profit_order/bloc/conditional_take_profit_order_bloc.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_take_profit_trigger_condition_enum.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/utils/text_with_multi_style.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/widgets/conditional_order_confirm_dialog/conditional_order_confirm_row_title.dart';

import '../../../bloc/bloc.dart';

class ConditionalTakeProfitConfirmDialog extends StatelessWidget {
  final Function? onConfirm;
  final PlaceOrderState placeOrderState;
  final ConditionalTakeProfitOrderState takeProfitState;

  const ConditionalTakeProfitConfirmDialog({
    Key? key,
    this.onConfirm,
    required this.placeOrderState,
    required this.takeProfitState,
  }) : super(key: key);

  String getTitleDialog() {
    return getTitleByOrderType() + ' ' + placeOrderState.symbolText;
  }

  Color getColorIcon() {
    if (placeOrderState.order == Order.buy) {
      return themeData.primary;
    } else {
      return themeData.red;
    }
  }

  String getTitleByOrderType() {
    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;
    if (context != null) {
      final orderType = placeOrderState.orderType;
      if (orderType == OrderType.stopLoss) {
        return WealthStock.current.stopLossCommand;
      } else if (orderType == OrderType.takeProfit) {
        return WealthStock.current.takeProfitCommand;
      } else {
        return '';
      }
    } else {
      return '';
    }
  }

  String getRateTitle(ConditionalTakeProfitTriggerConditionEnum condition) {
    final orderType = placeOrderState.orderType;
    if (orderType == OrderType.stopLoss) {
      return _getStopLossRateTitle(condition);
    } else if (orderType == OrderType.takeProfit) {
      return _getTakeProfitRateTitle(condition);
    } else {
      return '';
    }
  }

  String _getTakeProfitRateTitle(
    ConditionalTakeProfitTriggerConditionEnum condition,
  ) {
    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;
    if (context != null) {
      if (condition == ConditionalTakeProfitTriggerConditionEnum.rateProfit) {
        return getStockLang(StockKeyLang.profitTakingRate);
      } else {
        return WealthStock.current.profitMargin;
      }
    } else {
      return '';
    }
  }

  String _getStopLossRateTitle(
    ConditionalTakeProfitTriggerConditionEnum condition,
  ) {
    final context =
        GetIt.instance<NavigationService>().navigatorKey.currentContext;
    if (context != null) {
      if (condition == ConditionalTakeProfitTriggerConditionEnum.rateProfit) {
        return WealthStock.current.stopLossRate;
      } else {
        return WealthStock.current.stopLossMargin;
      }
    } else {
      return '';
    }
  }

  String getRateProfit() {
    final condition = takeProfitState.triggerCondition;
    final rateProfit =
        takeProfitState.takeProfitRateProfitInputState?.text ?? '';
    if (condition == ConditionalTakeProfitTriggerConditionEnum.rateProfit) {
      return '$rateProfit%';
    } else {
      return rateProfit;
    }
  }

  @override
  Widget build(BuildContext context) {
    final subAccountLabel = placeOrderState.subAccountLabel.label;
    final startDate = takeProfitState.effectiveTime.startText;
    final endDate = takeProfitState.effectiveTime.endText;
    final String activationPrice = takeProfitState.orderPrice.getPriceFormatted(
      convertToThousand: true,
    );
    // final String activationPriceSymbol =
    //     takeProfitState.activationPriceType?.getSymbol() ?? '';
    final orderPrice = takeProfitState.orderPriceDisplay;
    final rateProfit = getRateProfit();
    final slippageMargin =
        takeProfitState.takeProfitSlippageInputState?.text ?? '';
    final volume = takeProfitState.conditionalVolumeInputState?.text ?? '';
    final dilutionType =
        takeProfitState.dilutionType?.toStringShowDialog() ?? '';
    final slippageMarginDisplay = slippageMargin.isEmpty ? '0' : slippageMargin;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(
          StockKeyAssets.icOrder,
          color: getColorIcon(),
          package: WealthConstants.packageName,
        ),
        const SizedBox(height: SizeUtils.kSize32),
        Text(
          getTitleDialog(),
          style: vpTextStyle.headine6?.copyWith(
            color: Theme.of(context).textTheme.displayLarge!.color,
          ),
        ),
        const SizedBox(height: SizeUtils.kSize16),
        ConditionalOrderConfirmRowTitle(
          title: getStockLang(StockKeyLang.subAccount),
          content: subAccountLabel,
        ),
        ConditionalOrderConfirmRowTitle(
          title: WealthStock.current.effectiveTime,
          content: '$startDate - $endDate',
          oneRow: true,
        ),
        ConditionalOrderConfirmRowTitle(
          title: getRateTitle(takeProfitState.triggerCondition),
          content: rateProfit,
        ),
        ConditionalOrderConfirmRowTitle(
          title: WealthStock.current.slippageMargin,
          content: slippageMarginDisplay,
        ),
        ConditionalOrderConfirmRowTitle(
          title: getStockLang(StockKeyLang.orderPrice),
          content: orderPrice,
        ),
        ConditionalOrderConfirmRowTitle(
          title: WealthStock.current.volume,
          content: volume,
        ),
        ConditionalOrderConfirmRowTitle(
          title: WealthStock.current.whenHaveEventToDiluteStock,
          content: dilutionType,
        ),
        kSpacingHeight16,
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: themeData.yellow.withOpacity(0.16),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            WealthStock.current.conditionalOrderWaitingConfirmNote,
            style: vpTextStyle.captionRegular?.copyWith(
              color: themeData.gray700,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        kSpacingHeight16,
        DividerWidget(),
        kSpacingHeight16,
        TextWithMultiStyle(
          content: WealthStock.current.conditionalOrderWaitingConfirmGuide,
          textStyle: vpTextStyle.captionRegular?.copyWith(
            color: themeData.gray500,
          ),
          highLightStyle: vpTextStyle.captionRegular?.copyWith(
            color: themeData.black,
          ),
        ),
        kSpacingHeight16,
        Row(
          children: [
            Expanded(
              child: VpsButton.secondarySmall(
                title: WealthStock.current.commandClose,
                onPressed: () => Navigator.pop(context),
              ),
            ),
            const SizedBox(width: SizeUtils.kSize8),
            Expanded(
              child: VpsButton.primarySmall(
                title: getStockLang(StockKeyLang.orderConfirm),
                onPressed: () {
                  context.pop();
                  onConfirm?.call();
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}
