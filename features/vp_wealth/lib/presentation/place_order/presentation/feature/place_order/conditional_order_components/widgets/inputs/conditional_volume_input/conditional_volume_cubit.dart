import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/exchange.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/focus_keyboard.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/gen/locale_keys.g.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/state/base_state.dart';
import 'package:vp_wealth/presentation/place_order/utils/ext.dart';

part 'conditional_volume_state.dart';

class ConditionalVolumeInputCubit extends Cubit<ConditionalVolumeInputState> {
  ConditionalVolumeInputCubit(this.orderType, this.order, this.exchange,
      {this.maxForSell})
      : super(ConditionalVolumeInputState(
          orderType: orderType,
          focusKeyboard: FocusKeyboard.none,
          order: order,
          highlight: false,
          exchange: exchange,
        )) {
    print('Hello MaxForSell: $maxForSell');
  }
  final OrderType orderType;
  final Order order;
  final Exchange exchange;
  final num? maxForSell;

  void onChangeVolumeEvent(String value) {
    final volumeText = value;
    emit(state.copyWith(
        volume: volumeText.volume, clearVolume: volumeText.isEmpty));
  }

  void onUpdateIsMarketCommand(bool isMarketCommand) {
    emit(state.copyWith(isMarketCommand: isMarketCommand));
  }

  void onTapDecreaseVolumeEvent() {
    final decreaseVolume = state.decreaseVolume();
    if (decreaseVolume.toMoney(showSymbol: false).length > state.maxLength) {
      return;
    }
    emit(state.copyWith(volume: decreaseVolume));
  }

  void onTapIncreaseVolumeEvent() {
    final increaseVolume = state.increaseVolume();
    if (increaseVolume.toMoney(showSymbol: false).length > state.maxLength) {
      return;
    }
    emit(state.copyWith(volume: increaseVolume));
  }

  void updateTradeOfSecurities(num? value) {
    emit(state.copyWith(tradOfSecurities: value));
  }

  void onUpdateOrder(Order order) {
    emit(state.copyWith(order: order));
  }
}
