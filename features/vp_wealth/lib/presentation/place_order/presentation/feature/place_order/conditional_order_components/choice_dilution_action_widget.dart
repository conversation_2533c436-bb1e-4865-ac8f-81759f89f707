import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/conditional_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/gen/locale_keys.g.dart';

class ChoiceDilutionActionWidget extends StatelessWidget {
  const ChoiceDilutionActionWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          WealthStock.current.dilutionChoiceTitle,
          style: vpTextStyle.captionRegular
              ?.copyWith(color: themeData.gray500),
        ),
        kSpacingHeight4,
        InkWell(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            width: double.infinity,
            decoration: BoxDecoration(
              color: themeData.highlightBg,
              borderRadius: const BorderRadius.all(Radius.circular(4)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    ConditionalDilutionTypeEnum.cancel.toStringShowDialog(),
                    style: vpTextStyle.body14
                        ?.copyWith(color: themeData.black),
                    textAlign: TextAlign.start,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
