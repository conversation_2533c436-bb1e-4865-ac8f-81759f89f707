import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';
import 'package:vp_wealth/presentation/place_order/domain/domain.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/conditional_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/exchange.dart';
import 'package:vp_wealth/presentation/place_order/gen/asset_util.dart';
import 'package:vp_wealth/presentation/place_order/gen/locale_keys.g.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/place_order_bloc.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/place_order_state.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/choice_dilution_action_widget.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_pending_buy_order/bloc/conditional_pending_order_bloc.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/utils/listener/message_listener.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/widgets/choice_equal_buy_button.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/widgets/conditional_buy_pending_effect_time_widget.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/widgets/inputs/conditional_price_input/conditional_price_input.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/widgets/inputs/conditional_price_input/conditional_price_input_cubit.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/widgets/inputs/conditional_volume_input/conditional_volume_cubit.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/widgets/inputs/conditional_volume_input/conditional_volume_input.dart';

import '../../bloc/state/place_order_message.dart';

class ConditionalPendingOrderComponent extends StatefulWidget {
  const ConditionalPendingOrderComponent({Key? key}) : super(key: key);

  @override
  State<ConditionalPendingOrderComponent> createState() =>
      _ConditionalPendingOrderComponentState();
}

class _ConditionalPendingOrderComponentState
    extends State<ConditionalPendingOrderComponent> {
  late ConditionalPendingOrderBloc _pendingOrderBloc;
  late final PlaceOrderBloc _placeOrderBloc;

  @override
  void didChangeDependencies() {
    // TODO: implement didChangeDependencies
    super.didChangeDependencies();
    _initialBloc();
  }

  void _initialBloc() {
    _placeOrderBloc = context.read<PlaceOrderBloc>();
    _pendingOrderBloc = context.read<ConditionalPendingOrderBloc>();
    context.read<ConditionalPendingOrderBloc>().add(PendingOrderResetState());
    _checkUpdatePriceType();
    _initialDataAndUpdateOrderType();
  }

  void _checkUpdatePriceType() {
    final isBuy = context.read<PlaceOrderBloc>().state.order.isBuy;
    if (isBuy) {
      context.read<ConditionalPendingOrderBloc>().add(
        OnChangePriceType(ConditionalOrderPriceTypeEnum.lessThan),
      );
    } else {
      context.read<ConditionalPendingOrderBloc>().add(
        OnChangePriceType(ConditionalOrderPriceTypeEnum.greaterThan),
      );
    }
  }

  void _initialDataAndUpdateOrderType() {
    _pendingOrderBloc.add(
      PendingOrderUpdateSubAccountEvent(_placeOrderBloc.state.subAccount),
    );
    final maxVolume = context.read<PlaceOrderBloc>().state.maxVolume;
    _pendingOrderBloc.add(IInitialInputValue(_placeOrderBloc.state));
    _pendingOrderBloc.add(UpdateMaxVolumeToBuy(maxVolume));
    _pendingOrderBloc.add(
      ConditionalUpdateOrderEvent(order: _placeOrderBloc.state.order),
    );
  }

  @override
  Widget build(BuildContext context) {
    context.read<ConditionalPendingOrderBloc>().onChangePlaceOrderBloc(
      context.read<PlaceOrderBloc>(),
    );
    return MultiBlocListener(
      listeners: [
        // BlocListener<PlaceOrderBloc, PlaceOrderState>(
        //   // listenWhen: (previous, current) =>
        //   //     previous.priceInputState != current.priceInputState,
        //   listener: (context, state) {
        //     context
        //         .read<ConditionalPendingOrderBloc>()
        //         .add(IInitialInputValue(state));
        //   },
        // ),
        BlocListener<PlaceOrderBloc, PlaceOrderState>(
          listenWhen:
              (previous, current) =>
                  previous.inputPriceState.exchange !=
                  current.inputPriceState.exchange,
          listener: (context, state) {
            context.read<ConditionalPendingOrderBloc>().add(
              ConditionalOrderChangeExchangeEvent(
                exchange: state.inputPriceState.exchange ?? Exchange.hsx,
              ),
            );
          },
        ),
        BlocListener<PlaceOrderBloc, PlaceOrderState>(
          listenWhen:
              (previous, current) =>
                  previous.orderNavigateStatus != current.orderNavigateStatus,
          listener: (context, state) {
            context.read<ConditionalPendingOrderBloc>().add(
              UpdateOrderNavigateStatusOtherInputEvent(
                orderNavigateStatus: state.orderNavigateStatus,
              ),
            );
          },
        ),
        BlocListener<PlaceOrderBloc, PlaceOrderState>(
          listenWhen: (previous, current) => previous.order != current.order,
          listener: (context, state) {
            context.read<ConditionalPendingOrderBloc>().add(
              ConditionalUpdateOrderEvent(order: state.order),
            );
          },
        ),

        // //TODO:
        // BlocListener<PlaceOrderBloc, PlaceOrderState>(
        //   listenWhen: (previous, current) =>
        //       previous.maxVolume != current.maxVolume,
        //   listener: (context, state) {
        //     context
        //         .read<ConditionalPendingOrderBloc>()
        //         .add(OnUpdateMaxVolume(state.maxVolume));
        //   },
        // ),
        BlocListener<PlaceOrderBloc, PlaceOrderState>(
          listenWhen:
              (current, previous) => current.subAccount != previous.subAccount,
          listener: (context, state) {
            _pendingOrderBloc.add(
              PendingOrderUpdateSubAccountEvent(state.subAccount),
            );
          },
        ),
        MessageListener<PlaceOrderBloc, PlaceOrderState>(
          message: {
            PlaceOrderMessageType.successOrder: (_) {
              context.read<ConditionalPendingOrderBloc>().add(
                PendingOrderResetState(),
              );
            },
          },
        ),
      ],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocBuilder<PlaceOrderBloc, PlaceOrderState>(
            builder: (context, state) {
              return Visibility(
                visible: !state.order.isBuy,
                child: Column(
                  children: [
                    kSpacingHeight16,
                    Row(
                      children: [
                        BlocBuilder<
                          ConditionalPendingOrderBloc,
                          ConditionalPendingOrderState
                        >(
                          buildWhen:
                              (previous, current) =>
                                  previous.costPriceDisplay !=
                                  current.costPriceDisplay,
                          builder: (context, state) {
                            return _RowContent(
                              title:
                                  WealthStock
                                      .current
                                      .averageCostPriceConditional,
                              content: state.costPriceDisplay,
                            );
                          },
                        ),
                        BlocBuilder<
                          ConditionalPendingOrderBloc,
                          ConditionalPendingOrderState
                        >(
                          buildWhen:
                              (previous, current) =>
                                  previous.tradeDisplay != current.tradeDisplay,
                          builder: (context, state) {
                            return _RowContent(
                              title: WealthStock.current.holdVolume,
                              content: state.tradeDisplay,
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
          kSpacingHeight16,
          Text(
            WealthStock.current.activationPrice,
            style: vpTextStyle.captionRegular?.copyWith(
              color: themeData.gray500,
            ),
          ),
          kSpacingHeight4,
          BlocSelector<
            ConditionalPendingOrderBloc,
            ConditionalPendingOrderState,
            ConditionalOrderPriceTypeEnum?
          >(
            selector: (state) {
              return state.activationPriceType;
            },
            builder: (context, state) {
              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ChoiceEqualBuyButton(
                    assetsIcon: StockAssetsUtil.svgs.icLessThanEqual,
                    isFocus: state == ConditionalOrderPriceTypeEnum.lessThan,
                    onTap:
                        () => context.read<ConditionalPendingOrderBloc>().add(
                          OnChangePriceType(
                            ConditionalOrderPriceTypeEnum.lessThan,
                          ),
                        ),
                  ),
                  kSpacingWidth4,
                  ChoiceEqualBuyButton(
                    assetsIcon: StockAssetsUtil.svgs.icGreaterThanEqual,
                    isFocus: state == ConditionalOrderPriceTypeEnum.greaterThan,
                    onTap:
                        () => context.read<ConditionalPendingOrderBloc>().add(
                          OnChangePriceType(
                            ConditionalOrderPriceTypeEnum.greaterThan,
                          ),
                        ),
                  ),
                  kSpacingWidth4,
                  Expanded(
                    child: BlocBuilder<PlaceOrderBloc, PlaceOrderState>(
                      builder: (context, state) {
                        return ConditionalPriceInputCommon<
                          ConditionalPendingOrderBloc,
                          ConditionalPendingOrderState
                        >(
                          exchange:
                              state.inputPriceState.exchange ?? Exchange.hsx,
                          ceilingPrice: state.inputPriceState.ceilingPrice,
                          floorPrice: state.inputPriceState.floorPrice,
                          isBuy: state.order.isBuy,
                          isPrice: false,
                          order: state.order,
                          onTap: () {
                            context.read<ConditionalPendingOrderBloc>().add(
                              OnTapFocusActivationPriceInput(),
                            );
                          },
                          onUpdatePrice: (state) {
                            context.read<ConditionalPendingOrderBloc>().add(
                              UpdateConditionalActivationPriceEvent(
                                state as ConditionalPriceInputState,
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ),
                ],
              );
            },
          ),
          kSpacingHeight16,
          Text(
            getStockLang(StockKeyLang.orderPrice),
            style: vpTextStyle.captionRegular?.copyWith(
              color: themeData.gray500,
            ),
          ),
          kSpacingHeight4,
          BlocBuilder<PlaceOrderBloc, PlaceOrderState>(
            builder: (context, state) {
              return ConditionalPriceInputCommon<
                ConditionalPendingOrderBloc,
                ConditionalPendingOrderState
              >(
                exchange: state.inputPriceState.exchange ?? Exchange.hsx,
                ceilingPrice: state.inputPriceState.ceilingPrice,
                floorPrice: state.inputPriceState.floorPrice,
                isBuy: state.order.isBuy,
                order: state.order,
                onTap: () {
                  context.read<ConditionalPendingOrderBloc>().add(
                    OnTapFocusPriceInput(),
                  );
                },
                onUpdatePrice: (state) {
                  context.read<ConditionalPendingOrderBloc>().add(
                    UpdateConditionalPriceEvent(
                      state as ConditionalPriceInputState,
                    ),
                  );
                },
              );
              // }else{
              //   return _InputFormDecoder(child:Text(pendingState.marketCommand?.paramsRequest() ?? '') );
              // }
            },
          ),
          kSpacingHeight16,
          Text(
            getStockLang(StockKeyLang.volume),
            style: vpTextStyle.captionRegular?.copyWith(
              color: themeData.gray500,
            ),
          ),
          kSpacingHeight4,
          BlocBuilder<PlaceOrderBloc, PlaceOrderState>(
            builder: (context, state) {
              return Column(
                children: [
                  ConditionalVolumeInputCommon<ConditionalPendingOrderBloc>(
                    orderType: state.orderType,
                    order: state.order,
                    exchange: state.inputPriceState.exchange ?? Exchange.hsx,
                    onTap: () {
                      context.read<ConditionalPendingOrderBloc>().add(
                        OnTapFocusVolumeInput(),
                      );
                    },
                    onUpdateVolume: (state) {
                      context.read<ConditionalPendingOrderBloc>().add(
                        UpdateConditionalVolumeEvent(
                          state as ConditionalVolumeInputState,
                        ),
                      );
                    },
                  ),
                ],
              );
            },
          ),
          kSpacingHeight16,
          Text(
            WealthStock.current.effectiveTime,
            style: vpTextStyle.captionRegular?.copyWith(
              color: themeData.gray500,
            ),
          ),
          kSpacingHeight4,
          const _InputFormDecoder(
            child: ConditionalBuyPendingEffectTimeWidget(),
          ),
          kSpacingHeight16,
          const ChoiceDilutionActionWidget(),
        ],
      ),
    );
  }
}

class _InputFormDecoder extends StatelessWidget {
  const _InputFormDecoder({Key? key, required this.child}) : super(key: key);
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(SizeUtils.kRadius4),
      child: ColoredBox(color: themeData.highlightBg, child: child),
    );
  }
}

class _RowContent extends StatelessWidget {
  const _RowContent({Key? key, required this.title, required this.content})
    : super(key: key);
  final String title;
  final String content;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: vpTextStyle.captionRegular?.copyWith(
              color: themeData.gray500,
            ),
          ),
          Text(
            content,
            style: vpTextStyle.body14?.copyWith(color: themeData.black),
          ),
        ],
      ),
    );
  }
}
