import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/assets/stock_key_assets.dart';
import 'package:vp_wealth/presentation/place_order/utils/widgets/dialog_change_gtc_effective_time.dart';
import 'package:vp_wealth/presentation/place_order/utils/widgets/type_item_button.dart';

import '../bloc/bloc.dart';

class GtcEffectiveTimeButton extends StatelessWidget {
  const GtcEffectiveTimeButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<PlaceOrderBloc>();
    return GtcEffectiveTimeSelector(
      (gtcTime) =>
          gtcTime.visible
              ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  kSpacingHeight16,
                  Text(
                    WealthStock.current.effectiveTime,
                    style: vpTextStyle.captionRegular?.copyWith(
                      color: themeData.gray500,
                      fontFamily: 'medium',
                    ),
                  ),
                  kSpacingHeight4,
                  TypeItemButton(
                    title: Text(gtcTime.label, style: gtcTime.labelStyle),
                    icon: StockKeyAssets.icDate,
                    bottomSheetBuilder:
                        (_) => DialogChangeGtcEffectiveTime(
                          startDate: gtcTime.time.startDate,
                          endDate: gtcTime.time.endDate,
                        ),
                    onDataReturn:
                        (result) =>
                            bloc.add(ChangeGtcEffectiveTimeEvent(result)),
                  ),
                ],
              )
              : const SizedBox.shrink(),
    );
  }
}
