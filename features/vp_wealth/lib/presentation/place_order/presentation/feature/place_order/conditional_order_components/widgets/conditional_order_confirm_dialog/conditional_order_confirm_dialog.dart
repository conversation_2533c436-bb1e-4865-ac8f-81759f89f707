import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_wealth/common/utils/wealth_utils.dart';
import 'package:vp_wealth/data/utils/constains.dart';
import 'package:vp_wealth/data/utils/wealth_constants.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/lang/stock_key_lang.dart';
import 'package:vp_wealth/presentation/lang/stock_localized_values.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/conditional_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/gen/asset_util.dart';
import 'package:vp_wealth/presentation/place_order/gen/locale_keys.g.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_pending_buy_order/bloc/conditional_pending_order_bloc.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/utils/text_with_multi_style.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/widgets/conditional_order_confirm_dialog/conditional_order_confirm_row_title.dart';

import '../../../bloc/bloc.dart';

class ConditionalOrderConfirmDialog extends StatelessWidget {
  final Function? onConfirm;
  final PlaceOrderState placeOrderState;
  final ConditionalPendingOrderState conditionalBuyState;

  const ConditionalOrderConfirmDialog({
    Key? key,
    this.onConfirm,
    required this.placeOrderState,
    required this.conditionalBuyState,
  }) : super(key: key);

  String getTitleDialog() {
    if (placeOrderState.order == Order.buy) {
      return '${WealthStock.current.pendingBuyCommand} ${placeOrderState.symbolText}';
    } else {
      return '${WealthStock.current.pendingSellCommand} ${placeOrderState.symbolText}';
    }
  }

  Color getColorIcon() {
    if (placeOrderState.order == Order.buy) {
      return themeData.primary;
    } else {
      return themeData.red;
    }
  }

  String getOrderIcon() {
    if (placeOrderState.order == Order.buy) {
      return StockAssetsUtil.svgs.icOrderBuy;
    } else {
      return StockAssetsUtil.svgs.icOrderSell;
    }
  }

  @override
  Widget build(BuildContext context) {
    final subAccountLabel =
        placeOrderState.isNormalSubAccount ? 'normal' : 'margin';
    final startDate = conditionalBuyState.effectiveTime.startText;
    final endDate = conditionalBuyState.effectiveTime.endText;
    final String activationPrice =
        conditionalBuyState.activationPriceInputState?.text ?? '';
    final String activationPriceSymbol =
        conditionalBuyState.activationPriceType?.getSymbol() ?? '';

    final volume = conditionalBuyState.conditionalVolumeInputState?.text ?? '';
    final price = conditionalBuyState.conditionalPriceInputState?.text ?? '';
    final dilutionType =
        conditionalBuyState.dilutionType?.toStringShowDialog() ?? '';

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(getOrderIcon(), package: WealthConstants.packageName),
        const SizedBox(height: SizeUtils.kSize32),
        Text(
          getTitleDialog(),
          style: vpTextStyle.headine6?.copyWith(
            color: Theme.of(context).textTheme.displayLarge!.color,
          ),
        ),
        const SizedBox(height: SizeUtils.kSize16),
        ConditionalOrderConfirmRowTitle(
          title: getStockLang(StockKeyLang.subAccount),
          content: subAccountLabel,
        ),
        ConditionalOrderConfirmRowTitle(
          title: WealthStock.current.effectiveTime,
          content: '$startDate - $endDate',
          oneRow: true,
        ),
        ConditionalOrderConfirmRowTitle(
          title: WealthStock.current.activationPrice,
          content: '$activationPriceSymbol $activationPrice',
        ),
        ConditionalOrderConfirmRowTitle(
          title: getStockLang(StockKeyLang.volume),
          content: volume,
        ),
        ConditionalOrderConfirmRowTitle(
          title: getStockLang(StockKeyLang.orderPrice),
          content: price,
        ),
        ConditionalOrderConfirmRowTitle(
          title: WealthStock.current.whenHaveEventToDiluteStock,
          content: dilutionType,
        ),
        kSpacingHeight16,
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: themeData.yellow.withOpacity(0.16),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            WealthStock.current.conditionalOrderWaitingConfirmNote,
            style: vpTextStyle.captionRegular?.copyWith(
              color: themeData.gray700,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        kSpacingHeight16,
        DividerWidget(),
        kSpacingHeight16,
        TextWithMultiStyle(
          content: WealthStock.current.conditionalOrderWaitingConfirmGuide,
          textStyle: vpTextStyle.captionRegular?.copyWith(
            color: themeData.gray500,
          ),
          highLightStyle: vpTextStyle.captionRegular?.copyWith(
            color: themeData.black,
          ),
        ),
        kSpacingHeight16,
        Row(
          children: [
            Expanded(
              child: VpsButton.secondarySmall(
                title: WealthStock.current.commandClose,
                onPressed: () => Navigator.pop(context),
              ),
            ),
            const SizedBox(width: SizeUtils.kSize8),
            Expanded(
              child: VpsButton.primarySmall(
                title: getStockLang(StockKeyLang.orderConfirm),
                onPressed: () {
                  context.pop();
                  onConfirm?.call();
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}
