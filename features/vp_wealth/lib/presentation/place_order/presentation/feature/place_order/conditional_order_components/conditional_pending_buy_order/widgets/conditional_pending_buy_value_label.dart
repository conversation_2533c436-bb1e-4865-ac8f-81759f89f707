import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/place_order_selector.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_pending_buy_order/bloc/conditional_pending_order_bloc.dart';

class ConditionalPendingBuyValueLabel extends StatelessWidget {
  final bool center;

  const ConditionalPendingBuyValueLabel({Key? key, this.center = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment:
          center ? CrossAxisAlignment.center : CrossAxisAlignment.start,
      children: [
        BlocBuilder<ConditionalPendingOrderBloc, ConditionalPendingOrderState>(
          buildWhen: (previous, current) =>
              previous.marketCommand != current.marketCommand,
          builder: (context, state) {
            if (state.isNormalPrice) {
              return NewOrderSelector(
                (newOrder) => Text(
                  newOrder
                      ? WealthStock.current.orderValue
                      : WealthStock.current.value,
                  style: vpTextStyle.captionRegular,
                ),
              );
            } else {
              return Text(
                WealthStock.current.expectValue,
                style: vpTextStyle.captionRegular,
              );
            }
          },
        ),
        Expanded(
            child: BlocBuilder<ConditionalPendingOrderBloc,
                ConditionalPendingOrderState>(
          buildWhen: (previous, current) =>
              previous.totalOrderPrice != current.totalOrderPrice,
          builder: (context, state) {
            return ValueSelector(
              (value) => AutoSizeText(
                state.totalOrderPrice.toMoney(),
                style: vpTextStyle.body14?.copyWith(
                  color: value.color,
                ),
                minFontSize: 2,
              ),
            );
          },
        )),
      ],
    );
  }
}
