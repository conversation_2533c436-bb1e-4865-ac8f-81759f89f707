import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_wealth/data/utils/num_extensions.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/exchange.dart';
import 'package:vp_wealth/presentation/place_order/domain/enum/place_order_enum.dart';
import 'package:vp_wealth/presentation/place_order/gen/locale_keys.g.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/state/price_input_state.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_focus_keyboard.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_take_profit_trigger_condition_enum.dart';
import 'package:vp_wealth/presentation/place_order/utils/ext.dart';

part 'take_profit_rate_profit_input_state.dart';

class TakeProfitRateProfitInputCubit
    extends Cubit<TakeProfitRateProfitInputState> {
  TakeProfitRateProfitInputCubit()
      : super(TakeProfitRateProfitInputState.init());
  void onUpdateTriggerCondition(
      ConditionalTakeProfitTriggerConditionEnum triggerCondition) {
    emit(state.copyWith(triggerCondition: triggerCondition));
  }

  @override
  void onChange(Change<TakeProfitRateProfitInputState> change) {
    print(
        'Change OrderPrice: ${change.currentState.orderPrice} to ${change.nextState.orderPrice}');
    super.onChange(change);
  }

  void onUpdateOrderPrice(num orderPrice) {
    print('order Price in Cubit : ${state.orderPrice}');
    print('order Price from Update : ${orderPrice}');
    final stateUpdate = state.copyWith(orderPrice: orderPrice);
    emit(stateUpdate);
  }

  void onChangeRateProfit(String value) {
    final slippageText = value;
    if (slippageText.isEmpty) {
      emit(state.copyWith(clearPrice: true));
    }
    emit(state.copyWith(
      price: slippageText.price,
      formatPrice: false,
      priceNotFormat: slippageText,
    ));
  }

  void onTapDecreaseRateProfit() {
    print(' Price in Cubit : ${state.price}');

    final decreaseSlippage = state.decreasePrice().toDouble();

    emit(state.copyWith(price: decreaseSlippage, formatPrice: true));
  }

  void onTapIncreaseRateProfit() {
    final increaseSlippage = state.increasePrice().toDouble();

    emit(state.copyWith(price: increaseSlippage, formatPrice: true));
  }
}
