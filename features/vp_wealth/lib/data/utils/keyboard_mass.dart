import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/custom_widget/divider_widget.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';

import 'volume_format_utils.dart';

class KeyboardMass extends StatefulWidget {
  final TextEditingController controller;
  final int maxLength;
  final Function(double mass) onChange;
  final bool isMaxValue;

  KeyboardMass({
    required this.controller,
    required this.maxLength,
    required this.onChange,
    this.isMaxValue = false,
  });

  @override
  State<KeyboardMass> createState() => _KeyboardMassState();
}

class _KeyboardMassState extends State<KeyboardMass> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DividerWidget(),
        Padding(
          padding: const EdgeInsets.all(8),
          child: GridView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 2.5,
              crossAxisSpacing: 5,
              mainAxisSpacing: 5,
            ),
            children: getListPhoneKey(),
          ),
        ),
      ],
    );
  }

  List<Widget> getListPhoneKey() {
    var list = [
      _phoneKey(label: "1"),
      _phoneKey(label: "2"),
      _phoneKey(label: "3"),
      _phoneKey(label: "4"),
      _phoneKey(label: "5"),
      _phoneKey(label: "6"),
      _phoneKey(label: "7"),
      _phoneKey(label: "8"),
      _phoneKey(label: "9"),
      _phoneKey(label: "000"),
      _phoneKey(label: "0"),
      _backspaceKey(),
    ];
    return list;
  }

  Widget _phoneKey({required String label}) {
    return Material(
      color: themeData.highlightBg,
      child: InkWell(
        onTap: () {
          if (_isMaxInsert(label)) return;
          _insertText(label);
          widget.onChange(VolumeFormatUtils.getMass(widget.controller.text));
        },
        child: Center(
          child: Text(
            label,
            style: vpTextStyle.body16?.copyWith(color: vpColor.textPrimary),
          ),
        ),
      ),
    );
  }

  Widget _backspaceKey() {
    return Material(
      color: themeData.highlightBg,
      child: InkWell(
        onTap: () {
          _backspaceText();
          widget.onChange(VolumeFormatUtils.getMass(widget.controller.text));
        },
        child: const Icon(Icons.backspace, size: 24),
      ),
    );
  }

  bool _isMaxInsert(String label) {
    final mass = VolumeFormatUtils.getMass(widget.controller.text);
    return (mass.toString().length - 2) + label.length > widget.maxLength;
  }

  void _insertText(String label) {
    String temp = widget.controller.text + label;
    final stringMoney = VolumeFormatUtils.formatMass(
      VolumeFormatUtils.getMass(temp),
      suffix: '',
    );
    widget.controller.text = stringMoney;
    widget.controller.selection = TextSelection.fromPosition(
      TextPosition(offset: widget.controller.text.length),
    );
  }

  void _backspaceText() {
    if (widget.controller.text.isEmpty) {
      return;
    }
    String temp = widget.controller.text.substring(
      0,
      widget.controller.text.length - 1,
    );
    final stringMoney = VolumeFormatUtils.formatMass(
      VolumeFormatUtils.getMass(temp),
      suffix: '',
    );
    widget.controller.text = stringMoney;
    widget.controller.selection = TextSelection.fromPosition(
      TextPosition(offset: widget.controller.text.length),
    );
  }
}
