import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart' hide AppLocalizationDelegate;
import 'package:vp_core/vp_core.dart';
import 'package:vp_wealth/data/model/asset/money_tranfer_result.dart';
// Import all the presentation pages
import 'package:vp_wealth/data/model/assets/item_assets_model.dart';
import 'package:vp_wealth/data/model/plan/plan_model.dart';
import 'package:vp_wealth/data/wealth_repository_impl.dart';
import 'package:vp_wealth/domain/wealth_repository.dart';
import 'package:vp_wealth/generated/intl/messages_all.dart';
import 'package:vp_wealth/generated/l10n.dart';
import 'package:vp_wealth/presentation/place_order/domain/conditional_order_repository_impl.dart';
import 'package:vp_wealth/presentation/place_order/domain/conditional_order_repository.dart';
import 'package:vp_wealth/presentation/place_order/domain/place_order_repository.dart';
import 'package:vp_wealth/presentation/place_order/domain/place_order_repository_impl.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/order_suggest/order_suggest_cubit.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/place_order_bloc.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/bloc/stock_info/stock_info_cubit.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_pending_buy_order/bloc/conditional_pending_order_bloc.dart';
import 'package:vp_wealth/presentation/place_order/presentation/feature/place_order/conditional_order_components/conditional_take_profit_order/bloc/conditional_take_profit_order_bloc.dart';
import 'package:vp_wealth/presentation/place_order/presentation/place_order_page.dart';
import 'package:vp_wealth/presentation/place_order/presentation/router/place_order_arguments.dart';
import 'package:vp_wealth/presentation/wealth_presentation.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/detail_assets_held_screen.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/held_category_screen.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/stock_right/domain/right_off_repository.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/stock_right/domain/right_off_repository_impl.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/stock_right/register_stock_right_page.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/stock_right/service/right_of_stock_service.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/stock_right/stock_right_page.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/category/order/order_buy_investment_page.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/dept/dept_page.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/bank_statement_page.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/history_page.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/mbbank/2.mb_otp/ParamRequestMB.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/mbbank/2.mb_otp/mb_otp_page.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/mbbank/link_mbbank_widget.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/money_cash_in_finish_page_v2.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/money_data/money_repository_impl.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_cash_in_v2/money_domain/money_repository.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/money_cash_in/bank.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_data/money_models/money_cash_in/money_cash_in_input_finish_param.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/money_transfer_page.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/tabs/money/widget/money_transfer_result.dart';
import 'package:vp_wealth/presentation/wealths/assets/details/total_money_screen.dart';
import 'package:vp_wealth/presentation/wealths/intro/wealth_tutorial_page.dart';
import 'package:vp_wealth/presentation/wealths/order/investment_command/accept_command_detail_page.dart';
import 'package:vp_wealth/presentation/wealths/order/investment_command/accept_command_page.dart';
import 'package:vp_wealth/presentation/wealths/order/investment_command/await_command_page.dart';
import 'package:vp_wealth/presentation/wealths/order/investment_command/order_error_page.dart';
import 'package:vp_wealth/presentation/wealths/order/investment_command/order_success_page.dart';
import 'package:vp_wealth/presentation/wealths/plan/create_plan_success_page.dart';
import 'package:vp_wealth/presentation/wealths/plan/cubit/wealth_plan_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/custom_category/category_performance_page.dart';
import 'package:vp_wealth/presentation/wealths/plan/custom_category/my_investment_category_page.dart';
import 'package:vp_wealth/presentation/wealths/plan/info_plan_page.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/detail_plan/action_contract_page.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/detail_plan/category_detail_widget.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/detail_plan/detail_wealth_plan_page.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/edit_plan/check_info_plan_page.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/edit_plan/cubit/edit_wealth_plan_cubit.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/edit_plan/cusom_category/custom_investment_category_page.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/edit_plan/edit_wealth_plan_page.dart';
import 'package:vp_wealth/presentation/wealths/plan/list_plan/investmen_approach_screen.dart';
import 'package:vp_wealth/presentation/wealths/plan/plan_page.dart';
import 'package:vp_wealth/presentation/wealths/plan/support_person/support_person_page.dart';
import 'package:vp_wealth/presentation/wealths/plan/survey/survey_required_page.dart';
import 'package:vp_wealth/presentation/wealths/plan/survey/survey_result_page.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/data/repo/stock_common_repo_impl.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/data/repo/stock_detail_repo_impl.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/domain/repo/stock_detail_repo.dart';
import 'package:vp_wealth/presentation/wealths/stock/details_stock/domain/stock_common_repo.dart';
import 'package:vp_wealth/presentation/wealths/widgets/investment_goals_page.dart';
import 'package:vp_wealth/presentation/wealths/widgets/investment_philosophy_page.dart';
import 'package:vp_wealth/router/wealth_router.dart';

import 'presentation/place_order/domain/enum/order.dart';

class VpWealthModule implements Module {
  @override
  void injectServices(GetIt service) {
    // Register DioManager as a service
    // service.registerLazySingleton<DioManager>(() => DioManager.instance);
    // Register repository with DioManager
    service.registerLazySingleton<WealthRepository>(
      () => WealthRepositoryImpl(
        restDio: service(),
        investDio: service(),
        flexDio: service(),
      ),
    );
    service.registerLazySingleton<PlaceOrderRepository>(
      () => PlaceOrderRepositoryImpl(
        restDio: service(),
        baseDio: service(),
        restSaleSupportDio: service(),
        noAuthDio: service(),
        restKrxDio: service(),
      ),
    );
    service.registerLazySingleton<MoneyRepository>(
      () => MoneyRepositoryImpl(
        restDio: service(),
        baseDio: service(),
        emoneiDio: service(),
      ),
    );
    service.registerLazySingleton<StockCommonRepo>(
      () => StockCommonRepoImpl(
        dio: service(),
        beDio: service(),
        investDio: service(),
        noAuthDio: service(),
      ),
    );
    service.registerLazySingleton<StockDetailRepo>(
      () => StockDetailRepositoryImpl(
        investmentToolDio: service(),
        restDio: service(),
        baseDio: service(),
      ),
    );
    service.registerLazySingleton(() => RightOfStockService(service()));
    service.registerLazySingleton<ConditionalOrderRepository>(
      () => ConditionalOrderRepositoryIml(service()),
    );
    service.registerLazySingleton<RightOffRepository>(
      () => RightOffRepositoryImpl(restClient: service()),
    );
  }

  @override
  List<RouteBase> router() {
    return [
      // Main wealth page
      GoRoute(
        path: WealthRouter.wealthMainPage,
        name: WealthRouter.wealthMainPage,
        builder: (context, state) {
          final indexPage = state.extra as int?;
          return WealthPresentationPage(indexPage: indexPage);
        },
      ),

      // Tutorial
      GoRoute(
        path: WealthRouter.tutorialPage,
        name: WealthRouter.tutorialPage,
        builder: (context, state) => const WealthTutorialPage(),
      ),

      // Investment Goals
      GoRoute(
        path: WealthRouter.investmentGoalsPage,
        name: WealthRouter.investmentGoalsPage,
        builder: (context, state) {
          final planModel = state.extra as PlanModel?;
          return InvestmentGoalsPage(planModel: planModel);
        },
      ),

      // Investment Philosophy
      GoRoute(
        path: WealthRouter.investmentPhilosophyPage,
        name: WealthRouter.investmentPhilosophyPage,
        builder: (context, state) => const InvestmentPhilosophy(),
      ),

      // Plan Page
      GoRoute(
        path: WealthRouter.planPage,
        name: WealthRouter.planPage,
        builder: (context, state) {
          final plan = state.extra as PlanModel?;
          return BlocProvider<WealthPlanCubit>(
            create: (context) => WealthPlanCubit(),
            child: PlanPage(plan: plan),
          );
        },
      ),

      // My Investment Category
      GoRoute(
        path: WealthRouter.myInvestmentCategoryPage,
        name: WealthRouter.myInvestmentCategoryPage,
        builder: (context, state) {
          final cubit = state.extra as WealthPlanCubit;
          return MyInvestmentCategoryPage(cubit: cubit);
        },
      ),

      // Info Plan
      GoRoute(
        path: WealthRouter.infoPlanPage,
        name: WealthRouter.infoPlanPage,
        builder: (context, state) {
          final arg = state.extra as InfoPlanArguments;
          return InfoPlanPage(arg: arg);
        },
      ),

      // Create Plan Success
      GoRoute(
        path: WealthRouter.createPlanSuccessPage,
        name: WealthRouter.createPlanSuccessPage,
        builder: (context, state) {
          final isHaveInitialInvestmentAmount = state.extra as bool;
          return CreatePlanSuccessPage(
            isHaveInitialInvestmentAmount: isHaveInitialInvestmentAmount,
          );
        },
      ),

      // Support Person
      GoRoute(
        path: WealthRouter.supportPersonPage,
        name: WealthRouter.supportPersonPage,
        builder: (context, state) => const SupportPersonPage(),
      ),

      // Survey Required
      GoRoute(
        path: WealthRouter.surveyRequiredPage,
        name: WealthRouter.surveyRequiredPage,
        builder: (context, state) => const SurveyRequiredPage(),
      ),

      // Survey Result
      GoRoute(
        path: WealthRouter.surveyResult,
        name: WealthRouter.surveyResult,
        builder: (context, state) => SurveyResultPage.newInstance(),
      ),

      // Detail Wealth Plan
      GoRoute(
        path: WealthRouter.detailWealthPlanPage,
        name: WealthRouter.detailWealthPlanPage,
        builder: (context, state) {
          final model = state.extra as PlanModel;
          return DetailWealthPlanPage(model: model);
        },
      ),

      // Edit Wealth Plan
      GoRoute(
        path: WealthRouter.editWealthPlanPage,
        name: WealthRouter.editWealthPlanPage,
        builder: (context, state) {
          final cubit = state.extra as EditWealthPlanCubit?;
          if (cubit == null) {
            throw Exception('EditWealthPlanCubit is required for this page');
          }
          return EditWealthPlanPage(cubit: cubit);
        },
      ),

      // Custom Investment Category
      GoRoute(
        path: WealthRouter.customInvestmentCategoryPage,
        name: WealthRouter.customInvestmentCategoryPage,
        builder: (context, state) {
          final cubit = state.extra as EditWealthPlanCubit;
          return CustomInvestmentCategoryPage(cubit: cubit);
        },
      ),

      // Accept Command
      GoRoute(
        path: WealthRouter.acceptCommandPage,
        name: WealthRouter.acceptCommandPage,
        builder: (context, state) {
          final arg = state.extra as AcceptCommandArguments;
          return AcceptCommandPage(arg: arg);
        },
      ),

      // Check Info Plan
      GoRoute(
        path: WealthRouter.checkInfoPlanPage,
        name: WealthRouter.checkInfoPlanPage,
        builder: (context, state) {
          final arg = state.extra as CheckInfoPlanArguments;
          return CheckInfoPlanPage(arg: arg);
        },
      ),

      // Detail Assets Held
      GoRoute(
        path: WealthRouter.detailAssetsHeldScreen,
        name: WealthRouter.detailAssetsHeldScreen,
        builder: (context, state) {
          final model =
              state.extra is ItemAssetsModel
                  ? state.extra as ItemAssetsModel
                  : ItemAssetsModel.fromJson(
                    state.extra as Map<String, dynamic>,
                  );
          return DetailAssetsHeldScreen(model: model);
        },
      ),

      // Held Category
      GoRoute(
        path: WealthRouter.heldCategoryScreen,
        name: WealthRouter.heldCategoryScreen,
        builder: (context, state) {
          final model =
              state.extra is ItemAssetsModel
                  ? state.extra as ItemAssetsModel
                  : ItemAssetsModel.fromJson(
                    state.extra as Map<String, dynamic>,
                  );
          return HeldCategoryScreen(model: model);
        },
      ),

      // Total Money
      GoRoute(
        path: WealthRouter.totalMoneyScreen,
        name: WealthRouter.totalMoneyScreen,
        builder: (context, state) {
          final model =
              state.extra is ItemAssetsModel
                  ? state.extra as ItemAssetsModel
                  : ItemAssetsModel.fromJson(
                    state.extra as Map<String, dynamic>,
                  );
          return TotalMoneyScreen(model: model);
        },
      ),

      // History
      GoRoute(
        path: WealthRouter.historyPage,
        name: WealthRouter.historyPage,
        builder: (context, state) {
          final model =
              state.extra is ItemAssetsModel
                  ? state.extra as ItemAssetsModel
                  : ItemAssetsModel.fromJson(
                    state.extra as Map<String, dynamic>,
                  );
          return WealthHistoryPage(model: model);
        },
      ),

      // State of Money
      GoRoute(
        path: WealthRouter.stateOfMoneyPage,
        name: WealthRouter.stateOfMoneyPage,
        builder: (context, state) {
          final model =
              state.extra is ItemAssetsModel
                  ? state.extra as ItemAssetsModel
                  : ItemAssetsModel.fromJson(
                    state.extra as Map<String, dynamic>,
                  );
          return BankStatementPage(model: model);
        },
      ),

      // Money Transfer
      GoRoute(
        path: WealthRouter.moneyTransferPage,
        name: WealthRouter.moneyTransferPage,
        builder: (context, state) {
          final model =
              state.extra is ItemAssetsModel
                  ? state.extra as ItemAssetsModel
                  : ItemAssetsModel.fromJson(
                    state.extra as Map<String, dynamic>,
                  );
          return WealthMoneyTransferPage(model: model);
        },
      ),

      // Dept Page
      GoRoute(
        path: WealthRouter.deptPage,
        name: WealthRouter.deptPage,
        builder: (context, state) {
          final model =
              state.extra is ItemAssetsModel
                  ? state.extra as ItemAssetsModel
                  : ItemAssetsModel.fromJson(
                    state.extra as Map<String, dynamic>,
                  );
          return DeptPage(model: model);
        },
      ),

      // Order Buy Investment
      GoRoute(
        path: WealthRouter.orderBuyInvestmentPage,
        name: WealthRouter.orderBuyInvestmentPage,
        builder: (context, state) {
          final model =
              state.extra is ItemAssetsModel
                  ? state.extra as ItemAssetsModel
                  : ItemAssetsModel.fromJson(
                    state.extra as Map<String, dynamic>,
                  );
          return OrderBuyInvestmentPage(model: model);
        },
      ),

      // Order Success
      GoRoute(
        path: WealthRouter.orderSuccessPage,
        name: WealthRouter.orderSuccessPage,
        builder: (context, state) => const OrderSuccessPage(),
      ),

      // Order Error
      GoRoute(
        path: WealthRouter.orderErrorPage,
        name: WealthRouter.orderErrorPage,
        builder: (context, state) {
          final messageError = state.extra as String?;
          return OrderErrorPage(messageError: messageError);
        },
      ),

      // Stock Right
      GoRoute(
        path: WealthRouter.stockRightPage,
        name: WealthRouter.stockRightPage,
        builder: (context, state) {
          final model =
              state.extra is ItemAssetsModel
                  ? state.extra as ItemAssetsModel
                  : ItemAssetsModel.fromJson(
                    state.extra as Map<String, dynamic>,
                  );
          return StockRightPage(model: model);
        },
      ),

      // Register Stock Right
      GoRoute(
        path: WealthRouter.registerStockRightPage,
        name: WealthRouter.registerStockRightPage,
        builder: (context, state) {
          final argument = state.extra as RegisterStockRightArgument;
          return RegisterStockRightPage(argument: argument);
        },
      ),

      // Action Contract
      GoRoute(
        path: WealthRouter.actionContractPage,
        name: WealthRouter.actionContractPage,
        builder: (context, state) {
          final argument = state.extra as ActionContractArgument;
          return ActionContractPage(argument: argument);
        },
      ),

      // Category Performance
      GoRoute(
        path: WealthRouter.categoryPerformancePage,
        name: WealthRouter.categoryPerformancePage,
        builder: (context, state) {
          final wealthPlanCubit = state.extra as WealthPlanCubit;
          return CategoryPerformancePage(wealthPlanCubit: wealthPlanCubit);
        },
      ),

      // Await Command
      GoRoute(
        path: WealthRouter.awaitCommandPage,
        name: WealthRouter.awaitCommandPage,
        builder: (context, state) {
          final commandId = state.extra as int;
          return AwaitCommandPage(commandId: commandId);
        },
      ),

      // Accept Command Detail
      GoRoute(
        path: WealthRouter.acceptCommandDetailPage,
        name: WealthRouter.acceptCommandDetailPage,
        builder: (context, state) {
          final arg = state.extra as AcceptCommandDetailArguments;
          return AcceptCommandDetailPage(arg: arg);
        },
      ),

      // Category Detail
      GoRoute(
        path: WealthRouter.categoryDetailWidget,
        name: WealthRouter.categoryDetailWidget,
        builder: (context, state) {
          final params = state.extra as CategoryDetailParams;
          return CategoryDetailWidget(params: params);
        },
      ),

      // Investment Approach
      GoRoute(
        path: WealthRouter.investmenApproachScreen,
        name: WealthRouter.investmenApproachScreen,
        builder: (context, state) => InvestmenApproachScreen.newInstance(),
      ),

      // Money Cash In Finish V2
      GoRoute(
        path: WealthRouter.moneyCashInFinishV2,
        name: WealthRouter.moneyCashInFinishV2,
        builder: (context, state) {
          final param = state.extra as MoneyCashInInputFinishParam;
          return MoneyCashInFinishPageV2(param: param);
        },
      ),

      // Money MB Bank Link Account
      GoRoute(
        path: WealthRouter.moneyMBBankLinkAccount,
        name: WealthRouter.moneyMBBankLinkAccount,
        builder: (context, state) {
          final bank = state.extra as Bank;
          return LinkMBBankWidget(bank: bank);
        },
      ),

      // MB OTP Page
      GoRoute(
        path: WealthRouter.mbOtpPage,
        name: WealthRouter.mbOtpPage,
        builder: (context, state) {
          final paramRequestMB = state.extra as ParamRequestMB?;
          return MbOtpPage(paramRequestMB: paramRequestMB);
        },
      ),
      GoRoute(
        path: WealthRouter.moneyTransferResult,
        name: WealthRouter.moneyTransferResult,
        builder: (context, state) {
          return MoneyTransferResult(
            model: state.extra as MoneyTranferResultModel,
          );
        },
      ),
      GoRoute(
        path: WealthRouter.placeOrder,
        name: WealthRouter.placeOrder,
        builder: (context, state) {
          return MultiBlocProvider(
            providers: [
              BlocProvider<PlaceOrderBloc>(
                create:
                    (_) => PlaceOrderBloc(
                      symbol: (state.extra as PlaceOrderArguments?)?.symbol,
                      order:
                          (state.extra as PlaceOrderArguments?)?.order ??
                          Order.buy,
                      wealthModel:
                          (state.extra as PlaceOrderArguments?)?.wealthModel,
                      subAccount:
                          (state.extra as PlaceOrderArguments?)?.subAccountType,
                      repository: GetIt.instance.get<PlaceOrderRepository>(),
                      recommendDetailsOrder:
                          (state.extra as PlaceOrderArguments?)
                              ?.recommendDetailsOrder,
                      recommendationDetailModel:
                          (state.extra as PlaceOrderArguments?)
                              ?.recommendationDetailModel,
                    )..add(InitEvent()),
              ),
              // BlocProvider<SetupConfigurationAccountBloc>(
              //   create: (_) => SetupConfigurationAccountBloc(),
              // ),
              BlocProvider<ConditionalPendingOrderBloc>(
                create:
                    (_) => ConditionalPendingOrderBloc(
                      repository:
                          GetIt.instance.get<ConditionalOrderRepository>(),
                    ),
              ),
              BlocProvider<ConditionalTakeProfitOrderBloc>(
                create:
                    (_) => ConditionalTakeProfitOrderBloc(
                      GetIt.instance.get<ConditionalOrderRepository>(),
                    ),
              ),
              BlocProvider<StockInfoCubit>(create: (_) => StockInfoCubit()),
              BlocProvider<OrderSuggestCubit>(
                create:
                    (_) => OrderSuggestCubit(
                      wealthModel:
                          (state.extra as PlaceOrderArguments?)?.wealthModel,
                    ),
              ),
              // BlocProvider<ConditionNormalCubit>(
              //   create: (_) => ConditionNormalCubit(),
              // ),
              // BlocProvider<ConditionCommandCubit>(
              //   create: (_) => ConditionCommandCubit(),
              // ),
              // BlocProvider<CommandGtcBloc>(create: (_) => CommandGtcBloc()),
              // BlocProvider<SetupConfigurationTransactionBloc>(
              //   create: (_) => SetupConfigurationTransactionBloc(),
              // ),
            ],
            child: const PlaceOrderPage(),
          );
        },
      ),
    ];
  }

  @override
  List<LocalizationsDelegate> localizationsDelegates() {
    return [MultiLocalizationsDelegate.delegate];
  }

  @override
  String modulePath() {
    return "vp_wealth";
  }
}

class MultiLocalizationsDelegate extends AppLocalizationDelegate {
  const MultiLocalizationsDelegate();

  static const AppLocalizationDelegate delegate = MultiLocalizationsDelegate();

  @override
  Future<WealthStock> load(Locale locale) {
    return MultipleLocalizations.load(
      initializeMessages,
      locale,
      (l) => WealthStock.load(locale),
      setDefaultLocale: true,
    );
  }
}
