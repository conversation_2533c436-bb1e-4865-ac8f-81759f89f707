{"@@locale": "en", "category": "Category", "market": "Market", "utilities": "Utilities", "setCommand": "Set command", "noData": "<PERSON><PERSON><PERSON> tại không có dữ liệu", "priceList": "Price list", "warrants": "Warrants", "etf": "ETF", "bondRL": "Bonds RL", "topStocks": "Top stocks", "stock": "Stock", "offerList": "Offer list", "offerForSaleList": "Offer for sale list", "stockFilter": "Stock filter", "price": "Giá", "searchStocks": "Search stocks", "searchStocks2": "Search securities", "addWatchList": "Add to watch list", "watchList": "Watch list", "selfCreatedDirectory": "Self-created directory", "newCategory": "New category", "addToCategory": "Add to category", "addNewCategory": "Add new category", "descAddNewCategory": "Name your new list", "add": "Add", "nameCategory": "Name category", "added": "Added", "toTheList": "to the list", "noStock": "The list has no codes yet", "validateNameCategory": "Please enter a category name", "nameAlreadyExists": "Name already exists", "editCategory": "Category updated successfully", "removeCategory": "Deleted category", "noHoldingPortfolio": "You haven't held any shares yet", "noHoldingPortfolioForSell": "You haven't owned any sellable stocks yet", "averagePrice": "Average price", "marketPrice": "Market price", "profitLoss": "Profit/Loss", "profitLossExpected": "Expected profit/loss", "totalCapital": "Total capital", "availableVolume": "KL khả dụng", "otherVolume": "Other volume", "volumeWaiting": "Buying awaiting volume", "totalVolume": "Total volume", "totalValue": "Total value", "addCode": "Add code", "marketPriceAcr": "MP", "addedToList": "Added to list", "expectedProfit": "Expected profit", "totalMass": "Total mass", "capitalCost": "Capital cost", "capitalValue": "Capital value", "expectedProfitLoss": "Expected profit/loss", "totalAmountShares": "Total number of shares", "averageCostPrice": "Average cost price", "profitLossToday": "Profit/loss today", "expected": "Expected", "totalProfitLoss": "Total profit/loss", "numberSharesTraded": "Shares traded", "numberBlockedShares": "Blocked shares", "plan": "Plan", "target": "Target", "targetInvestment": "Target Investment", "buttonContinue": "<PERSON><PERSON><PERSON><PERSON>", "buttonClose": "Close", "buttonAccept": "Accept", "buttonSaveChange": "Save change", "buttonSeeDetails": "See details", "buttonBack": "Back", "comingSoon": "Coming soon", "allocationRules": "Allocation rules 50/30/20", "allocationRulesContent": "Rule 50/30/20 is a popular rule recommended for effective personal financial management. Accordingly, 50% of income is spent on essential spending needs, 30% of income is used to serve personal needs, and the remaining 20% of income will be used for savings and investments. VPBankS recommends that customers allocate at least 20% of their monthly income to accumulated savings and investments, in order to achieve their future personal financial goals.", "hintTextSearchStockSymbol": "Search for stock symbols", "hintTextSearchPlan": "Search for plans", "hintTextEnterAmount": "Enter amount", "hintTextEnterYearNumber": "Enter the year number", "hintTextEnterPlanName": "Enter a plan name", "emptyListFilterPlanWhenNoResultMatching": "There are no matching results", "emptyListSearchWhenSymbolExist": "The stock symbol already exists in your portfolio.\nPlease refer to additional stocks\nselected by VPBankS.", "emptyListSearchWhenNoResultMatching": "There are no matching results.\nPlease refer to the list of stocks\nselected by VPBankS.", "symbolSuggestionByVPBankS": "This is a list of stocks selected by VPBankS.", "titlePlanName": "Plan name", "initialInvestmentAmount": "Initial investment amount", "initialInvestmentAmountAnd": "And Initial investment amount", "periodicInvestmentAmount": "Periodic investment amount", "noteAboutPeriodicInvestmentAmount": "The recommended minimum investment allocation is 20% of total income.", "noteAboutInvestmentTime": "Minimum investment period is 1 year, maximum is 50 years.", "investmentTime": "Investment time", "investmentFrequency": "Investment frequency", "chooseInvestmentFrequency": "Choose investment frequency", "periodicInvestmentStartDate": "Periodic investment start date", "monthlyInvestmentDay": "The monthly recurring investment date is the date", "investmentCategory": "Investment category", "createInvestmentTaste": "Create your own investment taste", "expectedGrowth": "Expected growth", "editPlanName": "Edit plan name", "messageErrorWhenEnterPlanName": "Please enter a name for the plan", "chooseTarget": "Choose target", "wealthProductTitle": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON> sản", "wealthProductDes": "<PERSON><PERSON> sản phẩm VPBankS hỗ trợ và đồng hành cùng quý khách hàng trên chặng đường đầu tư và tích lũy tài sản nhằm đạt được các mục tiêu tài chính mong muốn.Bắt đầu ngay hôm nay cùng VPBankS!", "wealthStart": "Start", "wealth": "Wealth", "investmentGoals": "Investment Goals", "investmentPhilosophy": "Investment Philosophy", "whatAreYourInvestmentGoals": "What are your investment goals?", "howDoYouAlignWithAnyInvestmentPhilosophy": "How do you align with any investment philosophy?", "descriptionInvestmentPhilosophy": "<PERSON><PERSON> xây dựng chiến lư<PERSON><PERSON> đầu tư tích sản phù hợ<PERSON>, cùng VPBankS thực hiện bộ khảo sát để tìm ra phong cách đầu tư và danh mục tích sản bền vững.", "skip": "<PERSON><PERSON>", "startSurvey": "Start Survey", "nextQuestion": "The next question", "redo": "Redo", "wealContinue": "Continue", "wealthPlan": "Plan", "myCategory": "My category", "cancelPlan": "Cancel wealth plan", "cancelPlanPage": "Cancel wealth plan", "cancelCreatePlan": "Cancel create plan", "areYouCancelCreatePlan": "Are you sure want to cancel\nwealth plan?", "bondsfunds": "Bonds/Bond Funds:", "stocksStockFunds": "Stocks/Stock Funds:", "cashMoney": "Cash/Money", "cancelSurvey": "Cancel Survey", "cancelSurveyNew": "Cancel Survey", "strOk": "OK", "rateInvestmentAmount": "", "emptyStockCategory": "No Stock on category", "suggestedByVPBankS": "Suggestion by VPBankS", "suggestedByVPBankSContent": "", "addStock": "", "confirmPlan": "Confirm Plan", "coutionInfoPlan": "Note: Investment amount less than market price of stock code will affect the order placement of the plan.", "heldAsset": "Held Assets", "asset": "Assets", "inDebit": "Debit", "amountInitialStart": "Amount Initial Start", "percenCompleted": "Percent Completed", "operational": "Operational", "moneyCategory": "Money Category", "completed": "Completed", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "active": "Active", "stopWealth": "Stop Wealth", "allocationPlan": "Allocation Plan", "periodicInvestmentDate": "Periodic Investment Date", "nextInvestmentPeriod": "Next Investment Period", "doYouWantToStopInvesting": "Do you want to stop investing?", "no": "No", "yesStopInvesting": "Yes, stop investing", "continueInvesting": "Continue investing", "yesContinueInvesting": "Yes, continue investing", "termsAndConditions": "Terms and Conditions", "scheduleInvestment": "Schedule Investment", "downloading": "Downloading", "downloadSuccess": "Download Success", "downloadFail": "Download Fail", "filterDataEmpty": "There are no results matching the filter. Please change the filter condition and try again", "orderFundCertificates": "Order fund certificates", "retry": "Retry", "search": "Search", "apply": "Apply", "reset": "Reset", "inputAnswer": "Enter your answer", "bil": "billion", "mil": "mil", "k": "k", "ts": "TS", "tv": "TV", "tt": "TT", "moneySource": "Money source", "selectLinkingAccount": "Select linking account", "availableBalances": "Balances", "cashInTitle": "CashIn", "minValueTransfer": "The minimum transfer amount is 1 VND. Please try again.", "maxMoneyTransfer": "Maximum transfer amount", "overTransferMoney": "The account balance is not enough to make the transaction", "feeTransfer": "Money Transfer Fee", "fee": "Fee", "linkedAccount": "Linked account", "unLink": "unLink", "deceptionUnlink": "Do you unregister linking", "confirm": "Confirm", "bankLinking": "Bank linking", "notificationCashIn": "Notification", "accountIsNotLinkedToTheDelegation": "Account is not linked to the delegation. Contact ********** for assistance.", "anErrorOccurred": "An error occurred", "titleErrorBank": "Bank had linked", "linkAccount": "<PERSON><PERSON><PERSON> kết tài k<PERSON>n", "cancelLinkingAccountSuccess": "<PERSON><PERSON><PERSON> liên kết tài khoản thành công", "cancelLinkingAccountFail": "<PERSON><PERSON><PERSON> liên kết tài khoản không thành công", "deceptionLinkAccount": "Bạn sẽ được chuyển đến trang web của ngân hàng đối tác. Bạn có muốn tiếp tục không?", "systemSendOTP": "The system has sent OTP to your phone number. Please enter the OTP received", "mpOtp": "OTP code is sent from MBBank to phone number", "rate": "Rate", "historyStockCode": "Stock code", "stockType": "Stock type", "commandType": "Command type", "orderPrice": "Order price", "orderTime": "Order time", "jointVolume": "Joint Volume", "averageMatchingPrice": "Average matching price", "orderValue2": "Order value", "cancelOrder": "Cancel order", "ordinary": "TK thường", "ordinaryFull": "<PERSON><PERSON><PERSON><PERSON> thư<PERSON>", "deposit": "TK ký quỹ", "depositFull": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>n ký quỹ", "maxBuyVolume": "KL tối đa", "addedSymbolFollowList": "<PERSON><PERSON> thêm vào danh sách Mã CK đang chọn", "notAddSymbolFollowList": "<PERSON><PERSON>ê<PERSON> vào danh sách Mã CK đang chọn thất bại", "allSymbol": "<PERSON><PERSON>t cả mã", "followList": "<PERSON><PERSON> s<PERSON>ch theo dõi", "oddLot": "Lô lẻ", "evenLot": "Lô Chẵn", "processingOrder": "l<PERSON><PERSON> đang xử lý", "placeVolume": "KL đặt", "symbol": "Mã", "priceOrder": "Giá đặt", "tryNow": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON> ngay", "newOrderInterfaceIntro1": "<PERSON><PERSON>o <PERSON> đặt lệnh nâng cao mới", "newOrderInterfaceIntro2": "<PERSON><PERSON> thay đổi giao di<PERSON>n đặt lệnh truy cập", "newOrderInterfaceIntro3": "C<PERSON>i đặt -> <PERSON><PERSON><PERSON> hình giao dịch", "allowMultiSelect": "<PERSON> phép chọn nhi<PERSON>u mã", "selectedSymbols": "<PERSON><PERSON> chứng kho<PERSON> đang chọn", "deleteAll": "<PERSON><PERSON><PERSON> tất cả", "deleteAllSelected": "<PERSON>oá tất cả cổ phiếu đã chọn", "confirmDeleteAll": "Bạn có muốn xóa tất cả cổ phiếu trong danh sách đã chọn không?", "selectedSymbolDeleted": "Đã xoá tất cả mã CK đang chọn", "searchSymbol": "<PERSON><PERSON><PERSON> mã", "noOrder": "<PERSON><PERSON><PERSON><PERSON> có lệnh", "pleaseSelectMax": "<PERSON><PERSON> lòng chọn tối đa {} mã", "orderValue": "<PERSON><PERSON><PERSON> trị l<PERSON>nh", "overbought": "<PERSON><PERSON> mua", "oversold": "<PERSON><PERSON> b<PERSON>", "wrongTransactionPrice": "<PERSON><PERSON><PERSON> giao dịch phải nằm trong khoảng trần - sàn", "wrongStepPrice": "Sai bư<PERSON>c giá", "invalidOutOfRange": "<PERSON><PERSON><PERSON> nh<PERSON>p phải nằm trong khoảng giá trần và giá sàn", "invalidStep10": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> không hợp lệ phải chia hết cho 10 đ", "invalidStep50": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> không hợp lệ phải chia hết cho 50 đ", "invalidStep100": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> không hợp lệ phải chia hết cho 100 đ", "invalidMinMass": "<PERSON>h<PERSON><PERSON> lượng tối thiểu 100 và phải là bội số của 100", "invalidMaxVolume": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>i lượng tối đa là", "invalidMaxBuy": "<PERSON><PERSON><PERSON><PERSON> quá sức mua của tiểu k<PERSON>n", "invalidMaxSell": "Bạn không nắm giữ cổ phiếu này", "invalidOddLot": "<PERSON><PERSON><PERSON> d<PERSON>ch lô lẻ chỉ sử dụng Lệnh LO", "priceCeiling": "T<PERSON>ầ<PERSON>", "priceFloor": "Sàn", "pricePrice": "<PERSON><PERSON><PERSON>", "priceLow": "<PERSON><PERSON><PERSON><PERSON>", "priceHigh": "<PERSON>", "less": "Nhỏ hơn", "bigger": "<PERSON><PERSON><PERSON>", "volumeKL": "KL", "activationPrice": "<PERSON><PERSON><PERSON> k<PERSON>", "whenHaveEventToDiluteStock": "<PERSON>hi có sự kiện quyền pha loãng giá cổ phiếu", "waitingCommand": "<PERSON><PERSON><PERSON> chờ", "stopLossCommand": "<PERSON><PERSON><PERSON> c<PERSON>t lỗ", "takeProfitCommand": "<PERSON><PERSON><PERSON> chốt lời", "dilutionChoiceTitle": "<PERSON>hi có sự kiện quyền pha loãng giá cổ phiếu", "autoAdjustTriggerPriceAndSetPrice": "Tự động điều chỉnh giá kích hoạt & giá đặt", "pendingBuyCommand": "<PERSON><PERSON><PERSON> chờ mua", "pendingSellCommand": "<PERSON><PERSON><PERSON> chờ bán", "conditionalOrderWaitingConfirmNote": "<PERSON>ệnh chỉ đượ<PERSON> kích hoạt 01 lần duy nhất trong thời gian hiệu lực.", "conditionalOrderWaitingConfirmGuide": "Bằng việc %&<PERSON><PERSON><PERSON> nhận lệnh%&, bạn hiểu và chấp thuận với các lệnh giao dịch  được phát sinh khi thỏa mãn các điều kiện kích hoạt.", "averageCostPriceConditional": "<PERSON><PERSON><PERSON> vốn bình quân", "holdVolume": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> n<PERSON>m giữ", "slippageMargin": "<PERSON><PERSON><PERSON><PERSON> giá", "triggerCondition": "<PERSON><PERSON><PERSON><PERSON> ki<PERSON>n k<PERSON><PERSON> ho<PERSON>", "takeProfitRatePercent": "Tỷ l<PERSON> chốt lời (%)", "profitMargin": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> chốt lời", "stopLossMargin": "<PERSON><PERSON><PERSON>n giá cắt lỗ", "stopLossRatePercent": "Tỷ lệ cắt lỗ (%)", "stopLossRate": "Tỷ lệ cắt lỗ", "pendingOrderCommandDescription": "<PERSON><PERSON><PERSON> đượ<PERSON> kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo điều kiện NĐT cài đặt", "takeProfitCommandDescription": "<PERSON><PERSON><PERSON> bán đượ<PERSON> kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo kỳ vọng chốt lời của NĐT", "stopLossCommandDescription": "<PERSON><PERSON><PERSON> bán đượ<PERSON> kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động đến điểm cắt lỗ của NĐT", "invalidRateProfit": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>p l<PERSON>", "invalidInput": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>p l<PERSON>", "invalidSlipPagePrice": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> không hợp lệ", "youNotHoldingThisStock": "Bạn không nắm giữ mã chứng khoán này", "canNotEditCommand": "<PERSON><PERSON><PERSON><PERSON> thể sửa lệnh", "canNotEditCommandWarning": "Bạn không thể sửa lệnh do không còn nắm giữ\nmã chứng khoán", "noteCanNotEditCommand": "Bạn không thể sửa lệnh do không còn nắm giữ mã chứng khoán", "pleaseInputVolume": "<PERSON><PERSON> lòng nhập kh<PERSON>i l<PERSON>", "priceSlippageInvalid": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> gi<PERSON> không hợp lệ", "maxVolumeIs": "<PERSON><PERSON><PERSON><PERSON> lượng tối đa là", "validateVolume": "<PERSON><PERSON><PERSON><PERSON> lư<PERSON> không hợp lệ", "expectValue": "<PERSON><PERSON><PERSON> trị dự kiến", "invalidConditionalPrice": "Gi<PERSON> đặt không hợp lệ", "invalidActivationPrice": "<PERSON><PERSON><PERSON> k<PERSON>ch ho<PERSON>t không hợp lệ", "cannotOrderOddLotWithMarketCommand": "<PERSON><PERSON><PERSON><PERSON> thể đặt lô lẻ với lệnh thị trường", "volumeMustEvenLot": "<PERSON><PERSON><PERSON><PERSON> lượng tối thiểu 100 và là bội số 100", "cancelAll": "<PERSON><PERSON><PERSON> tất cả lệnh", "noFilter": "<PERSON><PERSON><PERSON><PERSON> có kết quả nào phù hợp với bộ lọc. Vui lòng thay đổi điều kiện lọc và thử lại", "contactCustomerService": "<PERSON><PERSON><PERSON> DVKH", "priceThresholdDialogTitle": "<PERSON><PERSON> nh<PERSON>c phương án đầu tư", "priceThresholdDialogContent": "Tiềm năng tăng giá danh mục hiện tại nhỏ hơn 10% so với giá kỳ vọng. Quý khách cân nhắc trước khi đầu tư theo danh mục!", "stopLoss": "Cắt lỗ", "takeProfit": "<PERSON><PERSON><PERSON> lời", "purchasingAbility": "<PERSON><PERSON><PERSON> mua", "marginRate": "Tỷ lệ vay", "volume": "<PERSON><PERSON><PERSON><PERSON>", "back": "Quay lại", "value": "<PERSON><PERSON><PERSON> trị", "effectiveTime": "<PERSON><PERSON><PERSON><PERSON> gian hi<PERSON> l<PERSON>c", "effectiveDate": "<PERSON><PERSON><PERSON>", "successOrderPlace": "Đặt lệnh thành công", "pleaseEnterPrice": "<PERSON><PERSON> lòng điền giá", "vnd": "đ", "showMessageNextTime": "<PERSON><PERSON><PERSON> thị thông báo này trong lần sau", "commandClose": "Đ<PERSON><PERSON>", "commandDialogCancel": "<PERSON><PERSON><PERSON> nhận dừng đặt lệnh", "orderConfirmLate": "Để sau", "notConfirmCBTTClose": "<PERSON><PERSON><PERSON><PERSON> báo", "orderTypeLo": "<PERSON><PERSON><PERSON>", "orderTypeBuyin": "Lệnh Buy-in", "orderTypeAto": "Lệnh ATO", "orderTypeAtc": "Lệnh ATC", "orderTypeMp": "<PERSON><PERSON><PERSON>", "orderTypeMak": "Lệnh MAK", "orderTypeMok": "Lệnh MOK", "orderTypeMtl": "Lệnh MTL", "orderTypePlo": "Lệnh PLO", "orderTypeCondition": "<PERSON><PERSON><PERSON> đi<PERSON> kiện", "orderTypeGtc": "Lệnh GTC", "orderDescriptionAto": "<PERSON><PERSON><PERSON> tranh mua bán tại mức giá mở cửa", "orderDescriptionMp": "<PERSON><PERSON><PERSON> đặt mua - b<PERSON> chứng khoán với bất kỳ mức giá nào trong thời gian giao dịch", "orderDescriptionMak": "<PERSON><PERSON><PERSON> thị trường khớp và huỷ tức có thể thực hiện toàn bộ hoặc một phần phần còn lại sẽ bị huỷ ngay sau khi khớp lệnh", "orderDescriptionMok": "<PERSON><PERSON><PERSON> thị trường khớp toàn bộ hoặc huỷ nếu không thực hiện được toàn bộ thì bị huỷ ngay sau khi nhập", "orderDescriptionMtl": "<PERSON><PERSON><PERSON> thị trường giới hạn nếu không thực hiện được toàn bộ thì phần còn lại chuyển thành lệnh LO và áp dụng các quy định về sửa huỷ đối với lệnh LO", "orderDescriptionLo": "<PERSON><PERSON><PERSON> đặt mua - b<PERSON> chứng khoán theo giá mong muốn", "orderDescriptionBuyin": "<PERSON><PERSON><PERSON> bán áp dụng với các mã chứng khoán trong phiên Buy-in từ 8h45 - 9h00", "orderDescriptionAtc": "<PERSON><PERSON><PERSON> mua hoặc lệnh bán được thực hiện tại thời điểm giá đang đóng cửa", "orderDescriptionPlo": "<PERSON><PERSON><PERSON> mua hoặc bán chứng khoán tại mức giá đóng cửa sau khi kết thúc phiên ATC", "orderDescriptionGtc": "<PERSON><PERSON><PERSON> đượ<PERSON> kích hoạt khi giá đặt lệnh nằm trong khoảng trần - sàn của ngày giao dịch có hiệu lực trong khoảng thời gian thiết lập", "typeCommand": "<PERSON><PERSON><PERSON>", "codeTitle": "Mã CK", "bidPriceTitle": "Giá đặt/\nGiá khớp", "volTitle": "KL đặt/\nKL khớp", "statusTitle": "<PERSON><PERSON><PERSON><PERSON> thái", "noDataFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy dữ liệu", "date": "<PERSON><PERSON><PERSON>", "month": "th<PERSON>g", "itm": "<PERSON><PERSON> l<PERSON>", "atm": "<PERSON><PERSON><PERSON> vốn", "otm": "<PERSON><PERSON> lỗ", "money": "<PERSON><PERSON><PERSON><PERSON>", "share": "<PERSON><PERSON> phi<PERSON>u", "individual": "Cá nhân", "organization": "<PERSON><PERSON> chức", "revenue": "<PERSON><PERSON>h thu", "profit": "<PERSON><PERSON><PERSON>", "shortExrightDate": "Ngày GDKHQ", "exrightDate": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> không hưởng quyền", "publicDate": "<PERSON><PERSON><PERSON> c<PERSON> bố", "issueDate": "<PERSON><PERSON><PERSON>n"}