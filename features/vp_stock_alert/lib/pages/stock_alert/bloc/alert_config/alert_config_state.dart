part of 'alert_config_cubit.dart';

class AlertConfigState extends Equatable {
  final StockAlertEntity current;
  final StockAlertEntity edit;
  final ConfigTab configTab;

  const AlertConfigState({
    required this.current,
    required this.edit,
    this.configTab = ConfigTab.volatility,
  });

  /// copyWith
  AlertConfigState copyWith({
    StockAlertEntity? current,
    StockAlertEntity? edit,
    ConfigTab? configTab,
  }) {
    return AlertConfigState(
      current: current ?? this.current,
      edit: edit ?? this.edit,
      configTab: configTab ?? this.configTab,
    );
  }

  AlertConfigState copyWithEditMutation({
    int? overTopIndex,
    PriceTimePeriod? overTop,
    int? bottomOutIndex,
    PriceTimePeriod? bottomOut,
    AlertItemEntity? setting,
  }) {
    final newMutation = edit.mutation.copyWith(
      overTopIndex: overTopIndex,
      overTop: overTop,
      bottomOutIndex: bottomOutIndex,
      bottomOut: bottomOut,
      setting: setting,
    );

    return AlertConfigState(
      edit: edit.copyWith(mutation: newMutation),
      current: current,
      configTab: configTab,
    );
  }

  AlertConfigState copyWithEditMutationSetting({
    ConfigTab? category,
    bool? status,
    String? fromDate,
    String? toDate,
    AlertFrequency? frequency,
    int? index,
  }) {
    final newMutation = edit.mutation.copyWith(
      setting: edit.mutation.setting.copyWith(
        category: category,
        status: status,
        frequency: frequency,
        fromDate: fromDate,
        toDate: toDate,
        index: index,
      ),
    );

    return AlertConfigState(
      edit: edit.copyWith(mutation: newMutation),
      current: current,
      configTab: configTab,
    );
  }

  /// computed properties
  String get criteriaText => '${edit.criteria}/${StockAlertCubit.maxCriteria}';

  bool get criteriaMaxInValid => edit.criteria > StockAlertCubit.maxCriteria;

  Color get criteriaTextColor =>
      criteriaMaxInValid ? themeData.red : themeData.primary;

  bool get hasChange =>
      (current.matchingPrice != edit.matchingPrice) &&
          (current.matchingPrice.hasConfig || edit.matchingPrice.hasConfig) ||
      (current.volatility != edit.volatility) &&
          (current.volatility.hasConfig || edit.volatility.hasConfig) ||
      (current.mutation != edit.mutation) &&
          (current.mutation.hasConfig || edit.mutation.hasConfig) ||
      (current.trend != edit.trend) &&
          (current.trend.hasConfig || edit.trend.hasConfig) ||
      (current.volume != edit.volume) &&
          (current.volume.hasConfig || edit.volume.hasConfig);

  bool get isEnableSave =>
      hasChange && edit.validate && edit.hasConfig && !criteriaMaxInValid;

  /// Equatable
  @override
  List<Object?> get props => [current, edit, configTab];
}
