import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_alert/generated/l10n.dart';
import 'package:vp_stock_alert/model/enum/config_tab.dart';
import 'package:vp_stock_alert/model/stock_alert_entity.dart';
import 'package:vp_stock_alert/model/alert_item_entity.dart';
import 'package:vp_stock_alert/model/enum/alert_frequency.dart';
import 'package:vp_stock_alert/model/enum/price_time_period.dart';
import 'package:vp_stock_alert/pages/stock_alert/bloc/alert_config/alert_config_state_extensions.dart';
import 'package:vp_stock_alert/pages/stock_alert/bloc/stock_alert/stock_alert_cubit.dart';

import '../percent_price_state.dart';
import '../price_matching_state.dart';
import '../session_input_state.dart';

part 'alert_config_state.dart';

class AlertConfigCubit extends BaseCubit<AlertConfigState> {
  late final PercentState increasePercent;
  late final PercentState decreasePercent;
  late final RequiredState periodRequired;
  late final RequiredState priceCompareRequired;
  late final ChangePercentState priceValue;
  late final PriceMatchingState minPrice;
  late final PriceMatchingState maxPrice;
  late final RequiredState sessionTimeRequired;
  late final RequiredState sessionCompareRequired;
  late final SessionInputState sessionValue;

  AlertConfigCubit({
    required StockAlertEntity entity,
    required double? referencePrice,
  }) : super(AlertConfigState(current: entity, edit: entity)) {
    increasePercent = PercentState(
      controller: TextEditingController(
        text: entity.volatility.increasePercentText,
      ),
    )..addListener(
      text:
          (text) => emit(
            state.copyWithEditVolatility(
              increasePercentText: text,
              increasePercentDone: increasePercent.isDone,
            ),
          ),
    );
    decreasePercent = PercentState(
      controller: TextEditingController(
        text: entity.volatility.decreasePercentText,
      ),
    )..addListener(
      text:
          (text) => emit(
            state.copyWithEditVolatility(
              decreasePercentText: text,
              decreasePercentDone: decreasePercent.isDone,
            ),
          ),
    );
    periodRequired = RequiredState(
      requiredError: ErrorState(VPStockAlertLocalize.current.pleaseEnterAValue),
    )..addRequiredListener(
      (required) =>
          emit(state.copyWithEditVolatility(periodRequired: required)),
    );
    priceCompareRequired = RequiredState(requiredError: ErrorState.empty())
      ..addRequiredListener(
        (required) =>
            emit(state.copyWithEditVolatility(priceCompareRequired: required)),
      );
    priceValue = ChangePercentState(
      controller: TextEditingController(text: entity.volatility.priceValueText),
    )..addListener(
      text:
          (text) => emit(
            state.copyWithEditVolatility(
              priceValueText: text,
              priceValueDone: priceValue.isDone,
            ),
          ),
    );
    minPrice =
        PriceMatchingState(
            symbol: entity.symbol,
            controller: TextEditingController(
              text: entity.matchingPrice.minPriceText,
            ),
          )
          ..referencePrice = referencePrice
          ..addListener(
            text:
                (text) => emit(
                  state.copyWithEditMatchingPrice(
                    minPriceText: text,
                    minPriceDone: minPrice.isDone,
                  ),
                ),
          );
    maxPrice =
        PriceMatchingState(
            symbol: entity.symbol,
            controller: TextEditingController(
              text: entity.matchingPrice.maxPriceText,
            ),
          )
          ..referencePrice = referencePrice
          ..addListener(
            text:
                (text) => emit(
                  state.copyWithEditMatchingPrice(
                    maxPriceText: text,
                    maxPriceDone: maxPrice.isDone,
                  ),
                ),
          );
    sessionTimeRequired = RequiredState(
      requiredError: ErrorState(VPStockAlertLocalize.current.pleaseEnterAValue),
    )..addRequiredListener(
      (required) =>
          emit(state.copyWithEditVolume(sessionTimeRequired: required)),
    );
    sessionCompareRequired = RequiredState(requiredError: ErrorState.empty())
      ..addRequiredListener(
        (required) =>
            emit(state.copyWithEditVolume(sessionCompareRequired: required)),
      );
    sessionValue = SessionInputState(
      controller: TextEditingController(text: entity.volume.sessionValueText),
    )..addListener(
      text:
          (text) => emit(
            state.copyWithEditVolume(
              sessionValueText: text,
              sessionValueDone: sessionValue.isDone,
            ),
          ),
    );
  }

  @override
  void onChange(Change<AlertConfigState> change) {
    super.onChange(change);
    final volatility = change.nextState.edit.volatility;
    periodRequired
      ..fill(volatility.period)
      ..other([volatility.priceCompare, priceValue]);
    priceCompareRequired
      ..fill(volatility.priceCompare)
      ..other([volatility.period, priceValue]);
    priceValue.other([volatility.period, volatility.priceCompare]);
    final volume = change.nextState.edit.volume;
    sessionTimeRequired
      ..fill(volume.sessionTime)
      ..other([volume.sessionCompare, sessionValue]);
    sessionCompareRequired
      ..fill(volume.sessionCompare)
      ..other([volume.sessionTime, sessionValue]);
    sessionValue.other([volume.sessionTime, volume.sessionCompare]);
  }

  void updateState(AlertConfigState state, {bool isStatus = false}) {
    if (isStatus && state.criteriaMaxInValid) {
      showErrorMessage(
        VPStockAlertLocalize.current.utils_stockAlert_overCriteriaConfig,
      );
      return;
    }
    emit(state);
  }

  void onIndexChange(int index) =>
      emit(state.copyWith(configTab: ConfigTab.values[index]));

  void onReset() {
    emit(state.copyWith(edit: state.edit.reset));
    increasePercent.fill(5);
    decreasePercent.fill(5);
    priceValue.controller.clear();
    minPrice.controller.clear();
    maxPrice.controller.clear();
    sessionValue.controller.clear();
  }

  @override
  Future<void> close() {
    increasePercent.removeListener();
    decreasePercent.removeListener();
    periodRequired.removeListener();
    priceCompareRequired.removeListener();
    priceValue.removeListener();
    minPrice.removeListener();
    maxPrice.removeListener();
    sessionTimeRequired.removeListener();
    sessionCompareRequired.removeListener();
    sessionValue.removeListener();
    return super.close();
  }
}
