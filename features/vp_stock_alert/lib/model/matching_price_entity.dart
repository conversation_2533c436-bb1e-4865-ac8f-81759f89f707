import 'package:json_annotation/json_annotation.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_stock_alert/gen/assets.gen.dart';
import 'package:vp_stock_alert/generated/l10n.dart';
import 'alert_item_entity.dart';
import 'enum/config_tab.dart';

part 'matching_price_entity.g.dart';

@JsonSerializable()
class MatchingPriceEntity extends Equatable {
  @JsonKey(defaultValue: 0)
  final int minPriceIndex;

  @JsonKey(defaultValue: '')
  final String minPriceText;

  @JsonKey(defaultValue: 0)
  final int maxPriceIndex;

  @JsonKey(defaultValue: '')
  final String maxPriceText;

  @JsonKey(defaultValue: false)
  final bool minPriceDone;

  @JsonKey(defaultValue: false)
  final bool maxPriceDone;

  @JsonKey(fromJson: _settingFromJson, toJson: _settingToJson)
  final AlertItemEntity setting;

  const MatchingPriceEntity({
    this.minPriceIndex = 0,
    this.minPriceText = '',
    this.maxPriceIndex = 0,
    this.maxPriceText = '',
    this.minPriceDone = false,
    this.maxPriceDone = false,
    this.setting = const AlertItemEntity(category: ConfigTab.matchingPrice),
  });

  int get count => (minPriceDone ? 1 : 0) + (maxPriceDone ? 1 : 0);

  bool get hasConfig => count > 0;

  bool get validate =>
      (minPriceDone || minPriceText.isEmpty) &&
          (maxPriceDone || maxPriceText.isEmpty);

  String get name => VPStockAlertLocalize.current.utils_stockAlert_closePrice;

  String get minIcon => VPStockAlertAssets.icons.icGreaterThanEqual.path;

  String get maxIcon => VPStockAlertAssets.icons.icLessThanEqual.path;

  bool get isTurnOn => hasConfig && validate && setting.status;

  /// JSON
  factory MatchingPriceEntity.fromJson(Map<String, dynamic> json) =>
      _$MatchingPriceEntityFromJson(json);

  Map<String, dynamic> toJson() => _$MatchingPriceEntityToJson(this);

  static AlertItemEntity _settingFromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return const AlertItemEntity(category: ConfigTab.matchingPrice);
    }
    return AlertItemEntity.fromJson(json);
  }

  static Map<String, dynamic> _settingToJson(AlertItemEntity setting) =>
      setting.toJson();

  /// copyWith thủ công
  MatchingPriceEntity copyWith({
    int? minPriceIndex,
    String? minPriceText,
    int? maxPriceIndex,
    String? maxPriceText,
    bool? minPriceDone,
    bool? maxPriceDone,
    AlertItemEntity? setting,
  }) {
    return MatchingPriceEntity(
      minPriceIndex: minPriceIndex ?? this.minPriceIndex,
      minPriceText: minPriceText ?? this.minPriceText,
      maxPriceIndex: maxPriceIndex ?? this.maxPriceIndex,
      maxPriceText: maxPriceText ?? this.maxPriceText,
      minPriceDone: minPriceDone ?? this.minPriceDone,
      maxPriceDone: maxPriceDone ?? this.maxPriceDone,
      setting: setting ?? this.setting,
    );
  }

  /// Equatable
  @override
  List<Object?> get props => [
    minPriceIndex,
    minPriceText,
    maxPriceIndex,
    maxPriceText,
    minPriceDone,
    maxPriceDone,
    setting,
  ];

  @override
  bool get stringify => true;
}
