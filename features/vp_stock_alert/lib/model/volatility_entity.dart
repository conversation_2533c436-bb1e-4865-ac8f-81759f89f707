import 'package:json_annotation/json_annotation.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_stock_alert/gen/assets.gen.dart';
import 'package:vp_stock_alert/generated/l10n.dart';
import 'package:vp_stock_alert/model/enum/config_tab.dart';
import 'package:vp_stock_alert/model/enum/price_time_period.dart';
import 'package:vp_stock_alert/model/enum/value_compare.dart';
import 'alert_item_entity.dart';

part 'volatility_entity.g.dart';

@JsonSerializable()
class VolatilityEntity extends Equatable {
  @JsonKey(defaultValue: 0)
  final int increasePercentIndex;

  @JsonKey(defaultValue: '')
  final String increasePercentText;

  @JsonKey(defaultValue: false)
  final bool increasePercentDone;

  @JsonKey(defaultValue: 0)
  final int decreasePercentIndex;

  @Json<PERSON>ey(defaultValue: '')
  final String decreasePercentText;

  @Json<PERSON>ey(defaultValue: false)
  final bool decreasePercentDone;

  final PriceTimePeriod? period;

  @JsonKey(defaultValue: false)
  final bool periodRequired;

  final ValueCompare? priceCompare;

  @JsonKey(defaultValue: false)
  final bool priceCompareRequired;

  @JsonKey(defaultValue: 0)
  final int priceValueIndex;

  @JsonKey(defaultValue: '')
  final String priceValueText;

  @JsonKey(defaultValue: false)
  final bool priceValueDone;

  final AlertItemEntity setting;

  const VolatilityEntity({
    this.increasePercentIndex = 0,
    this.increasePercentText = '',
    this.increasePercentDone = false,
    this.decreasePercentIndex = 0,
    this.decreasePercentText = '',
    this.decreasePercentDone = false,
    this.period,
    this.periodRequired = false,
    this.priceCompare,
    this.priceCompareRequired = false,
    this.priceValueIndex = 0,
    this.priceValueText = '',
    this.priceValueDone = false,
    this.setting = const AlertItemEntity(category: ConfigTab.volatility),
  });

  bool get priceChangeDone =>
      period != null && priceCompare != null && priceValueDone;

  int get count =>
      (increasePercentDone ? 1 : 0) +
          (decreasePercentDone ? 1 : 0) +
          (priceChangeDone ? 1 : 0);

  String get increaseName =>
      VPStockAlertLocalize.current.utils_stockAlert_priceIncrease;

  String get increaseValue => '$increasePercentText %';

  String get decreaseName =>
      VPStockAlertLocalize.current.utils_stockAlert_priceDecrease;

  String get decreaseValue => '$decreasePercentText %';

  String get changeName => VPStockAlertLocalize.current
      .utils_stockAlert_priceChangeIn(period.toString());

  String get minIcon => VPStockAlertAssets.icons.icGreaterThanEqual.path;

  String? get changeIcon => priceCompare?.svg;

  String get changeValue => '$priceValueText %';

  bool get hasConfig => count > 0;

  bool get validate =>
      (increasePercentDone || increasePercentText.isEmpty) &&
          (decreasePercentDone || decreasePercentText.isEmpty) &&
          (priceChangeDone ||
              (period == null &&
                  priceCompare == null &&
                  priceValueText.isEmpty));

  bool get isTurnOn => hasConfig && validate && setting.status;

  VolatilityEntity get reset => VolatilityEntity(
    increasePercentIndex: increasePercentIndex,
    increasePercentText: '5',
    increasePercentDone: true,
    decreasePercentIndex: decreasePercentIndex,
    decreasePercentText: '5',
    decreasePercentDone: true,
    setting: setting.reset,
  );

  // ================= JSON =================
  factory VolatilityEntity.fromJson(Map<String, dynamic> json) =>
      _$VolatilityEntityFromJson(json);

  Map<String, dynamic> toJson() => _$VolatilityEntityToJson(this);

  // ================= COPYWITH =================
  VolatilityEntity copyWith({
    int? increasePercentIndex,
    String? increasePercentText,
    bool? increasePercentDone,
    int? decreasePercentIndex,
    String? decreasePercentText,
    bool? decreasePercentDone,
    PriceTimePeriod? period,
    bool? periodRequired,
    ValueCompare? priceCompare,
    bool? priceCompareRequired,
    int? priceValueIndex,
    String? priceValueText,
    bool? priceValueDone,
    AlertItemEntity? setting,
  }) {
    return VolatilityEntity(
      increasePercentIndex: increasePercentIndex ?? this.increasePercentIndex,
      increasePercentText: increasePercentText ?? this.increasePercentText,
      increasePercentDone: increasePercentDone ?? this.increasePercentDone,
      decreasePercentIndex: decreasePercentIndex ?? this.decreasePercentIndex,
      decreasePercentText: decreasePercentText ?? this.decreasePercentText,
      decreasePercentDone: decreasePercentDone ?? this.decreasePercentDone,
      period: period ?? this.period,
      periodRequired: periodRequired ?? this.periodRequired,
      priceCompare: priceCompare ?? this.priceCompare,
      priceCompareRequired: priceCompareRequired ?? this.priceCompareRequired,
      priceValueIndex: priceValueIndex ?? this.priceValueIndex,
      priceValueText: priceValueText ?? this.priceValueText,
      priceValueDone: priceValueDone ?? this.priceValueDone,
      setting: setting ?? this.setting,
    );
  }

  // ================= EQUATABLE =================
  @override
  List<Object?> get props => [
    increasePercentIndex,
    increasePercentText,
    increasePercentDone,
    decreasePercentIndex,
    decreasePercentText,
    decreasePercentDone,
    period,
    periodRequired,
    priceCompare,
    priceCompareRequired,
    priceValueIndex,
    priceValueText,
    priceValueDone,
    setting,
  ];
}
