import 'package:json_annotation/json_annotation.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_stock_alert/generated/l10n.dart';
import 'package:vp_stock_alert/pages/stock_alert_tab/widgets/config_item.dart';

import 'matching_price_entity.dart';
import 'mutation_entity.dart';
import 'trend_entity.dart';
import 'volatility_entity.dart';
import 'volume_entity.dart';
import 'package:collection/collection.dart';

part 'stock_alert_entity.g.dart';

@JsonSerializable()
class StockAlertEntity extends Equatable {
  final int? id;

  /// Bắt buộc như bản freezed
  final String symbol;

  @JsonKey(defaultValue: true)
  final bool status;

  @JsonKey(defaultValue: '')
  final String fromDate;

  @JsonKey(defaultValue: '')
  final String toDate;

  final VolatilityEntity volatility;

  final MatchingPriceEntity matchingPrice;

  final MutationEntity mutation;

  final TrendEntity trend;

  final VolumeEntity volume;

  const StockAlertEntity({
    this.id,
    required this.symbol,
    this.status = true,
    this.fromDate = '',
    this.toDate = '',
    this.volatility = const VolatilityEntity(),
    this.matchingPrice = const MatchingPriceEntity(),
    this.mutation = const MutationEntity(),
    this.trend = const TrendEntity(),
    this.volume = const VolumeEntity(),
  });

  String get time => '$fromDate - $toDate';

  int get criteria =>
      (matchingPrice.setting.status ? matchingPrice.count : 0) +
          (volatility.setting.status ? volatility.count : 0) +
          (mutation.setting.status ? mutation.count : 0) +
          (trend.setting.status ? trend.count : 0) +
          (volume.setting.status ? volume.count : 0);

  bool get hasConfig =>
      matchingPrice.hasConfig ||
          volatility.hasConfig ||
          mutation.hasConfig ||
          trend.hasConfig ||
          volume.hasConfig;

  bool get allStatusOff =>
      !matchingPrice.isTurnOn &&
          !volatility.isTurnOn &&
          !mutation.isTurnOn &&
          !trend.isTurnOn &&
          !volume.isTurnOn;

  bool get validate =>
      volatility.validate && matchingPrice.validate && volume.validate;

  StockAlertEntity get reset => StockAlertEntity(
    id: id,
    symbol: symbol,
    fromDate: DateTime.now().formatToDdMmYyyy(),
    toDate: DateTime.now().changeMonth(1).formatToDdMmYyyy(),
    volatility: volatility.reset,
    matchingPrice: MatchingPriceEntity(setting: matchingPrice.setting.reset),
    mutation: MutationEntity(setting: mutation.setting.reset),
    trend: TrendEntity(setting: trend.setting.reset),
    volume: VolumeEntity(setting: volume.setting.reset),
  );

  StockAlertEntity copyWith({
    int? id,
    String? symbol,
    bool? status,
    String? fromDate,
    String? toDate,
    VolatilityEntity? volatility,
    MatchingPriceEntity? matchingPrice,
    MutationEntity? mutation,
    TrendEntity? trend,
    VolumeEntity? volume,
  }) {
    return StockAlertEntity(
      id: id ?? this.id,
      symbol: symbol ?? this.symbol,
      status: status ?? this.status,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      volatility: volatility ?? this.volatility,
      matchingPrice: matchingPrice ?? this.matchingPrice,
      mutation: mutation ?? this.mutation,
      trend: trend ?? this.trend,
      volume: volume ?? this.volume,
    );
  }

  factory StockAlertEntity.fromJson(Map<String, dynamic> json) =>
      _$StockAlertEntityFromJson(json);

  Map<String, dynamic> toJson() => _$StockAlertEntityToJson(this);

  /// Equatable
  @override
  List<Object?> get props => [
    id,
    symbol,
    status,
    fromDate,
    toDate,
    volatility,
    matchingPrice,
    mutation,
    trend,
    volume,
  ];

  @override
  bool get stringify => true;
}

extension StockAlertExtensions on StockAlertEntity {
  List<ConfigItem> createItems() {
    final l10n = VPStockAlertLocalize.current;

    final items = <ConfigItem>[];
    _addMatchingPrice(items, l10n);
    _addVolatility(items, l10n);
    _addMutation(items, l10n);
    _addTrend(items, l10n);
    _addVolume(items, l10n);

    return items.sorted((a, b) {
      if (a.index != b.index) return a.index.compareTo(b.index);
      return a.subIndex.compareTo(b.subIndex);
    });
  }

  void _addMatchingPrice(List<ConfigItem> out, VPStockAlertLocalize l10n) {
    final mp = matchingPrice;
    if (!mp.setting.status) return;

    final board = l10n.utils_stockAlert_priceWarning;
    final idx = mp.setting.index;
    final title = mp.name;

    if (mp.minPriceDone) {
      out.add(
        ConfigItem(
          title: title,
          iconValue: mp.minIcon,
          value: mp.minPriceText,
          boardName: board,
          index: idx,
          subIndex: mp.minPriceIndex,
        ),
      );
    }
    if (mp.maxPriceDone) {
      out.add(
        ConfigItem(
          title: title,
          iconValue: mp.maxIcon,
          value: mp.maxPriceText,
          boardName: board,
          index: idx,
          subIndex: mp.maxPriceIndex,
        ),
      );
    }
  }

  void _addVolatility(List<ConfigItem> out, VPStockAlertLocalize l10n) {
    final v = volatility;
    if (!v.setting.status) return;

    final board = l10n.utils_stockAlert_volatilityWarning;
    final idx = v.setting.index;

    if (v.decreasePercentDone) {
      out.add(
        ConfigItem(
          title: v.decreaseName,
          iconValue: v.minIcon,
          value: v.decreaseValue,
          boardName: board,
          index: idx,
          subIndex: v.decreasePercentIndex,
        ),
      );
    }
    if (v.increasePercentDone) {
      out.add(
        ConfigItem(
          title: v.increaseName,
          iconValue: v.minIcon,
          value: v.increaseValue,
          boardName: board,
          index: idx,
          subIndex: v.increasePercentIndex,
        ),
      );
    }
    if (v.priceChangeDone) {
      out.add(
        ConfigItem(
          title: v.changeName,
          iconValue: v.changeIcon,
          value: v.changeValue,
          boardName: board,
          index: idx,
          subIndex: v.priceValueIndex,
        ),
      );
    }
  }

  void _addMutation(List<ConfigItem> out, VPStockAlertLocalize l10n) {
    final m = mutation;
    if (!m.setting.status) return;

    final board = l10n.utils_stockAlert_mutationWarning;
    final idx = m.setting.index;

    if (m.bottomOut != null) {
      out.add(
        ConfigItem(
          title: m.bottomOutName,
          value: m.bottomOutValue,
          boardName: board,
          index: idx,
          subIndex: m.bottomOutIndex,
        ),
      );
    }
    if (m.overTop != null) {
      out.add(
        ConfigItem(
          title: m.overTopName,
          value: m.overTopValue,
          boardName: board,
          index: idx,
          subIndex: m.overTopIndex,
        ),
      );
    }
  }

  void _addTrend(List<ConfigItem> out, VPStockAlertLocalize l10n) {
    final t = trend;
    if (!t.setting.status) return;

    final board = l10n.utils_stockAlert_trendAlert;
    final idx = t.setting.index;

    if (t.above != null) {
      out.add(
        ConfigItem(
          title: t.aboveName,
          value: t.aboveValue,
          boardName: board,
          index: idx,
          subIndex: t.aboveIndex,
        ),
      );
    }
    if (t.below != null) {
      out.add(
        ConfigItem(
          title: t.belowName,
          value: t.belowValue,
          boardName: board,
          index: idx,
          subIndex: t.belowIndex,
        ),
      );
    }
  }

  void _addVolume(List<ConfigItem> out, VPStockAlertLocalize l10n) {
    final vol = volume;
    if (!vol.setting.status || vol.sessionTime == null) return;

    out.add(
      ConfigItem(
        title: vol.name,
        iconValue: vol.icon,
        value: vol.value,
        boardName: l10n.utils_stockAlert_volumeWarning,
        index: vol.setting.index,
        subIndex: vol.sessionValueIndex,
      ),
    );
  }
}
