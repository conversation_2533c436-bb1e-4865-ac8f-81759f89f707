import 'package:json_annotation/json_annotation.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_stock_alert/generated/l10n.dart';
import 'package:vp_stock_alert/model/enum/average_line.dart';
import 'package:vp_stock_alert/model/enum/config_tab.dart';

import 'alert_item_entity.dart';

part 'trend_entity.g.dart';

@JsonSerializable()
class TrendEntity extends Equatable {
  @JsonKey(defaultValue: 0)
  final int aboveIndex;

  final AverageLine? above;

  @JsonKey(defaultValue: 0)
  final int belowIndex;

  final AverageLine? below;

  @JsonKey(fromJson: _settingFromJson, toJson: _settingToJson)
  final AlertItemEntity setting;

  const TrendEntity({
    this.aboveIndex = 0,
    this.above,
    this.belowIndex = 0,
    this.below,
    this.setting = const AlertItemEntity(category: ConfigTab.trend),
  });

  int get count => (above != null ? 1 : 0) + (below != null ? 1 : 0);

  bool get hasConfig => count > 0;

  String get aboveName =>
      VPStockAlertLocalize.current
          .utils_stockAlert_priceCrossesAboveTheAverageLine;

  String get aboveValue => above?.toString() ?? '';

  String get belowName =>
      VPStockAlertLocalize.current
          .utils_stockAlert_priceCutsBelowTheAverageLine;

  String get belowValue => below?.toString() ?? '';

  bool get isTurnOn => hasConfig && setting.status;

  TrendEntity get reset => TrendEntity(setting: setting.reset);

  /// JSON
  factory TrendEntity.fromJson(Map<String, dynamic> json) =>
      _$TrendEntityFromJson(json);

  Map<String, dynamic> toJson() => _$TrendEntityToJson(this);

  static AlertItemEntity _settingFromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return const AlertItemEntity(category: ConfigTab.trend);
    }
    return AlertItemEntity.fromJson(json);
  }

  static Map<String, dynamic> _settingToJson(AlertItemEntity v) => v.toJson();

  /// copyWith thủ công
  TrendEntity copyWith({
    int? aboveIndex,
    AverageLine? above,
    int? belowIndex,
    AverageLine? below,
    AlertItemEntity? setting,
  }) {
    return TrendEntity(
      aboveIndex: aboveIndex ?? this.aboveIndex,
      above: above ?? this.above,
      belowIndex: belowIndex ?? this.belowIndex,
      below: below ?? this.below,
      setting: setting ?? this.setting,
    );
  }

  /// Equatable
  @override
  List<Object?> get props => [
    aboveIndex,
    above,
    belowIndex,
    below,
    setting,
  ];

  @override
  bool get stringify => true;
}
