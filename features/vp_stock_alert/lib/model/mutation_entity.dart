import 'package:json_annotation/json_annotation.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_stock_alert/generated/l10n.dart';
import 'package:vp_stock_alert/model/enum/config_tab.dart';
import 'package:vp_stock_alert/model/enum/price_time_period.dart';
import 'alert_item_entity.dart';

part 'mutation_entity.g.dart';

@JsonSerializable()
class MutationEntity extends Equatable {
  @Json<PERSON>ey(defaultValue: 0)
  final int overTopIndex;

  final PriceTimePeriod? overTop;

  @JsonKey(defaultValue: 0)
  final int bottomOutIndex;

  final PriceTimePeriod? bottomOut;

  @JsonKey(fromJson: _settingFromJson, toJson: _settingToJson)
  final AlertItemEntity setting;

  const MutationEntity({
    this.overTopIndex = 0,
    this.overTop,
    this.bottomOutIndex = 0,
    this.bottomOut,
    this.setting = const AlertItemEntity(category: ConfigTab.mutation),
  });

  int get count => (overTop != null ? 1 : 0) + (bottomOut != null ? 1 : 0);

  bool get hasConfig => count > 0;

  String get overTopName =>
      VPStockAlertLocalize.current.utils_stockAlert_pricesAreOverTheTop;

  String get overTopValue => overTop?.toString() ?? '';

  String get bottomOutName =>
      VPStockAlertLocalize.current.utils_stockAlert_priceBottomedOut;

  String get bottomOutValue => bottomOut?.toString() ?? '';

  bool get isTurnOn => hasConfig && setting.status;

  MutationEntity get reset => MutationEntity(setting: setting.reset);

  /// JSON
  factory MutationEntity.fromJson(Map<String, dynamic> json) =>
      _$MutationEntityFromJson(json);

  Map<String, dynamic> toJson() => _$MutationEntityToJson(this);

  static AlertItemEntity _settingFromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return const AlertItemEntity(category: ConfigTab.mutation);
    }
    return AlertItemEntity.fromJson(json);
  }

  static Map<String, dynamic> _settingToJson(AlertItemEntity v) => v.toJson();

  /// copyWith thủ công
  MutationEntity copyWith({
    int? overTopIndex,
    PriceTimePeriod? overTop,
    int? bottomOutIndex,
    PriceTimePeriod? bottomOut,
    AlertItemEntity? setting,
  }) {
    return MutationEntity(
      overTopIndex: overTopIndex ?? this.overTopIndex,
      overTop: overTop ?? this.overTop,
      bottomOutIndex: bottomOutIndex ?? this.bottomOutIndex,
      bottomOut: bottomOut ?? this.bottomOut,
      setting: setting ?? this.setting,
    );
  }

  /// Equatable
  @override
  List<Object?> get props => [
    overTopIndex,
    overTop,
    bottomOutIndex,
    bottomOut,
    setting,
  ];

  @override
  bool get stringify => true;
}
