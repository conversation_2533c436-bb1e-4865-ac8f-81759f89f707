import 'package:json_annotation/json_annotation.dart';
import 'package:vp_stock_alert/model/enum/alert_frequency.dart';
import 'package:vp_stock_alert/model/enum/config_tab.dart';
import 'package:vp_common/vp_common.dart';

part 'alert_item_entity.g.dart';

@JsonSerializable()
class AlertItemEntity extends Equatable {
  final int? id;
  final ConfigTab category;

  @JsonKey(defaultValue: true)
  final bool status;

  @<PERSON>son<PERSON>ey(defaultValue: '')
  final String fromDate;

  @Json<PERSON>ey(defaultValue: '')
  final String toDate;

  @JsonKey(defaultValue: AlertFrequency.onePerDay)
  final AlertFrequency frequency;

  @Json<PERSON>ey(defaultValue: 0)
  final int index;

  const AlertItemEntity({
    this.id,
    required this.category,
    this.status = true,
    this.fromDate = '',
    this.toDate = '',
    this.frequency = AlertFrequency.onePerDay,
    this.index = 0,
  });

  DateTime get from => fromDate.dateTimeParse('dd/MM/yyyy') ?? DateTime.now();

  String get fromText => from.formatToDdMmYyyy();

  DateTime get to =>
      toDate.dateTimeParse('dd/MM/yyyy') ?? DateTime.now().changeMonth(1);

  String get toText => to.formatToDdMmYyyy();

  AlertItemEntity get reset => AlertItemEntity(
    id: id,
    category: category,
    fromDate: DateTime.now().formatToDdMmYyyy(),
    toDate: DateTime.now().changeMonth(1).formatToDdMmYyyy(),
  );

  /// copyWith
  AlertItemEntity copyWith({
    int? id,
    ConfigTab? category,
    bool? status,
    String? fromDate,
    String? toDate,
    AlertFrequency? frequency,
    int? index,
  }) {
    return AlertItemEntity(
      id: id ?? this.id,
      category: category ?? this.category,
      status: status ?? this.status,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      frequency: frequency ?? this.frequency,
      index: index ?? this.index,
    );
  }

  /// JSON
  factory AlertItemEntity.fromJson(Map<String, dynamic> json) =>
      _$AlertItemEntityFromJson(json);

  Map<String, dynamic> toJson() => _$AlertItemEntityToJson(this);

  /// Equatable
  @override
  List<Object?> get props => [
    id,
    category,
    status,
    fromDate,
    toDate,
    frequency,
    index,
  ];

  @override
  bool get stringify => true;
}
