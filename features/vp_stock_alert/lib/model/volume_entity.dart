import 'package:json_annotation/json_annotation.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_stock_alert/model/enum/config_tab.dart';
import 'package:vp_stock_alert/model/enum/session_time_period.dart';
import 'package:vp_stock_alert/model/enum/value_compare.dart';
import 'alert_item_entity.dart';
import 'package:vp_stock_alert/generated/l10n.dart';

part 'volume_entity.g.dart';

@JsonSerializable()
class VolumeEntity extends Equatable {
  final SessionTimePeriod? sessionTime;

  @JsonKey(defaultValue: false)
  final bool sessionTimeRequired;

  final ValueCompare? sessionCompare;

  @Json<PERSON>ey(defaultValue: false)
  final bool sessionCompareRequired;

  @JsonKey(defaultValue: 0)
  final int sessionValueIndex;

  @JsonKey(defaultValue: '')
  final String sessionValueText;

  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: false)
  final bool sessionValueDone;

  @<PERSON><PERSON><PERSON><PERSON>(fromJson: _settingFromJson, toJson: _settingToJson)
  final AlertItemEntity setting;

  const VolumeEntity({
    this.sessionTime,
    this.sessionTimeRequired = false,
    this.sessionCompare,
    this.sessionCompareRequired = false,
    this.sessionValueIndex = 0,
    this.sessionValueText = '',
    this.sessionValueDone = false,
    this.setting = const AlertItemEntity(category: ConfigTab.volume),
  });

  bool get valueDone =>
      sessionTime != null && sessionCompare != null && sessionValueDone;

  int get count => valueDone ? 1 : 0;

  bool get hasConfig => count > 0;

  bool get validate =>
      valueDone ||
          (sessionTime == null &&
              sessionCompare == null &&
              sessionValueText.isEmpty);

  String? get name => sessionTime != null
      ? VPStockAlertLocalize.current
      .utils_stockAlert_tradingVolumeComparedInPastSessions(
    sessionTime!.toString(),
  )
      : null;

  String? get icon => sessionCompare?.svg;

  String get value =>
      '$sessionValueText ${VPStockAlertLocalize.current.utils_stockAlert_bout}';

  bool get isTurnOn => hasConfig && validate && setting.status;

  /// JSON
  factory VolumeEntity.fromJson(Map<String, dynamic> json) =>
      _$VolumeEntityFromJson(json);

  Map<String, dynamic> toJson() => _$VolumeEntityToJson(this);

  static AlertItemEntity _settingFromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return const AlertItemEntity(category: ConfigTab.volume);
    }
    return AlertItemEntity.fromJson(json);
  }

  static Map<String, dynamic> _settingToJson(AlertItemEntity v) => v.toJson();

  /// copyWith thủ công
  VolumeEntity copyWith({
    SessionTimePeriod? sessionTime,
    bool? sessionTimeRequired,
    ValueCompare? sessionCompare,
    bool? sessionCompareRequired,
    int? sessionValueIndex,
    String? sessionValueText,
    bool? sessionValueDone,
    AlertItemEntity? setting,
  }) {
    return VolumeEntity(
      sessionTime: sessionTime ?? this.sessionTime,
      sessionTimeRequired: sessionTimeRequired ?? this.sessionTimeRequired,
      sessionCompare: sessionCompare ?? this.sessionCompare,
      sessionCompareRequired:
      sessionCompareRequired ?? this.sessionCompareRequired,
      sessionValueIndex: sessionValueIndex ?? this.sessionValueIndex,
      sessionValueText: sessionValueText ?? this.sessionValueText,
      sessionValueDone: sessionValueDone ?? this.sessionValueDone,
      setting: setting ?? this.setting,
    );
  }

  /// Equatable
  @override
  List<Object?> get props => [
    sessionTime,
    sessionTimeRequired,
    sessionCompare,
    sessionCompareRequired,
    sessionValueIndex,
    sessionValueText,
    sessionValueDone,
    setting,
  ];

  @override
  bool get stringify => true;
}
