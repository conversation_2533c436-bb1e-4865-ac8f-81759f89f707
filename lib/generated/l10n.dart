// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class VPNeoLocalize {
  VPNeoLocalize();

  static VPNeoLocalize? _current;

  static VPNeoLocalize get current {
    assert(
      _current != null,
      'No instance of VPNeoLocalize was loaded. Try to initialize the VPNeoLocalize delegate before accessing VPNeoLocalize.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<VPNeoLocalize> load(Locale locale) {
    final name =
        (locale.countryCode?.isEmpty ?? false)
            ? locale.languageCode
            : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = VPNeoLocalize();
      VPNeoLocalize._current = instance;

      return instance;
    });
  }

  static VPNeoLocalize of(BuildContext context) {
    final instance = VPNeoLocalize.maybeOf(context);
    assert(
      instance != null,
      'No instance of VPNeoLocalize present in the widget tree. Did you add VPNeoLocalize.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static VPNeoLocalize? maybeOf(BuildContext context) {
    return Localizations.of<VPNeoLocalize>(context, VPNeoLocalize);
  }

  /// `Wealth in hands`
  String get minimalistInvesting {
    return Intl.message(
      'Wealth in hands',
      name: 'minimalistInvesting',
      desc: '',
      args: [],
    );
  }

  /// `Chào mừng bạn đến với`
  String get welcome_to {
    return Intl.message(
      'Chào mừng bạn đến với',
      name: 'welcome_to',
      desc: '',
      args: [],
    );
  }

  /// `Chứng khoán VPBank`
  String get vpbs {
    return Intl.message('Chứng khoán VPBank', name: 'vpbs', desc: '', args: []);
  }

  /// `Đăng nhập`
  String get splash_login {
    return Intl.message('Đăng nhập', name: 'splash_login', desc: '', args: []);
  }

  /// `Mở tài khoản`
  String get splash_register {
    return Intl.message(
      'Mở tài khoản',
      name: 'splash_register',
      desc: '',
      args: [],
    );
  }

  /// `Tài sản ròng (NAV)`
  String get home_property_nav {
    return Intl.message(
      'Tài sản ròng (NAV)',
      name: 'home_property_nav',
      desc: '',
      args: [],
    );
  }

  /// `Tỉ lệ ký quỹ thực tế`
  String get home_actual_margin_ratio {
    return Intl.message(
      'Tỉ lệ ký quỹ thực tế',
      name: 'home_actual_margin_ratio',
      desc: '',
      args: [],
    );
  }

  /// `Tính năng ưa thích`
  String get home_favorist_features {
    return Intl.message(
      'Tính năng ưa thích',
      name: 'home_favorist_features',
      desc: '',
      args: [],
    );
  }

  /// `Tùy chỉnh`
  String get home_edit {
    return Intl.message('Tùy chỉnh', name: 'home_edit', desc: '', args: []);
  }

  /// `Cổ phiếu`
  String get home_sec {
    return Intl.message('Cổ phiếu', name: 'home_sec', desc: '', args: []);
  }

  /// `Phái sinh`
  String get home_derivatives {
    return Intl.message(
      'Phái sinh',
      name: 'home_derivatives',
      desc: '',
      args: [],
    );
  }

  /// `Đầu tư`
  String get home_invest {
    return Intl.message('Đầu tư', name: 'home_invest', desc: '', args: []);
  }

  /// `Tài sản`
  String get home_asset {
    return Intl.message('Tài sản', name: 'home_asset', desc: '', args: []);
  }

  /// `Bổ sung ngay`
  String get home_add_now {
    return Intl.message(
      'Bổ sung ngay',
      name: 'home_add_now',
      desc: '',
      args: [],
    );
  }

  /// `Tài khoản của bạn chưa hoàn thiện do thiếu ảnh Chữ ký.\nMột số tính năng sẽ bị giới hạn`
  String get home_missing_signature {
    return Intl.message(
      'Tài khoản của bạn chưa hoàn thiện do thiếu ảnh Chữ ký.\nMột số tính năng sẽ bị giới hạn',
      name: 'home_missing_signature',
      desc: '',
      args: [],
    );
  }

  /// `Thông tin khách hàng`
  String get home_info_customer {
    return Intl.message(
      'Thông tin khách hàng',
      name: 'home_info_customer',
      desc: '',
      args: [],
    );
  }

  /// `Thông tin khách hàng`
  String get home_desc_customer {
    return Intl.message(
      'Thông tin khách hàng',
      name: 'home_desc_customer',
      desc: '',
      args: [],
    );
  }

  /// `Đăng xuất`
  String get home_logout {
    return Intl.message('Đăng xuất', name: 'home_logout', desc: '', args: []);
  }

  /// `Bạn có chắc chắn muốn đăng xuất khỏi tài khoản?`
  String get home_confirm_logout {
    return Intl.message(
      'Bạn có chắc chắn muốn đăng xuất khỏi tài khoản?',
      name: 'home_confirm_logout',
      desc: '',
      args: [],
    );
  }

  /// `Danh sách tính năng`
  String get home_features_list {
    return Intl.message(
      'Danh sách tính năng',
      name: 'home_features_list',
      desc: '',
      args: [],
    );
  }

  /// `Tìm kiếm tính năng`
  String get home_find_features {
    return Intl.message(
      'Tìm kiếm tính năng',
      name: 'home_find_features',
      desc: '',
      args: [],
    );
  }

  /// `Bạn cần đặt ít nhất 4 tính năng`
  String get home_min_features_failed {
    return Intl.message(
      'Bạn cần đặt ít nhất 4 tính năng',
      name: 'home_min_features_failed',
      desc: '',
      args: [],
    );
  }

  /// `Bạn chỉ được chọn tối đa 8 tính năng`
  String get home_max_features_failed {
    return Intl.message(
      'Bạn chỉ được chọn tối đa 8 tính năng',
      name: 'home_max_features_failed',
      desc: '',
      args: [],
    );
  }

  /// `Tiện ích cổ phiếu`
  String get home_stock_utility {
    return Intl.message(
      'Tiện ích cổ phiếu',
      name: 'home_stock_utility',
      desc: '',
      args: [],
    );
  }

  /// `Đầu tư`
  String get home_stock_invest {
    return Intl.message(
      'Đầu tư',
      name: 'home_stock_invest',
      desc: '',
      args: [],
    );
  }

  /// `Phái sinh`
  String get home_stock_derivatives {
    return Intl.message(
      'Phái sinh',
      name: 'home_stock_derivatives',
      desc: '',
      args: [],
    );
  }

  /// `Tiện ích khác`
  String get home_stock_other {
    return Intl.message(
      'Tiện ích khác',
      name: 'home_stock_other',
      desc: '',
      args: [],
    );
  }

  /// `Bảo mật`
  String get home_security {
    return Intl.message('Bảo mật', name: 'home_security', desc: '', args: []);
  }

  /// `Hỗ trợ`
  String get home_support {
    return Intl.message('Hỗ trợ', name: 'home_support', desc: '', args: []);
  }

  /// `Cài đặt`
  String get home_setup {
    return Intl.message('Cài đặt', name: 'home_setup', desc: '', args: []);
  }

  /// `Cập nhật thành công.`
  String get home_update_success {
    return Intl.message(
      'Cập nhật thành công.',
      name: 'home_update_success',
      desc: '',
      args: [],
    );
  }

  /// `Lấy mã Smart OTP`
  String get home_get_smart_oTP {
    return Intl.message(
      'Lấy mã Smart OTP',
      name: 'home_get_smart_oTP',
      desc: '',
      args: [],
    );
  }

  /// `Lấy mã Smart OTP`
  String get home_getSmartOTP {
    return Intl.message(
      'Lấy mã Smart OTP',
      name: 'home_getSmartOTP',
      desc: '',
      args: [],
    );
  }

  /// `VPBank Securities v`
  String get home_version {
    return Intl.message(
      'VPBank Securities v',
      name: 'home_version',
      desc: '',
      args: [],
    );
  }

  /// `Cập nhật ảnh đại diện thành công`
  String get home_updateAvtSuccess {
    return Intl.message(
      'Cập nhật ảnh đại diện thành công',
      name: 'home_updateAvtSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Chọn ảnh từ thiết bị`
  String get home_takePicktureFromGallery {
    return Intl.message(
      'Chọn ảnh từ thiết bị',
      name: 'home_takePicktureFromGallery',
      desc: '',
      args: [],
    );
  }

  /// `Chụp ảnh`
  String get home_takePickture {
    return Intl.message(
      'Chụp ảnh',
      name: 'home_takePickture',
      desc: '',
      args: [],
    );
  }

  /// `Vào cài đặt`
  String get home_goToSetting {
    return Intl.message(
      'Vào cài đặt',
      name: 'home_goToSetting',
      desc: '',
      args: [],
    );
  }

  /// `Bật quyền truy cập`
  String get home_openCamera {
    return Intl.message(
      'Bật quyền truy cập',
      name: 'home_openCamera',
      desc: '',
      args: [],
    );
  }

  /// `VPBank Securities cần cấp quyền camera và thư viện ảnh để có thể truy cập và cập nhật ảnh đại diện của Quý khách`
  String get home_contentPermissionDialog {
    return Intl.message(
      'VPBank Securities cần cấp quyền camera và thư viện ảnh để có thể truy cập và cập nhật ảnh đại diện của Quý khách',
      name: 'home_contentPermissionDialog',
      desc: '',
      args: [],
    );
  }

  /// `Kết nối đối tác`
  String get home_partnerConnection {
    return Intl.message(
      'Kết nối đối tác',
      name: 'home_partnerConnection',
      desc: '',
      args: [],
    );
  }

  /// `Khách hàng thân thiết`
  String get home_membershipOffer {
    return Intl.message(
      'Khách hàng thân thiết',
      name: 'home_membershipOffer',
      desc: '',
      args: [],
    );
  }

  /// `Hạng, điểm thưởng, ưu đãi`
  String get home_descMember {
    return Intl.message(
      'Hạng, điểm thưởng, ưu đãi',
      name: 'home_descMember',
      desc: '',
      args: [],
    );
  }

  /// `Giới thiệu bạn mới`
  String get home_introduceFriend {
    return Intl.message(
      'Giới thiệu bạn mới',
      name: 'home_introduceFriend',
      desc: '',
      args: [],
    );
  }

  /// `Giới thiệu bạn bè & khách hàng`
  String get home_introduceFriendAndCustomer {
    return Intl.message(
      'Giới thiệu bạn bè & khách hàng',
      name: 'home_introduceFriendAndCustomer',
      desc: '',
      args: [],
    );
  }

  /// `Hệ thống đang bảo trì`
  String get systemMaintenanceTitle {
    return Intl.message(
      'Hệ thống đang bảo trì',
      name: 'systemMaintenanceTitle',
      desc: '',
      args: [],
    );
  }

  /// `Hoạt động bảo trì có thể mất vài phút hoặc vài tiếng, chúng tôi xin lỗi về sự bất tiện này.`
  String get systemMaintenanceContent {
    return Intl.message(
      'Hoạt động bảo trì có thể mất vài phút hoặc vài tiếng, chúng tôi xin lỗi về sự bất tiện này.',
      name: 'systemMaintenanceContent',
      desc: '',
      args: [],
    );
  }

  /// `Đăng ký tài khoản`
  String get register {
    return Intl.message(
      'Đăng ký tài khoản',
      name: 'register',
      desc: '',
      args: [],
    );
  }

  /// `Chụp ảnh hồ sơ`
  String get captureProfile {
    return Intl.message(
      'Chụp ảnh hồ sơ',
      name: 'captureProfile',
      desc: '',
      args: [],
    );
  }

  /// `Chữ ký`
  String get signature {
    return Intl.message('Chữ ký', name: 'signature', desc: '', args: []);
  }

  /// `Tiếp theo`
  String get next {
    return Intl.message('Tiếp theo', name: 'next', desc: '', args: []);
  }

  /// `Chụp ảnh chữ ký`
  String get captureSignature {
    return Intl.message(
      'Chụp ảnh chữ ký',
      name: 'captureSignature',
      desc: '',
      args: [],
    );
  }

  /// `Đã cập nhật chữ ký`
  String get signatureSuccess {
    return Intl.message(
      'Đã cập nhật chữ ký',
      name: 'signatureSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Vui lòng chụp ảnh chữ ký của bạn`
  String get please_take_photo {
    return Intl.message(
      'Vui lòng chụp ảnh chữ ký của bạn',
      name: 'please_take_photo',
      desc: '',
      args: [],
    );
  }

  /// `Tải xuống thành công`
  String get home_downloadSuccess {
    return Intl.message(
      'Tải xuống thành công',
      name: 'home_downloadSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Tải xuống thất bại`
  String get home_downloadFail {
    return Intl.message(
      'Tải xuống thất bại',
      name: 'home_downloadFail',
      desc: '',
      args: [],
    );
  }

  /// `Điều khoản và điều kiện chung`
  String get home_generalTermsAndConditions {
    return Intl.message(
      'Điều khoản và điều kiện chung',
      name: 'home_generalTermsAndConditions',
      desc: '',
      args: [],
    );
  }

  /// `Thông tin điều khoản và điều kiện chung theo Nghị định 13/2023/NĐ-CP`
  String get home_infoGeneralTermsAndConditions {
    return Intl.message(
      'Thông tin điều khoản và điều kiện chung theo Nghị định 13/2023/NĐ-CP',
      name: 'home_infoGeneralTermsAndConditions',
      desc: '',
      args: [],
    );
  }

  /// `Đóng`
  String get home_close {
    return Intl.message('Đóng', name: 'home_close', desc: '', args: []);
  }

  /// `Cập nhật thông tin cá nhân`
  String get home_updatePersonalInformation {
    return Intl.message(
      'Cập nhật thông tin cá nhân',
      name: 'home_updatePersonalInformation',
      desc: '',
      args: [],
    );
  }

  /// `VPBankS thông báo Quý khách cần thực hiện cập nhật thông tin căn cước công dân theo quy định tại Công văn số: 4501/UBCK-CNTT`
  String get home_updateEkyc {
    return Intl.message(
      'VPBankS thông báo Quý khách cần thực hiện cập nhật thông tin căn cước công dân theo quy định tại Công văn số: 4501/UBCK-CNTT',
      name: 'home_updateEkyc',
      desc: '',
      args: [],
    );
  }

  /// `Cập nhật`
  String get home_update {
    return Intl.message('Cập nhật', name: 'home_update', desc: '', args: []);
  }

  /// `Để bảo vệ dữ liệu cá nhân của Khách Hàng theo  Nghị định 13/2023/NĐ-CP, Quý Khách vui lòng xác nhận đồng ý với `
  String get home_updateNd13 {
    return Intl.message(
      'Để bảo vệ dữ liệu cá nhân của Khách Hàng theo  Nghị định 13/2023/NĐ-CP, Quý Khách vui lòng xác nhận đồng ý với ',
      name: 'home_updateNd13',
      desc: '',
      args: [],
    );
  }

  /// `Điều Khoản Và Điều Kiện Chung về bảo vệ và xử lý dữ liệu cá nhân `
  String get home_generalTermsAndConditionsNd13 {
    return Intl.message(
      'Điều Khoản Và Điều Kiện Chung về bảo vệ và xử lý dữ liệu cá nhân ',
      name: 'home_generalTermsAndConditionsNd13',
      desc: '',
      args: [],
    );
  }

  /// `bằng việc tích chọn dưới đây.`
  String get home_byChosenBelow {
    return Intl.message(
      'bằng việc tích chọn dưới đây.',
      name: 'home_byChosenBelow',
      desc: '',
      args: [],
    );
  }

  /// `To protect your personal data in accordance with Decree No. 13/2023/ND-CP, please kindly confirm your acceptance with the `
  String get home_updateNd13_en {
    return Intl.message(
      'To protect your personal data in accordance with Decree No. 13/2023/ND-CP, please kindly confirm your acceptance with the ',
      name: 'home_updateNd13_en',
      desc: '',
      args: [],
    );
  }

  /// `General Terms and Conditions on personal data protection and processing `
  String get home_generalTermsAndConditionsNd13_en {
    return Intl.message(
      'General Terms and Conditions on personal data protection and processing ',
      name: 'home_generalTermsAndConditionsNd13_en',
      desc: '',
      args: [],
    );
  }

  /// `by clicking the below box.`
  String get home_byChosenBelow_en {
    return Intl.message(
      'by clicking the below box.',
      name: 'home_byChosenBelow_en',
      desc: '',
      args: [],
    );
  }

  /// `Tôi đồng ý/ I agree`
  String get home_agree {
    return Intl.message(
      'Tôi đồng ý/ I agree',
      name: 'home_agree',
      desc: '',
      args: [],
    );
  }

  /// `Hoàn thành`
  String get home_complete {
    return Intl.message(
      'Hoàn thành',
      name: 'home_complete',
      desc: '',
      args: [],
    );
  }

  /// `Xác nhận`
  String get home_contractOTPConfirmButton {
    return Intl.message(
      'Xác nhận',
      name: 'home_contractOTPConfirmButton',
      desc: '',
      args: [],
    );
  }

  /// `Cập nhật điều khoản hợp đồng`
  String get home_contractOTPDialogTitle {
    return Intl.message(
      'Cập nhật điều khoản hợp đồng',
      name: 'home_contractOTPDialogTitle',
      desc: '',
      args: [],
    );
  }

  /// `<div style="text-align: center">VPBankS thông báo cập nhật nội dung <a href="https://33ebaeb4-b166-4507-af2e-e29a08919483.usrfiles.com/ugd/33ebae_ced5323105734e4081a128d43c078551.pdf" style="color:#48D597;text-decoration:none">điều khoản và điều kiện</a> của Hợp đồng mở tài khoản chứng khoán (T&C).<br>Bằng việc nhấn “Xác nhận” bạn đã đọc và đồng ý với điều khoản và điều kiện mới.</div>`
  String get home_contractOTPDialogContent {
    return Intl.message(
      '<div style="text-align: center">VPBankS thông báo cập nhật nội dung <a href="https://33ebaeb4-b166-4507-af2e-e29a08919483.usrfiles.com/ugd/33ebae_ced5323105734e4081a128d43c078551.pdf" style="color:#48D597;text-decoration:none">điều khoản và điều kiện</a> của Hợp đồng mở tài khoản chứng khoán (T&C).<br>Bằng việc nhấn “Xác nhận” bạn đã đọc và đồng ý với điều khoản và điều kiện mới.</div>',
      name: 'home_contractOTPDialogContent',
      desc: '',
      args: [],
    );
  }

  /// `Xác nhận thành công`
  String get home_confirmedSuccessfully {
    return Intl.message(
      'Xác nhận thành công',
      name: 'home_confirmedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Quý khách cần xác nhận các lệnh đặt cổ phiếu của nhân viên chăm sóc`
  String get orderConfirmWidgetDescription {
    return Intl.message(
      'Quý khách cần xác nhận các lệnh đặt cổ phiếu của nhân viên chăm sóc',
      name: 'orderConfirmWidgetDescription',
      desc: '',
      args: [],
    );
  }

  /// `Xác nhận ngay`
  String get orderConfirmNow {
    return Intl.message(
      'Xác nhận ngay',
      name: 'orderConfirmNow',
      desc: '',
      args: [],
    );
  }

  /// `Giao dịch chưa hoàn tất`
  String get orderConfirmWidgetTitle {
    return Intl.message(
      'Giao dịch chưa hoàn tất',
      name: 'orderConfirmWidgetTitle',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<VPNeoLocalize> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'vi'),
      Locale.fromSubtags(languageCode: 'en'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<VPNeoLocalize> load(Locale locale) => VPNeoLocalize.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
